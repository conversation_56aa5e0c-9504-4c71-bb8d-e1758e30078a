import React from "react";
import { useParams } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import FlashcardReviewSection from "@/components/flashcards/FlashcardReviewSection";
import { useQuery } from "@tanstack/react-query";
// Removed direct Supabase import - using backend API endpoints

const FlashcardReview: React.FC = () => {
  const params = useParams<{ deckId: string }>();
  const deckId = params?.deckId;

  const { data: deck } = useQuery({
    queryKey: [`/api/flashcard-sets/${deckId}`],
    queryFn: async () => {
      if (!deckId) return null;

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await fetch(`/api/flashcard-sets/${deckId}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch flashcard set: ${response.statusText}`);
      }

      return response.json();
    },
    enabled: !!deckId
  });

  return (
    <AppLayout title={deck ? `Reviewing: ${deck.name}` : "Flashcard Review"}>
      <FlashcardReviewSection />
    </AppLayout>
  );
};

export default FlashcardReview;
