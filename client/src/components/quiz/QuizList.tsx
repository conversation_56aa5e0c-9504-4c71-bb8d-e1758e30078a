import React from "react";
import { useQuery, UseQueryOptions } from "@tanstack/react-query";
import {
  getAllQuizzesAPI,
  deleteQuizAPI,
} from "@/lib/api";
import { Quiz, GetAllQuizzesApiResponse } from "@shared/types/quiz";
import { Button } from "@/components/ui/button";
import Spinner from "@/components/ui/Spinner";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";

interface QuizListProps {
  onSelectQuiz: (quizId: string, quizName: string) => void;
  onPlayQuiz: (quizId: string) => void;
}

export const QuizList: React.FC<QuizListProps> = ({
  onSelectQuiz,
  onPlayQuiz,
}) => {
  const { toast } = useToast();

  // Define query options with proper TypeScript typing
  const queryOptions: UseQueryOptions<
    GetAllQuizzesApiResponse,
    Error,
    GetAllQuizzesApiResponse,
    string[]
  > = {
    queryKey: ["quizzes"],
    queryFn: getAllQuizzesAPI,
    retry: false,
  };

  const {
    data: quizzesResponse,
    isLoading,
    error,
    isError,
  } = useQuery(queryOptions);

  const handleDeleteQuiz = async (quizId: string) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this quiz? All questions in this quiz will also be deleted."
      )
    )
      return;

    try {
      await deleteQuizAPI(quizId);
      toast({
        title: "Quiz Deleted",
        description: "The quiz has been successfully deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ["quizzes"] });
    } catch (err: any) {
      toast({
        title: "Error Deleting Quiz",
        description: err.message || "An unexpected error occurred.",
        variant: "destructive",
      });
      console.error("Error deleting quiz:", err);
    }
  };

  if (isLoading)
    return (
      <div className="flex justify-center items-center py-6">
        <Spinner size="md" />
      </div>
    );

  if (isError) {
    // Check if the error is related to Supabase client
    const errorMessage = error?.message || "";
    const isSupabaseError =
      errorMessage.includes("Supabase client not available") ||
      errorMessage.includes("Internal Server Configuration Error");

    return (
      <div className="py-6 text-center">
        {isSupabaseError ? (
          <>
            <h3 className="text-lg font-medium text-purple-400 mb-4">
              Database Connection Issue
            </h3>
            <p className="text-purple-300 mb-4">
              The quiz feature is currently unavailable. You can still use other
              features of the application.
            </p>
            <div className="flex flex-col items-center space-y-3">
              <p className="text-purple-300 text-sm">
                This could be due to a temporary database connection issue or
                configuration setup.
              </p>
              <Button
                variant="outline"
                onClick={() =>
                  queryClient.invalidateQueries({ queryKey: ["quizzes"] })
                }
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Try Again
              </Button>
            </div>
          </>
        ) : (          <p className="text-destructive text-sm mb-4">
            Error fetching quizzes: {error?.message || "Unknown error"}
          </p>
        )}
      </div>
    );
  }

  const quizzes = quizzesResponse?.quizzes || [];
  return (
    <div>
      {quizzes.length === 0 ? (
        <p className="text-purple-300">No quizzes yet. Create one!</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quizzes.map((quiz: Quiz) => (
            <div
              key={quiz.id}
              className="bg-slate-800 border border-slate-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-150 ease-in-out"
            >
              <div className="mb-3">
                <h3 className="text-purple-400 font-semibold mb-2 truncate" title={quiz.name}>
                  {quiz.name}
                </h3>
                {quiz.description && (
                  <p className="text-sm text-purple-300 mb-2 line-clamp-2">
                    {quiz.description}
                  </p>
                )}
                <p className="text-xs text-purple-300 mb-4">
                  Created:{" "}
                  {new Date(quiz.created_at || Date.now()).toLocaleDateString()}
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onPlayQuiz(String(quiz.id))}
                  className="w-full"
                >
                  <span className="material-icons md-18 mr-1">play_arrow</span>
                  Play
                </Button>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onSelectQuiz(String(quiz.id), quiz.name)}
                    className="flex-1"
                  >
                    <span className="material-icons md-18 mr-1">edit</span>
                    Manage
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDeleteQuiz(String(quiz.id))}
                    className="flex-1"
                  >
                    <span className="material-icons md-18 mr-1">delete</span>
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
