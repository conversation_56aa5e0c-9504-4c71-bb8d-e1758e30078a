import React, { useEffect, useState } from "react";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  ChevronLeft,
  ChevronRight,
  X,
  Clock,
  CheckCircle,
  RotateCcw,
  Brain,
  TrendingUp,
  Calendar,
} from "lucide-react";
import { notify } from "@/lib/notifications";
import { useKeyboardNavigation } from "@/hooks/useKeyboardNavigation";
import {
  ReviewDifficulty,
  updateQuizQuestionSRS,
  getQuizQuestionsDueForReview,
  sortQuizQuestionsByDueDate,
} from "@/lib/srs";
import { QuizAPI } from "@/lib/api/quizApi";

type QuizQuestion = Tables<"quiz_questions">;

interface SRSQuizModeProps {
  onExit: () => void;
}

interface SRSResults {
  questionsReviewed: number;
  correctAnswers: number;
  accuracy: number;
  timeSpent: number;
}

export const SRSQuizMode: React.FC<SRSQuizModeProps> = ({ onExit }) => {
  const { user } = useAuth();
  const [allQuestions, setAllQuestions] = useState<QuizQuestion[]>([]);
  const [dueQuestions, setDueQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({});
  const [submittedAnswers, setSubmittedAnswers] = useState<Array<boolean>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [results, setResults] = useState<SRSResults | null>(null);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    const fetchDueQuestions = async () => {
      if (!user) return;
      setLoading(true);
      setError(null);

      try {
        // Fetch all quiz questions for the user
        const { data: questionsData, error: questionsError } = await supabase
          .from("quiz_questions")
          .select("*")
          .eq("user_id", user.id)
          .order("created_at", { ascending: true });

        if (questionsError) throw questionsError;

        const allQuestions = questionsData || [];
        setAllQuestions(allQuestions);

        // Filter to only questions due for review and sort by priority
        const dueQuestions = getQuizQuestionsDueForReview(allQuestions);
        const sortedDueQuestions = sortQuizQuestionsByDueDate(dueQuestions);

        setDueQuestions(sortedDueQuestions);
        setSubmittedAnswers(new Array(sortedDueQuestions.length).fill(false));

        if (sortedDueQuestions.length === 0) {
          notify.info({
            title: "No questions due",
            description:
              "Great job! No questions are due for review right now.",
          });
        }
      } catch (err: any) {
        console.error("Error fetching due questions:", err);
        setError(err.message || "Failed to load questions due for review.");
        notify.error({
          title: "Failed to load SRS questions",
          description: err.message || "Please try again later.",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDueQuestions();
  }, [user]);

  const handleAnswerSelect = (questionId: string, answer: any) => {
    setUserAnswers((prev) => ({
      ...prev,
      [questionId]: answer,
    }));
  };

  const handleSelectAllAnswerToggle = (
    questionId: string,
    optionText: string
  ) => {
    setUserAnswers((prev) => {
      const currentAnswers = prev[questionId] || [];
      const isSelected = currentAnswers.includes(optionText);

      if (isSelected) {
        return {
          ...prev,
          [questionId]: currentAnswers.filter(
            (ans: string) => ans !== optionText
          ),
        };
      } else {
        return {
          ...prev,
          [questionId]: [...currentAnswers, optionText],
        };
      }
    });
  };

  const checkAnswerCorrect = (
    question: QuizQuestion,
    userAnswer: any
  ): boolean => {
    if (!userAnswer) return false;

    if (question.type === "multiple_choice" && question.options) {
      const selectedOption = (question.options as any[])?.find(
        (opt) => opt.text === userAnswer
      );
      return selectedOption?.is_correct || false;
    } else if (question.type === "select_all_that_apply" && question.options) {
      const userSelectedAnswers = userAnswer || [];
      const correctOptions = (question.options as any[])?.filter(
        (opt) => opt.is_correct
      );
      const correctOptionTexts = correctOptions.map((opt) => opt.text);

      return (
        correctOptionTexts.length === userSelectedAnswers.length &&
        correctOptionTexts.every((text) => userSelectedAnswers.includes(text))
      );
    } else if (question.type === "true_false") {
      return userAnswer === question.correct_answer;
    } else if (question.type === "short_answer") {
      return (
        userAnswer?.toLowerCase().trim() ===
        question.correct_answer?.toLowerCase().trim()
      );
    }
    return false;
  };

  const handleSubmitAnswer = async () => {
    const currentQuestion = dueQuestions[currentQuestionIndex];
    if (
      currentQuestion &&
      userAnswers[currentQuestion.id] !== undefined &&
      !submittedAnswers[currentQuestionIndex]
    ) {
      const isCorrect = checkAnswerCorrect(
        currentQuestion,
        userAnswers[currentQuestion.id]
      );

      const newSubmittedAnswers = [...submittedAnswers];
      newSubmittedAnswers[currentQuestionIndex] = true;
      setSubmittedAnswers(newSubmittedAnswers);

      // SRS Update Logic with more nuanced difficulty assessment
      let difficulty: ReviewDifficulty;
      if (isCorrect) {
        // Could add time-based difficulty assessment here
        difficulty = ReviewDifficulty.EASY;
      } else {
        difficulty = ReviewDifficulty.DIFFICULT;
      }

      const updatedQuestion = updateQuizQuestionSRS(
        currentQuestion,
        difficulty
      );

      // Persist SRS updates to the backend
      try {
        await QuizAPI.updateQuestionSRS(updatedQuestion.id, {
          srs_level: updatedQuestion.srs_level ?? undefined,
          due_at: updatedQuestion.due_at ?? undefined,
          last_reviewed_at: updatedQuestion.last_reviewed_at ?? undefined,
          srs_interval: updatedQuestion.srs_interval ?? undefined,
          srs_ease_factor: updatedQuestion.srs_ease_factor ?? undefined,
          srs_repetitions: updatedQuestion.srs_repetitions ?? undefined,
          srs_correct_streak: updatedQuestion.srs_correct_streak ?? undefined,
        });

        console.log("SRS Updated successfully:", updatedQuestion.id);
      } catch (error) {
        console.error("Failed to update SRS data:", error);
        notify.error({
          title: "SRS Update Failed",
          description: "Failed to save spaced repetition data",
        });
      }

      // Auto-advance to next question after a brief delay
      setTimeout(() => {
        if (currentQuestionIndex < dueQuestions.length - 1) {
          setCurrentQuestionIndex((prev) => prev + 1);
        } else {
          handleCompleteReview();
        }
      }, 1500);
    }
  };

  const handleCompleteReview = () => {
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    let correctAnswers = 0;

    dueQuestions.forEach((q) => {
      const userAnswer = userAnswers[q.id];
      if (userAnswer && checkAnswerCorrect(q, userAnswer)) {
        correctAnswers++;
      }
    });

    const reviewResults: SRSResults = {
      questionsReviewed: dueQuestions.length,
      correctAnswers,
      accuracy: Math.round((correctAnswers / dueQuestions.length) * 100),
      timeSpent,
    };

    setResults(reviewResults);
    setIsCompleted(true);

    notify.success({
      title: "SRS Review Complete!",
      description: `You reviewed ${dueQuestions.length} questions with ${reviewResults.accuracy}% accuracy.`,
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const restartReview = () => {
    setCurrentQuestionIndex(0);
    setUserAnswers({});
    setSubmittedAnswers(new Array(dueQuestions.length).fill(false));
    setIsCompleted(false);
    setResults(null);
  };

  // Keyboard navigation
  useKeyboardNavigation({
    onNext: () =>
      !isCompleted && currentQuestionIndex < dueQuestions.length - 1,
    onPrevious: () => !isCompleted && currentQuestionIndex > 0,
    onSubmit: () => !isCompleted && handleSubmitAnswer,
    onEscape: onExit,
    disabled: loading || isCompleted,
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6 text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <Button
            onClick={onExit}
            variant="outline"
            className="border-slate-600 text-slate-300"
          >
            Go Back
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (dueQuestions.length === 0) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6 text-center">
          <div className="mb-4">
            <CheckCircle className="h-16 w-16 mx-auto text-green-500 mb-4" />
            <h2 className="text-xl font-semibold text-slate-200 mb-2">
              All Caught Up!
            </h2>
            <p className="text-slate-400">
              No questions are due for review right now.
            </p>
            <p className="text-slate-500 text-sm mt-2">
              You have {allQuestions.length} total questions across all quizzes.
            </p>
          </div>
          <Button
            onClick={onExit}
            className="bg-purple-600 hover:bg-purple-700"
          >
            Back to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (isCompleted && results) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-slate-200 flex items-center justify-center gap-2">
            <Brain className="h-8 w-8 text-purple-400" />
            SRS Review Complete!
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-slate-900 p-4 rounded-lg text-center">
              <p className="text-slate-400 text-sm">Questions Reviewed</p>
              <p className="text-2xl font-bold text-purple-400">
                {results.questionsReviewed}
              </p>
            </div>
            <div className="bg-slate-900 p-4 rounded-lg text-center">
              <p className="text-slate-400 text-sm">Correct</p>
              <p className="text-2xl font-bold text-green-400">
                {results.correctAnswers}
              </p>
            </div>
            <div className="bg-slate-900 p-4 rounded-lg text-center">
              <p className="text-slate-400 text-sm">Accuracy</p>
              <p className="text-2xl font-bold text-purple-400">
                {results.accuracy}%
              </p>
            </div>
            <div className="bg-slate-900 p-4 rounded-lg text-center">
              <p className="text-slate-400 text-sm">Time</p>
              <p className="text-2xl font-bold text-purple-400">
                {formatTime(results.timeSpent)}
              </p>
            </div>
          </div>

          <div className="flex justify-center space-x-3">
            <Button
              onClick={restartReview}
              variant="outline"
              className="border-slate-600 text-slate-300"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Review Again
            </Button>
            <Button
              onClick={onExit}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Finish
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentQuestion = dueQuestions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / dueQuestions.length) * 100;
  const isAnswered = userAnswers[currentQuestion.id] !== undefined;
  const isSubmitted = submittedAnswers[currentQuestionIndex];

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-semibold text-slate-200 flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-400" />
                SRS Review Mode
              </h2>
              <p className="text-slate-400 text-sm mt-1">
                Spaced repetition optimized learning
              </p>
            </div>
            <Button
              onClick={onExit}
              variant="ghost"
              size="sm"
              className="text-slate-400 hover:text-slate-200"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-slate-400">
                Question {currentQuestionIndex + 1} of {dueQuestions.length}
              </span>
              <Badge
                variant="secondary"
                className="text-xs bg-purple-900/30 text-purple-400 border-purple-700"
              >
                <TrendingUp className="h-3 w-3 mr-1" />
                SRS Level {currentQuestion.srs_level || 0}
              </Badge>
            </div>
            <div className="flex items-center space-x-2 text-sm text-slate-400">
              <Clock className="h-4 w-4" />
              <span>
                {formatTime(Math.round((Date.now() - startTime) / 1000))}
              </span>
            </div>
          </div>

          <Progress value={progress} className="mb-6" />
        </CardContent>
      </Card>

      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-slate-200 mb-4">
            {currentQuestion.question_text}
          </h3>

          <div className="space-y-3">
            {/* Question rendering logic (same as QuizPlayer) */}
            {currentQuestion.type === "multiple_choice" &&
              currentQuestion.options && (
                <div className="space-y-3">
                  {(currentQuestion.options as any[]).map((option, index) => (
                    <label
                      key={index}
                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${
                        userAnswers[currentQuestion.id] === option.text
                          ? isSubmitted
                            ? option.is_correct
                              ? "border-green-500 bg-green-950/20"
                              : "border-red-500 bg-red-950/20"
                            : "border-purple-500 bg-purple-950/20"
                          : isSubmitted && option.is_correct
                          ? "border-green-500 bg-green-950/20"
                          : "border-slate-600"
                      }`}
                    >
                      <input
                        type="radio"
                        name={`question-${currentQuestion.id}`}
                        value={option.text}
                        checked={
                          userAnswers[currentQuestion.id] === option.text
                        }
                        onChange={() =>
                          !isSubmitted &&
                          handleAnswerSelect(currentQuestion.id, option.text)
                        }
                        className="sr-only"
                        disabled={isSubmitted}
                      />
                      <div
                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                          userAnswers[currentQuestion.id] === option.text
                            ? isSubmitted
                              ? option.is_correct
                                ? "border-green-500 bg-green-500"
                                : "border-red-500 bg-red-500"
                              : "border-purple-500 bg-purple-500"
                            : isSubmitted && option.is_correct
                            ? "border-green-500 bg-green-500"
                            : "border-slate-400"
                        }`}
                      >
                        {(userAnswers[currentQuestion.id] === option.text ||
                          (isSubmitted && option.is_correct)) && (
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                        )}
                      </div>
                      <span className="text-slate-200">{option.text}</span>
                    </label>
                  ))}
                </div>
              )}

            {currentQuestion.type === "select_all_that_apply" &&
              currentQuestion.options && (
                <div className="space-y-3">
                  <p className="text-sm text-slate-400 mb-3 italic">
                    Select all correct answers:
                  </p>
                  {(currentQuestion.options as any[]).map((option, index) => {
                    const isSelected = (
                      userAnswers[currentQuestion.id] || []
                    ).includes(option.text);
                    return (
                      <label
                        key={index}
                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${
                          isSelected
                            ? isSubmitted
                              ? option.is_correct
                                ? "border-green-500 bg-green-950/20"
                                : "border-red-500 bg-red-950/20"
                              : "border-purple-500 bg-purple-950/20"
                            : isSubmitted && option.is_correct
                            ? "border-green-500 bg-green-950/20"
                            : "border-slate-600"
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() =>
                            !isSubmitted &&
                            handleSelectAllAnswerToggle(
                              currentQuestion.id,
                              option.text
                            )
                          }
                          className="sr-only"
                          disabled={isSubmitted}
                        />
                        <div
                          className={`w-4 h-4 border-2 mr-3 flex items-center justify-center rounded ${
                            isSelected
                              ? isSubmitted
                                ? option.is_correct
                                  ? "border-green-500 bg-green-500"
                                  : "border-red-500 bg-red-500"
                                : "border-purple-500 bg-purple-500"
                              : isSubmitted && option.is_correct
                              ? "border-green-500 bg-green-500"
                              : "border-slate-400"
                          }`}
                        >
                          {(isSelected ||
                            (isSubmitted && option.is_correct)) && (
                            <div className="w-2 h-2 bg-white rounded-sm"></div>
                          )}
                        </div>
                        <span className="text-slate-200">{option.text}</span>
                      </label>
                    );
                  })}
                </div>
              )}

            {currentQuestion.type === "true_false" && (
              <div className="space-y-3">
                {["True", "False"].map((val) => (
                  <label
                    key={val}
                    className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${
                      userAnswers[currentQuestion.id] === val
                        ? isSubmitted
                          ? val === currentQuestion.correct_answer
                            ? "border-green-500 bg-green-950/20"
                            : "border-red-500 bg-red-950/20"
                          : "border-purple-500 bg-purple-950/20"
                        : isSubmitted && val === currentQuestion.correct_answer
                        ? "border-green-500 bg-green-950/20"
                        : "border-slate-600"
                    }`}
                  >
                    <input
                      type="radio"
                      name={`question-${currentQuestion.id}`}
                      value={val}
                      checked={userAnswers[currentQuestion.id] === val}
                      onChange={() =>
                        !isSubmitted &&
                        handleAnswerSelect(currentQuestion.id, val)
                      }
                      className="sr-only"
                      disabled={isSubmitted}
                    />
                    <div
                      className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                        userAnswers[currentQuestion.id] === val
                          ? isSubmitted
                            ? val === currentQuestion.correct_answer
                              ? "border-green-500 bg-green-500"
                              : "border-red-500 bg-red-500"
                            : "border-purple-500 bg-purple-500"
                          : isSubmitted &&
                            val === currentQuestion.correct_answer
                          ? "border-green-500 bg-green-500"
                          : "border-slate-400"
                      }`}
                    >
                      {(userAnswers[currentQuestion.id] === val ||
                        (isSubmitted &&
                          val === currentQuestion.correct_answer)) && (
                        <div className="w-2 h-2 rounded-full bg-white"></div>
                      )}
                    </div>
                    <span className="text-slate-200">{val}</span>
                  </label>
                ))}
              </div>
            )}

            {currentQuestion.type === "short_answer" && (
              <div className="space-y-3">
                <input
                  type="text"
                  value={userAnswers[currentQuestion.id] || ""}
                  onChange={(e) =>
                    !isSubmitted &&
                    handleAnswerSelect(currentQuestion.id, e.target.value)
                  }
                  className={`w-full px-4 py-3 border rounded-lg text-slate-200 placeholder-slate-400 focus:ring-1 outline-none ${
                    isSubmitted
                      ? userAnswers[currentQuestion.id]
                          ?.toLowerCase()
                          .trim() ===
                        currentQuestion.correct_answer?.toLowerCase().trim()
                        ? "bg-green-950/20 border-green-500 focus:border-green-500 focus:ring-green-500"
                        : "bg-red-950/20 border-red-500 focus:border-red-500 focus:ring-red-500"
                      : "bg-slate-900 border-slate-600 focus:border-purple-500 focus:ring-purple-500"
                  }`}
                  placeholder="Type your answer here..."
                  disabled={isSubmitted}
                />
                {isSubmitted && (
                  <p className="text-sm text-slate-400">
                    <span className="font-medium">Correct answer:</span>{" "}
                    {currentQuestion.correct_answer}
                  </p>
                )}
              </div>
            )}
          </div>

          {isSubmitted && currentQuestion.explanation && (
            <div className="mt-4 p-4 bg-slate-900 rounded-lg border border-slate-600">
              <h4 className="text-sm font-medium text-slate-300 mb-2">
                Explanation:
              </h4>
              <p className="text-slate-400 text-sm">
                {currentQuestion.explanation}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-center">
        {!isSubmitted && isAnswered ? (
          <Button
            onClick={handleSubmitAnswer}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Submit Answer
          </Button>
        ) : isSubmitted ? (
          <div className="text-center">
            <p className="text-slate-400 text-sm">
              {currentQuestionIndex === dueQuestions.length - 1
                ? "Completing review..."
                : "Moving to next question..."}
            </p>
          </div>
        ) : (
          <p className="text-slate-500 text-sm">Select an answer to continue</p>
        )}
      </div>
    </div>
  );
};
