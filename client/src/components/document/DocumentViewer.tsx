import React, { useEffect, useState } from "react";
// Removed direct Supabase import - using backend API endpoints
import { Tables } from "../../types/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, FileText, Download } from "lucide-react";
import Spinner from "@/components/ui/Spinner";
import MarkdownRenderer from "./MarkdownRenderer";

type StudyDocument = Tables<"study_documents">;

interface DocumentViewerProps {
  document: StudyDocument;
  onClose: () => void;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL ||
  (import.meta.env.DEV ? "http://localhost:5000/api" : "/api");

// Helper to get auth token
async function getAuthToken(): Promise<string | null> {
  try {
    const token = localStorage.getItem('auth_token');
    return token || null;
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  document,
  onClose,
}) => {
  const [content, setContent] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };
    window.addEventListener("keydown", handleEsc);
    return () => {
      window.removeEventListener("keydown", handleEsc);
    };
  }, [onClose]);

  useEffect(() => {
    const fetchDocumentContent = async () => {
      if (!document?.id) {
        setContent(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setContent(null);

      try {
        const token = await getAuthToken();
        if (!token) {
          throw new Error("Authentication required");
        }

        const url = `${API_BASE_URL}/documents/${document.id}/content`;
        console.log("Fetching document content from:", url);
        console.log("Using token:", token ? "✓ Present" : "✗ Missing");

        // Fetch content from secure backend endpoint
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        console.log("Response status:", response.status);
        console.log("Response headers:", Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          let errorData;
          try {
            errorData = await response.json();
          } catch {
            errorData = { error: `HTTP ${response.status}: ${response.statusText}` };
          }
          console.error("API Error:", errorData);
          throw new Error(
            errorData.error || `HTTP ${response.status}: ${response.statusText}`
          );
        }

        const textContent = await response.text();
        console.log("Content fetched successfully, length:", textContent.length);
        setContent(textContent);
      } catch (err: any) {
        console.error("Error fetching document content:", err);
        console.error("Error details:", {
          name: err.name,
          message: err.message,
          stack: err.stack
        });
        setError(err.message || "Failed to load document content.");
      } finally {
        setLoading(false);
      }
    };

    fetchDocumentContent();
  }, [document]);

  const handleDownload = async () => {
    if (!content || !document) return;

    try {
      const blob = new Blob([content], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${document.file_name}_extracted.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error("Error downloading content:", err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl h-[80vh] bg-slate-800 border-slate-700 flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-purple-400" />
            <CardTitle className="text-lg text-purple-400">
              {document?.file_name || "Document Viewer"}
            </CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {content && (
              <Button
                onClick={handleDownload}
                variant="outline"
                size="sm"
                className="text-slate-300 border-slate-600 hover:bg-slate-700"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            )}
            <Button
              onClick={onClose}
              variant="outline"
              size="sm"
              className="text-slate-300 border-slate-600 hover:bg-slate-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 p-6 overflow-hidden">
          {loading && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Spinner size="lg" />
                <p className="text-slate-300 mt-4">
                  Loading document content...
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-red-400 mb-4">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  className="text-slate-300 border-slate-600 hover:bg-slate-700"
                >
                  Retry
                </Button>
              </div>
            </div>
          )}

          {content && !loading && !error && (
            <ScrollArea className="h-full w-full">
              <div className="bg-slate-900 p-6 rounded-lg">
                <div className="mb-4 text-sm text-slate-400">
                  <p>
                    <strong>File:</strong> {document.file_name}
                  </p>
                  <p>
                    <strong>Type:</strong> {document.content_type}
                  </p>
                  <p>
                    <strong>Size:</strong>{" "}
                    {document.size_bytes
                      ? `${(document.size_bytes / 1024).toFixed(1)} KB`
                      : "Unknown"}
                  </p>
                  <p>
                    <strong>Status:</strong>{" "}
                    <span className="text-green-400">{document.status}</span>
                  </p>
                </div>
                <div className="border-t border-slate-700 pt-4">
                  <h3 className="text-slate-300 font-medium mb-3">
                    Extracted Content:
                  </h3>
                  <MarkdownRenderer
                    content={content}
                    className="text-sm leading-relaxed"
                  />
                </div>
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
