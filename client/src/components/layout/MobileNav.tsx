import React from "react";
import { Link, useLocation } from "wouter";
import {
  LucideIcon,
  Home,
  BookOpen,
  FileQuestion,
  Upload,
  Settings,
} from "lucide-react";

interface NavItemProps {
  href: string;
  icon: LucideIcon;
  label: string;
  isActive: boolean;
  onClick?: (e: React.MouseEvent) => void;
}

const NavItem: React.FC<NavItemProps> = ({
  href,
  icon: Icon,
  label,
  isActive,
  onClick,
}) => (
  <Link
    href={href}
    onClick={onClick}
    className={`flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors duration-150 ease-in-out 
      ${isActive ? "text-purple-400" : "text-slate-400 hover:text-purple-300"}`}
    aria-label={label}
  >
    <Icon size={20} className="mb-0.5" />
    <span className="text-xs tracking-tight leading-tight">{label}</span>
  </Link>
);

const MobileNav: React.FC = () => {
  const [location, navigate] = useLocation();

  const handleUploadClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation if it's just a trigger
    navigate("/"); // Navigate to dashboard first
    // Dispatch event to open upload dialog on dashboard page
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent("openDocumentUploadDialog"));
    }, 100); // Timeout to allow dashboard to mount if navigating
  };

  return (
    <nav className="md:hidden bg-slate-900 border-t border-slate-700 fixed bottom-0 left-0 right-0 z-40 flex justify-around items-stretch">
      <NavItem
        href="/"
        icon={Home}
        label="Dashboard"
        isActive={location === "/"}
      />
      <NavItem
        href="/flashcards"
        icon={BookOpen}
        label="Flashcards"
        isActive={location.startsWith("/flashcards")}
      />
      <NavItem
        href="/quizzes"
        icon={FileQuestion}
        label="Quizzes"
        isActive={location.startsWith("/quizzes")}
      />
      <NavItem
        href="/"
        icon={Upload}
        label="Upload"
        isActive={false}
        onClick={handleUploadClick}
      />
      <NavItem
        href="/ai-config"
        icon={Settings}
        label="Settings"
        isActive={location === "/ai-config"}
      />
    </nav>
  );
};

export default MobileNav;
