import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Link, useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getAllDecks, deleteDeck } from "@/lib/storage";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { Tables } from "@/types/supabase";
import { DocumentViewer } from "@/components/document/DocumentViewer";
import { InlineDocumentViewer } from "@/components/document/InlineDocumentViewer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useEscapeKey } from "@/App";
import {
  Layers,
  FileTextIcon,
  Eye,
  ListTree,
  ExternalLink,
} from "lucide-react";
import { QuizList } from "@/components/quiz/QuizList";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { queryClient } from "@/lib/queryClient";
import MarkdownRenderer from "@/components/document/MarkdownRenderer";

type StudyDocument = Tables<"study_documents">;

interface SummaryDisplayModalProps {
  summary: StudyDocument;
  isOpen: boolean;
  onClose: () => void;
}

const SummaryDisplayModal: React.FC<SummaryDisplayModalProps> = ({ summary, isOpen, onClose }) => {
  useEscapeKey(onClose);

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[625px] bg-slate-800 border-slate-700 text-slate-200">
        <DialogHeader>
          <DialogTitle className="text-purple-400">Summary: {summary.file_name}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[60vh] mt-4">
          <MarkdownRenderer
            content={summary.extracted_text_summary || "No summary available."}
            className="text-sm leading-relaxed"
          />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

const StudySection: React.FC = () => {
  const [activeTab, setActiveTab] = useState("flashcards");
  const [selectedDocument, setSelectedDocument] =
    useState<StudyDocument | null>(null);
  const [selectedSummary, setSelectedSummary] = useState<StudyDocument | null>(null);
  const [, navigate] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();

  const { data: decks, isLoading: isLoadingDecks } = useQuery({
    queryKey: ["/api/flashcard-decks"],
    queryFn: getAllDecks,
  });

  const { data: documents, isLoading: isLoadingDocuments } = useQuery({
    queryKey: ["study-documents", user?.id],
    queryFn: async () => {
      if (!user) return [];

      const { data, error } = await supabase
        .from("study_documents")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });
        // .not("extracted_text_summary", "is", null); // For summaries tab, if we only want docs with summaries

      if (error) {
        console.error("Error fetching documents:", error);
        return [];
      }

      return data || [];
    },
    enabled: !!user,
  });

  const documentsWithSummaries = React.useMemo(() => {
    return (documents || []).filter((doc: any) => doc.extracted_text_summary && doc.extracted_text_summary.trim() !== "");
  }, [documents]);

  const handleDocumentSelect = (doc: StudyDocument) => {
    setSelectedDocument(doc);
  };

  const handleDocumentView = (doc: StudyDocument) => {
    navigate(`/documents/${doc.id}`);
  };

  const handleSelectQuiz = (quizId: string, quizName: string) => {
    navigate(`/quizzes/${quizId}/edit`);
  };

  const handlePlayQuiz = (quizId: string) => {
    navigate(`/quizzes/${quizId}`);
  };

  const handleDeleteDeck = async (deckId: string, deckName: string) => {
    if (
      !window.confirm(
        `Are you sure you want to delete the deck "${deckName}"? All flashcards in this deck will also be deleted.`
      )
    )
      return;

    try {
      await deleteDeck(deckId);
      
      // Invalidate and refetch the decks
      queryClient.invalidateQueries({
        queryKey: ["/api/flashcard-decks"],
      });
      
      toast({
        title: "Success",
        description: `Deck "${deckName}" has been deleted.`,
      });
    } catch (error) {
      console.error("Error deleting deck:", error);
      toast({
        title: "Error",
        description: "Failed to delete the deck. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleManageDeck = (deckId: string) => {
    navigate(`/flashcards/edit/${deckId}`);
  };

  const handleDeleteDocument = async (document: StudyDocument) => {
    try {
      // Delete from Supabase
      const { error } = await supabase
        .from("study_documents")
        .delete()
        .eq("id", document.id)
        .eq("user_id", user?.id);

      if (error) {
        throw error;
      }

      // Clear selected document if it was the one being deleted
      if (selectedDocument?.id === document.id) {
        setSelectedDocument(null);
      }

      // Invalidate and refetch the documents
      queryClient.invalidateQueries({
        queryKey: ["study-documents", user?.id],
      });

      toast({
        title: "Success",
        description: `Document "${document.file_name}" has been deleted.`,
      });
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Error",
        description: "Failed to delete the document. Please try again.",
        variant: "destructive",
      });
    }
  };

  const commonCardClasses =
    "bg-slate-800 border-slate-700 text-purple-400 shadow-md";
  const textPrimaryClass = "text-purple-400";
  const textSecondaryClass = "text-purple-300";
  const textMutedClass = "text-purple-300";
  const accentColor = "purple-400";
  const accentTextClass = `text-${accentColor}`;
  const accentBorderClass = `border-${accentColor}`;
  const accentBgClass = `bg-${accentColor}`;
  const hoverAccentBgClass = `hover:bg-purple-500`;

  return (
    <section className="mb-8 bg-slate-800 p-6 rounded-lg text-purple-400">
      <div className="flex justify-between items-center mb-4">
        <h2 className={`text-xl font-medium ${accentTextClass}`}>
          Study Materials
        </h2>
      </div>

      <Tabs
        defaultValue="flashcards"
        value={activeTab}
        onValueChange={setActiveTab}
        className="mb-6"
      >
        <TabsList className="grid w-full grid-cols-4 border-b border-slate-700 mb-4 bg-transparent">
          {["flashcards", "quizzes", "summaries", "documents"].map(
            (tabValue) => (
              <TabsTrigger
                key={tabValue}
                value={tabValue}
                className={`pb-2 text-sm font-medium rounded-none 
                          data-[state=active]:${accentTextClass} data-[state=active]:border-b-2 data-[state=active]:${accentBorderClass} 
                          ${textMutedClass} hover:${accentTextClass}/80 transition-colors`}
              >
                {tabValue.charAt(0).toUpperCase() + tabValue.slice(1)}
              </TabsTrigger>
            )
          )}
        </TabsList>

        <TabsContent value="flashcards">
          {isLoadingDecks ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className={`${commonCardClasses}`}>
                  <Skeleton className="h-24 w-full bg-slate-700/80" />
                  <CardContent className="p-4">
                    <Skeleton className={`h-6 w-3/4 mb-2 bg-slate-700`} />
                    <div className="flex justify-between mb-3">
                      <Skeleton className={`h-4 w-20 bg-slate-700`} />
                      <Skeleton className={`h-4 w-20 bg-slate-700`} />
                    </div>
                    <Skeleton className={`h-2 w-full mb-1 bg-slate-700`} />
                    <div className="flex justify-between mb-3">
                      <Skeleton className={`h-3 w-24 bg-slate-700`} />
                      <Skeleton className={`h-3 w-24 bg-slate-700`} />
                    </div>
                    <Skeleton className={`h-9 w-full bg-slate-600`} />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : decks && decks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {decks.map((deck) => (
                <Card
                  key={deck.id}
                  className={`overflow-hidden ${commonCardClasses} hover:shadow-lg transition-shadow`}
                >
                  <div
                    className={`h-24 ${accentBgClass}/30 flex items-center justify-center ${accentTextClass}/70`}
                  >
                    <Layers size={36} />
                  </div>
                  <CardContent className="p-4">
                    <h3
                      className={`font-medium ${textPrimaryClass} mb-2 truncate`}
                      title={deck.name}
                    >
                      {deck.name}
                    </h3>
                    <div
                      className={`flex justify-between text-sm ${textMutedClass} mb-3`}
                    >
                      <span>{deck.totalCards} cards</span>
                      <span>{deck.dueTodayCount} due</span>
                    </div>
                    <div className="mb-3">
                      <Progress
                        value={
                          deck.totalCards > 0
                            ? (deck.masteredCount / deck.totalCards) * 100
                            : 0
                        }
                        className={`h-1.5 [&>div]:${accentBgClass}`}
                      />
                      <div
                        className={`flex justify-between mt-1 text-xs ${textMutedClass}`}
                      >
                        <span>
                          {deck.totalCards > 0
                            ? Math.round(
                                (deck.masteredCount / deck.totalCards) * 100
                              )
                            : 0}
                          %
                        </span>
                        <span>{deck.masteredCount} done</span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2">
                      <Link href={`/flashcards/${deck.id}`}>
                        <Button
                          className={`w-full ${
                            deck.dueTodayCount > 0
                              ? `${accentBgClass} ${hoverAccentBgClass} text-white`
                              : `bg-slate-700 hover:bg-slate-600 ${textSecondaryClass}`
                          }`}
                        >
                          <span className="material-icons md-18 mr-1">play_arrow</span>
                          {deck.dueTodayCount > 0
                            ? "Review Due Cards"
                            : "Review All"}
                        </Button>
                      </Link>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleManageDeck(deck.id)}
                          className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300"
                        >
                          <span className="material-icons md-18 mr-1">edit</span>
                          Manage
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteDeck(deck.id, deck.name)}
                          className="flex-1"
                        >
                          <span className="material-icons md-18 mr-1">delete</span>
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card
              className={`${commonCardClasses} p-6 text-center ${textMutedClass} flex flex-col items-center justify-center min-h-[200px]`}
            >
              <Layers size={40} className={`${accentTextClass}/70 mb-3`} />
              <h3 className={`text-lg font-medium mb-2 ${textPrimaryClass}`}>
                No Flashcard Decks Yet
              </h3>
              <p className={`${textMutedClass} mb-4`}>
                Upload documents and generate flashcards to start studying.
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="quizzes">
          <QuizList
            onSelectQuiz={handleSelectQuiz}
            onPlayQuiz={handlePlayQuiz}
          />
        </TabsContent>

        <TabsContent value="summaries">
          {isLoadingDocuments ? (
             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
             {[...Array(3)].map((_, i) => (
               <Card key={i} className={`${commonCardClasses}`}>
                 <Skeleton className="h-20 w-full bg-slate-700/80" />
                 <CardContent className="p-4">
                   <Skeleton className={`h-5 w-3/4 mb-2 bg-slate-700`} />
                   <Skeleton className={`h-4 w-full bg-slate-700`} />
                   <Skeleton className={`h-4 w-2/3 bg-slate-700 mt-1`} />
                 </CardContent>
               </Card>
             ))}
           </div>
          ) : documentsWithSummaries && documentsWithSummaries.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {documentsWithSummaries.map((doc: any) => (
                <Card
                  key={doc.id}
                  className={`${commonCardClasses} hover:shadow-lg transition-shadow cursor-pointer`}
                  onClick={() => setSelectedSummary(doc)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className={`text-md font-medium ${textPrimaryClass} truncate`} title={doc.file_name}>
                      {doc.file_name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className={`${textMutedClass} text-sm line-clamp-3`}>
                      {(doc.extracted_text_summary || "").substring(0, 100) + ((doc.extracted_text_summary || "").length > 100 ? "..." : "")}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className={`${commonCardClasses} p-6 text-center ${textMutedClass} flex flex-col items-center justify-center min-h-[200px]`}>
              <ListTree size={40} className={`${accentTextClass}/70 mb-3`} />
              <h3 className={`text-lg font-medium mb-2 ${textPrimaryClass}`}>No Summaries Available</h3>
              <p className={`${textMutedClass} mb-4`}>
                Upload documents and ensure summaries are generated to see them here.
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="documents">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-1">
              <Card className={`${commonCardClasses} text-slate-100 h-full`}>
                <CardHeader>
                  <CardTitle
                    className={`text-lg font-medium ${textPrimaryClass}`}
                  >
                    Uploaded Documents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingDocuments ? (
                    <div className="space-y-2">
                      {[...Array(3)].map((_, i) => (
                        <Skeleton key={i} className="h-8 w-full bg-slate-700" />
                      ))}
                    </div>
                  ) : documents && documents.length > 0 ? (
                    <ScrollArea className="h-[60vh]">
                      <ul className="space-y-1">
                        {documents.map((doc: any) => (
                          <li key={doc.id} className="space-y-1">
                            <Button
                              variant="ghost"
                              className={`w-full justify-start text-left h-auto py-2 px-3 text-sm ${
                                selectedDocument?.id === doc.id
                                  ? `${accentBgClass}/30 ${accentTextClass}`
                                  : `${textMutedClass} hover:bg-slate-700/70 hover:${textSecondaryClass}`
                              }`}
                              onClick={() => handleDocumentSelect(doc)}
                            >
                              <FileTextIcon
                                className={`text-base mr-2 flex-shrink-0 ${
                                  selectedDocument?.id === doc.id
                                    ? accentTextClass
                                    : textMutedClass
                                }`}
                              />
                              <span
                                className="truncate flex-grow"
                                title={doc.file_name}
                              >
                                {doc.file_name}
                              </span>
                              <span
                                className={`text-xs ${textMutedClass} ml-2 whitespace-nowrap`}
                              >
                                ({Math.round((doc.size_bytes || 0) / 1024)} KB)
                              </span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className={`w-full text-xs ${accentTextClass} border-${accentColor}/30 hover:bg-${accentColor}/10`}
                              onClick={() => handleDocumentView(doc)}
                            >
                              <ExternalLink className="mr-1 h-3 w-3" />
                              Open in Full View
                            </Button>
                          </li>
                        ))}
                      </ul>
                    </ScrollArea>
                  ) : (
                    <p className={`${textMutedClass} text-sm`}>
                      No documents uploaded yet. Go to the "Upload" section to
                      add some.
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
            <div className="md:col-span-2">
              {selectedDocument ? (
                <InlineDocumentViewer
                  document={selectedDocument}
                  onClose={() => setSelectedDocument(null)}
                  onDelete={handleDeleteDocument}
                />
              ) : (
                <Card
                  className={`${commonCardClasses} ${textMutedClass} p-6 text-center flex flex-col justify-center items-center min-h-[300px] h-full`}
                >
                  <Eye size={48} className={`${accentTextClass}/70 mb-4`} />
                  <h3
                    className={`text-lg font-medium mb-2 ${textPrimaryClass}`}
                  >
                    Select a Document
                  </h3>
                  <p className={`${textMutedClass}`}>
                    Choose a document from the list to view its content here.
                  </p>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {selectedSummary && (
        <SummaryDisplayModal
          summary={selectedSummary}
          isOpen={!!selectedSummary}
          onClose={() => setSelectedSummary(null)}
        />
      )}
    </section>
  );
};

export default StudySection;
