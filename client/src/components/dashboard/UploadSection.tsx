import React, { useState, useC<PERSON>back, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  UploadCloud,
  FileText,
  AlertCircle,
  Sparkles,
  HelpCircleIcon,
  ListChecks,
  HelpCircle,
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { extractTextFromFile } from "@/lib/file-parser";
import {
  getAIProviderSettings,
  isAIProviderConfigured,
  isAIProviderConfiguredSync,
} from "@/lib/ai-provider";
import { Document as DocumentType } from "@/types"; 
import { Flashcard } from "@shared/types/flashcards";
import { Quiz, GenerateQuizResponse } from "@shared/types/quiz";
import { GenerateAiQuizApiPayload } from "@/lib/api";
import FlashcardViewer from "../flashcards/FlashcardViewer";
// Removed Supabase import - using backend API endpoints

interface UploadSectionProps {
  onDocumentProcessed: (document: DocumentType) => void;
  onFlashcardsGenerated: (flashcards: Flashcard[]) => void;
  onQuizGenerated: (quiz: Quiz) => void; 
};

const QUESTION_TYPE_OPTIONS = [
  { id: "multiple_choice", label: "Multiple Choice" },
  { id: "select_all_that_apply", label: "Select All That Apply" },
  { id: "true_false", label: "True/False" },
  { id: "short_answer", label: "Short Answer" },
];

const UploadSection: React.FC<UploadSectionProps> = ({
  onDocumentProcessed,
  onFlashcardsGenerated,
  onQuizGenerated,
}) => {
  const [, setLocation] = useLocation();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [extractedText, setExtractedText] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null); 
  const [document, setDocument] = useState<DocumentType | null>(null);
  const [numberOfQuizQuestions, setNumberOfQuizQuestions] = useState<number>(5);
  const [numberOfFlashcards, setNumberOfFlashcards] = useState<number>(10);
  const [quizTitle, setQuizTitle] = useState<string>("");
  const [quizQuestionTypes, setQuizQuestionTypes] = useState<string[]>(["multiple_choice"]);
  const [flashcardCustomPrompt, setFlashcardCustomPrompt] = useState<string>("");
  const [quizCustomPrompt, setQuizCustomPrompt] = useState<string>("");
  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);

  // Check AI configuration on mount
  useEffect(() => {
    const checkAIConfig = async () => {
      const configured = await isAIProviderConfigured();
      setIsAIConfigured(configured);
    };
    checkAIConfig();
  }, []);

  const commonCardClasses =
    "bg-slate-800 border-slate-700 text-slate-100 shadow-md";
  const textPrimaryClass = "text-slate-50";
  const textSecondaryClass = "text-slate-300";
  const textMutedClass = "text-slate-400";
  const accentColor = "purple-400";
  const accentTextClass = `text-${accentColor}`;
  const accentBorderClass = `border-${accentColor}`;
  const accentBgClass = `bg-${accentColor}`;
  const hoverAccentBgClass = `hover:bg-purple-500`;
  const destructiveColor = "red-500"; 
  const warningColor = "yellow-500"; 

  const {
    mutate: processFile,
    isPending: isProcessingFile,
    error: fileError,
  } = useMutation<DocumentType, Error, File>({
    mutationFn: async (file: File) => {      
      setQuizTitle(`Quiz for ${file.name}`);
      setProgress(30);
      try {
        const doc = await extractTextFromFile(file);
        setProgress(70);
        setExtractedText(doc.content);
        onDocumentProcessed(doc);
        setDocument(doc);
        setProgress(100);
        return doc;
      } catch (err: any) {
        console.error("Error in file processing mutation:", err);
        setProgress(0);
        throw new Error(err.message || "Failed to process file.");
      }
    },
    onSuccess: () => {
      console.log("File processed successfully, ready for generation.");
      setFlashcards([]); 
      setGeneratedQuiz(null); 
    },
    onError: (error) => {
      setSelectedFile(null);
      setExtractedText(null);
      setFlashcards([]);
      setGeneratedQuiz(null);
      setQuizTitle("");
      console.error("File processing failed:", error);
    },
  });

  const {
    mutate: generateFlashcards,
    isPending: isGeneratingFlashcards,
    error: flashcardError,
  } = useMutation<Flashcard[], Error, void>({
    mutationFn: async () => {
      const isConfigured = await isAIProviderConfigured();
      if (!isConfigured) {
        throw new Error("AI Provider not configured. Please configure your AI settings first.");
      }
      if (!extractedText)
        throw new Error("No text to generate flashcards from.");
      if (!document?.id)
        throw new Error("No processed document to associate with flashcards.");

      const requestBody = {
        textContent: extractedText,
        documentId: document.id,
        deckTitle: selectedFile?.name || `Flashcards for ${document.name}`,
        count: numberOfFlashcards,
        customPrompt: flashcardCustomPrompt || undefined,
        // aiSettings removed - credentials are retrieved from secure backend storage
      };

      try {
        const response = await fetch("/api/flashcards/generate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorText = await response.text();
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            throw new Error(
              `Failed to generate flashcards: ${response.status} ${response.statusText}. ${errorText}`
            );
          }
          throw new Error(
            errorData.error ||
              errorData.message ||
              `Failed to generate flashcards: ${response.status}`
          );
        }

        const data = await response.json();
        if (data && data.deck && data.deck.flashcards) {
          return data.deck.flashcards;
        } else {
          throw new Error(
            "Unexpected response structure from flashcard generation API."
          );
        }
      } catch (err: any) {
        console.error("Error calling backend to generate flashcards:", err);
        const errorDetails =
          err.response?.data?.message ||
          err.response?.data?.details ||
          err.message ||
          "Unknown error occurred";
        throw new Error(`Failed to generate flashcards: ${errorDetails}`);
      }
    },
    onSuccess: (generatedFlashcards) => {
      console.log("Flashcards generated:", generatedFlashcards);
      setFlashcards(generatedFlashcards);
      onFlashcardsGenerated(generatedFlashcards);
    },
    onError: (error) => {
      console.error("Flashcard generation failed:", error);
    },
  });

  const {
    mutate: generateQuizMutation,
    isPending: isGeneratingQuiz,
    error: quizError,
  } = useMutation<GenerateQuizResponse, Error, void>({
    mutationFn: async () => {
      const isConfigured = await isAIProviderConfigured();
      if (!isConfigured) {
        throw new Error("AI Provider not configured. Please configure your AI settings first.");
      }
      if (!extractedText) throw new Error("No text to generate quiz from.");
      if (!document?.id)
        throw new Error("No processed document to associate with quiz.");
      if (!quizTitle.trim()) throw new Error("Quiz title cannot be empty.");
      if (numberOfQuizQuestions <= 0) {
        throw new Error("Number of questions must be greater than 0.");
      }
      if (quizQuestionTypes.length === 0) {
        throw new Error("At least one question type must be selected.");
      }

      const requestBody: GenerateAiQuizApiPayload = {
        textContent: extractedText,
        documentId: document.id,
        quizName: quizTitle.trim(),
        generationOptions: {
          numberOfQuestions: numberOfQuizQuestions,
          questionTypes: quizQuestionTypes,
        },
        customPrompt: quizCustomPrompt || undefined,
        // AI config is now handled by the backend using stored credentials
      };

      try {
        const response = await fetch("/api/quizzes/generate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          const errorText = await response.text();
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch (e) {
            throw new Error(
              `Failed to generate quiz: ${response.status} ${response.statusText}. ${errorText}`
            );
          }
          throw new Error(
            errorData.error ||
              errorData.message ||
              `Failed to generate quiz: ${response.status}`
          );
        }

        const data = await response.json();
        if (data && data.success && data.quiz) {
          return data;
        } else {
          throw new Error(
            data.message ||
              "Failed to generate quiz or unexpected response structure."
          );
        }
      } catch (err: any) {
        console.error("Error calling backend to generate quiz:", err);
        const errorDetails = err.message || "Unknown error occurred";
        throw new Error(`Failed to generate quiz: ${errorDetails}`);
      }
    },
    onSuccess: (data) => {
      if (data.quiz && data.quizId) {
        console.log("Quiz generated:", data.quiz);
        setGeneratedQuiz(data.quiz);
        onQuizGenerated(data.quiz);
        setLocation(`/quiz/${data.quizId}`);
      } else {
        console.error(
          "Quiz generation reported success but quiz data or ID is missing."
        );
      }
    },
    onError: (error) => {
      console.error("Quiz generation failed:", error);
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setExtractedText(null);
      setFlashcards([]);
      setGeneratedQuiz(null);
      setProgress(10);
      processFile(file);
    }
  };

  const handleGenerateFlashcardsClick = useCallback(() => {
    generateFlashcards();
  }, [generateFlashcards]);

  const handleGenerateQuizClick = useCallback(() => {
    generateQuizMutation();
  }, [generateQuizMutation]);

  const handleQuizQuestionTypeChange = (typeId: string) => {
    setQuizQuestionTypes((prevTypes) =>
      prevTypes.includes(typeId)
        ? prevTypes.filter((qt) => qt !== typeId)
        : [...prevTypes, typeId]
    );
  };

  const displayError = fileError || flashcardError || quizError;
  const isGeneratingAnything =
    isProcessingFile || isGeneratingFlashcards || isGeneratingQuiz;

  return (
    <Card className={`${commonCardClasses} w-full`}>
      <CardHeader>
        <CardTitle
          className={`flex items-center gap-2 text-2xl ${accentTextClass}`}
        >
          <UploadCloud className="h-6 w-6" /> Upload Document
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <label htmlFor="file-upload" className="cursor-pointer">
            <div
              className={`flex flex-col items-center justify-center p-6 border-2 border-dashed border-slate-600 rounded-lg hover:bg-slate-700/70 transition-colors`}
            >
              <UploadCloud className={`h-10 w-10 mb-2 ${textMutedClass}`} />
              <span className={`text-sm font-semibold ${textSecondaryClass} truncate max-w-full`}>
                {selectedFile ? selectedFile.name : "Click or drag file to upload"}
              </span>
              <p className={`text-xs ${textMutedClass} mt-1`}>
                PDF, TXT, MD, DOCX
              </p>
            </div>
          </label>
          <Input
            id="file-upload"
            type="file"
            className="hidden"
            accept=".pdf,.txt,.md,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            onChange={handleFileChange}
            disabled={isGeneratingAnything}
          />
        </div>

        {(isProcessingFile ||
          (progress > 0 && progress < 100 && !extractedText)) && (
          <div className="space-y-2">
            <Progress
              value={progress}
              className={`w-full h-2 [&>div]:${accentBgClass}`}
            />
            <p className={`text-sm text-center ${textSecondaryClass}`}>
              Processing file...
            </p>
          </div>
        )}

        {displayError && (
          <Alert
            variant="destructive"
            className={`bg-red-900/30 border-${destructiveColor} text-${destructiveColor}`}
          >
            <AlertCircle className={`h-4 w-4 text-${destructiveColor}`} />
            <AlertTitle className={`text-${destructiveColor}`}>
              Error
            </AlertTitle>
            <AlertDescription className={`text-red-300`}>
              {displayError.message}
            </AlertDescription>
          </Alert>
        )}

        {extractedText && !isGeneratingAnything && (
          <div className="space-y-6">
            {/* Section Header */}
            <div className="text-center space-y-2 py-4 border-t border-slate-700">
              <h3 className={`text-lg font-semibold ${textPrimaryClass}`}>
                What would you like to generate? (Optional)
              </h3>
              <p className={`text-sm ${textSecondaryClass}`}>
                Choose one or both options below. You can always generate content later from your documents.
              </p>
            </div>

            {/* Flashcard Generation Section */}
            <div className="bg-slate-700/30 border border-slate-600 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5 text-purple-400" />
                <h4 className={`text-md font-medium ${textPrimaryClass}`}>
                  Generate Flashcards
                </h4>
              </div>
              <p className={`text-sm ${textSecondaryClass}`}>
                Create study flashcards from your document content for spaced repetition learning.
              </p>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="num-flashcards" className={textSecondaryClass}>
                    Number of Flashcards
                </Label>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-slate-500 hover:text-slate-300 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent className="bg-slate-800 border-slate-600 text-slate-200">
                      <p>Recommended: 5-15 flashcards per document</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <Input
                type="number"
                id="num-flashcards"
                value={numberOfFlashcards}
                onChange={(e) =>
                  setNumberOfFlashcards(
                    Math.max(1, parseInt(e.target.value, 10) || 1)
                  )
                }
                min="1"
                max="50"
                className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}
                disabled={isGeneratingAnything || !isAIConfigured}
              />
            </div>

            {/* Custom Prompt for Flashcards */}
            <div className="space-y-2">
              <Label htmlFor="flashcard-custom-prompt" className={textSecondaryClass}>
                Custom Prompt (Optional)
              </Label>
              <Textarea
                id="flashcard-custom-prompt"
                value={flashcardCustomPrompt}
                onChange={(e) => setFlashcardCustomPrompt(e.target.value)}
                placeholder="e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'"
                rows={3}
                className="!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500"
                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}
              />
              <p className="text-xs text-purple-400">
                Add specific instructions for the AI on what kind of flashcards you want.
              </p>
            </div>

            <Button
              onClick={handleGenerateFlashcardsClick}
              disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2.5 text-sm font-medium flex items-center justify-center"
            >
              {isGeneratingFlashcards ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                  Generating Flashcards...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" /> Generate Flashcards
                </>
              )}
            </Button>
            </div>

            {/* Quiz Generation Section */}
            <div className="bg-slate-700/30 border border-slate-600 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-2">
                <ListChecks className="h-5 w-5 text-purple-400" />
                <h4 className={`text-md font-medium ${textPrimaryClass}`}>
                  Generate Quiz
                </h4>
              </div>
              <p className={`text-sm ${textSecondaryClass}`}>
                Create an interactive quiz from your document content to test your knowledge.
              </p>

              <div className="space-y-1.5">
                <Label htmlFor="quiz-title" className={textSecondaryClass}>
                  Quiz Title
                </Label>
                <Input
                  type="text"
                  id="quiz-title"
                  value={quizTitle}
                  onChange={(e) => setQuizTitle(e.target.value)}
                  placeholder="Enter title for your quiz"
                  className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}
                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="num-questions" className={textSecondaryClass}>
                    Number of Questions
                  </Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-slate-500 hover:text-slate-300 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-slate-800 border-slate-600 text-slate-200">
                        <p>Optimal quiz length: 5-20 questions</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  type="number"
                  id="num-questions"
                  value={numberOfQuizQuestions}
                  onChange={(e) =>
                    setNumberOfQuizQuestions(
                      Math.max(1, parseInt(e.target.value, 10) || 1)
                    )
                  }
                  min="1"
                  max="50"
                  className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}
                  disabled={isGeneratingAnything || !isAIConfigured}
                />
              </div>

              <div>
                <Label className={textSecondaryClass}>Question Types</Label>
                <div className="mt-2 space-y-2">
                  {QUESTION_TYPE_OPTIONS.map((type) => (
                    <div key={type.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`upload-type-${type.id}`}
                        checked={quizQuestionTypes.includes(type.id)}
                        onCheckedChange={() => handleQuizQuestionTypeChange(type.id)}
                        disabled={isGeneratingAnything || !isAIConfigured}
                        className="border-slate-500 data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"
                      />
                      <Label
                        htmlFor={`upload-type-${type.id}`}
                        className={`font-normal ${textSecondaryClass} cursor-pointer`}
                      >
                        {type.label}
                      </Label>
                    </div>
                  ))}
                </div>
                {quizQuestionTypes.length === 0 && (
                  <p className="text-xs text-red-400 mt-1">Please select at least one question type.</p>
                )}
              </div>

              {/* Custom Prompt for Quiz */}
              <div className="space-y-2">
                <Label htmlFor="quiz-custom-prompt" className={textSecondaryClass}>
                  Custom Prompt (Optional)
                </Label>
                <Textarea
                  id="quiz-custom-prompt"
                  value={quizCustomPrompt}
                  onChange={(e) => setQuizCustomPrompt(e.target.value)}
                  placeholder="e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'"
                  rows={3}
                  className="!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500"
                  disabled={isGeneratingAnything || !isAIConfigured}
                />
                <p className="text-xs text-purple-400">
                  Add specific instructions for the AI on what kind of questions you want.
                </p>
              </div>

              <Button
                onClick={handleGenerateQuizClick}
                disabled={isGeneratingAnything || !isAIConfigured}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2.5 text-sm font-medium flex items-center justify-center"
              >
              {isGeneratingQuiz ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>
                  Generating Quiz...
                </>
              ) : (
                <>
                  <ListChecks className="mr-2 h-4 w-4" />
                  Generate Quiz
                </>
              )}
            </Button>
            </div>
          </div>
        )}
        {!isAIConfigured &&
          extractedText &&
          !isGeneratingAnything && (
            <Alert
              variant="default"
              className={`bg-yellow-900/30 border-${warningColor} text-${warningColor}`}
            >
              <AlertCircle className={`h-4 w-4 text-${warningColor}`} />
              <AlertTitle className={`text-${warningColor}`}>
                Configuration Needed
              </AlertTitle>
              <AlertDescription className={`text-yellow-300`}>
                Please configure your AI Provider in settings before generating
                content.
              </AlertDescription>
            </Alert>
          )}

        {Array.isArray(flashcards) &&
          flashcards.length > 0 &&
          !isGeneratingFlashcards && (
            <FlashcardViewer flashcards={flashcards} />
          )}

        {generatedQuiz && !isGeneratingQuiz && (
          <Alert
            variant="default"
            className={`bg-green-900/30 border-green-500 text-green-300 mt-4`}
          >
            <HelpCircleIcon className={`h-4 w-4 text-green-400`} />
            <AlertTitle className={`text-green-400`}>
              Quiz Generated!
            </AlertTitle>
            <AlertDescription className={`text-green-200`}>
              {generatedQuiz.name} is ready.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default UploadSection;
