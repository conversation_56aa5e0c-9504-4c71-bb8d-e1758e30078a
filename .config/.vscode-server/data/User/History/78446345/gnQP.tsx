import React, { useState, FormEvent, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import Spinner from "@/components/ui/Spinner";
import { Switch } from "@/components/ui/switch";
import { useToast } from "../../hooks/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AiQuizGenerationOptions,
  GenerationOptions,
} from "./AiQuizGenerationOptions";
import {
  generateAiQuizAPI,
  GenerateAiQuizApiResponse,
  createQuizAPI,
  CreateQuizApiResponse,
  GenerateAiQuizApiPayload,
} from "../../lib/api";
import { Tables } from "@/types/supabase";
import { getAIProviderSettings } from "@/lib/ai-provider";
import { AIProviderConfig } from "@shared/types/quiz";

interface CreateQuizFormProps {
  studyDocumentId?: string;
  onQuizCreated: (quizId: string, quizName: string) => void;
}

export const CreateQuizForm: React.FC<CreateQuizFormProps> = ({
  studyDocumentId,
  onQuizCreated,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false); // For manual creation
  const [isGeneratingAiQuiz, setIsGeneratingAiQuiz] = useState(false); // For AI generation
  const [error, setError] = useState<string | null>(null);
  const [isAiMode, setIsAiMode] = useState(false);

  // State for AI Mode specific inputs
  const [availableDocuments, setAvailableDocuments] = useState<
    Tables<"study_documents">[]
  >([]);
  const [aiSelectedDocIds, setAiSelectedDocIds] = useState<string[]>([]);
  const [customPrompt, setCustomPrompt] = useState<string>("");
  const [loadingAiDocs, setLoadingAiDocs] = useState(false);
  const [generationOptions, setGenerationOptions] = useState<GenerationOptions>(
    {
      numberOfQuestions: 5,
      questionTypes: ["multiple_choice"], // Fixed: use underscore to match UI options
    }
  );

  useEffect(() => {
    const fetchDocumentsForAI = async () => {
      if (isAiMode && user) {
        setLoadingAiDocs(true);
        try {
          const { data, error: dbError } = await supabase
            .from("study_documents")
            .select("id, file_name, created_at")
            .eq("user_id", user.id)
            .eq("status", "extracted") // Only use documents with extracted text
            .order("created_at", { ascending: false });

          if (dbError) throw dbError;
          // Cast the data to the expected type to satisfy TypeScript
          setAvailableDocuments(data as unknown as Tables<"study_documents">[] || []);
        } catch (err: any) {
          console.error("Error fetching documents for AI quiz:", err);
          toast({
            title: "Error",
            description: "Could not load documents for AI quiz.",
            variant: "destructive",
          });
          setAvailableDocuments([]);
        } finally {
          setLoadingAiDocs(false);
        }
      } else {
        setAvailableDocuments([]); // Clear if not in AI mode or no user
      }
    };

    fetchDocumentsForAI();
  }, [isAiMode, user, toast]);

  const handleManualSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!user) {
      setError("You must be logged in to create a quiz.");
      toast({
        title: "Error",
        description: "You must be logged in.",
        variant: "destructive",
      });
      return;
    }
    if (!name.trim()) {
      setError("Quiz name is required.");
      toast({
        title: "Error",
        description: "Quiz name is required.",
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const payload = {
        name: name.trim(),
        description: description || undefined,
        // study_document_id: effectiveDocumentIds.length > 0 ? effectiveDocumentIds[0] : undefined, // Manual creation might not link to docs this way
      };

      const result: CreateQuizApiResponse = await createQuizAPI(payload);

      toast({
        title: "Success!",
        description: `Quiz "${result.name}" created successfully.`,
      });
      queryClient.invalidateQueries({ queryKey: ["quizzes"] });
      onQuizCreated(result.id, result.name);
      setName("");
      setDescription("");
    } catch (err: any) {
      const errorMessage = err.message || "Failed to create quiz.";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAiGenerateQuiz = async () => {
    if (!user) {
      setError("You must be logged in to generate an AI quiz.");
      toast({
        title: "Error",
        description: "You must be logged in.",
        variant: "destructive",
      });
      return;
    }
    if (aiSelectedDocIds.length === 0) {
      setError(
        "At least one study document must be selected to generate an AI quiz."
      );
      toast({
        title: "Error",
        description: "Please select at least one document.",
        variant: "destructive",
      });
      return;
    }
    if (!name.trim()) {
      setError("Quiz name is required for AI generation as well.");
      toast({
        title: "Error",
        description: "Quiz name is required.",
        variant: "destructive",
      });
      return;
    }
    if (generationOptions.questionTypes.length === 0) {
      setError("Please select at least one question type for AI generation.");
      toast({
        title: "Error",
        description: "Select at least one question type.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingAiQuiz(true);
    setError(null);
    try {
      const payload: GenerateAiQuizApiPayload = {
        documentIds: aiSelectedDocIds,
        quizName: name,
        customPrompt: customPrompt || undefined,
        quizDescription: description,
        generationOptions,
        // aiConfig removed - credentials are retrieved from secure backend storage
      };

      // Debug logging
      console.log("Frontend: Sending AI quiz generation payload:", payload);
      console.log("Frontend: Question types being sent:", generationOptions.questionTypes);

      const result: GenerateAiQuizApiResponse = await generateAiQuizAPI(
        payload
      );
      toast({
        title: "Success!",
        description: `AI Quiz "${result.name}" generated successfully.`,
      });
      queryClient.invalidateQueries({ queryKey: ["quizzes"] });
      onQuizCreated(result.id, result.name);
      // Reset form
      setName("");
      setDescription("");
      setGenerationOptions({
        numberOfQuestions: 5,
        questionTypes: ["multiple_choice"], // Fixed: use underscore to match UI options
      });
      setAiSelectedDocIds([]);
      setCustomPrompt("");
      setIsAiMode(false); // Optionally switch back to manual or stay in AI mode
    } catch (err: any) {
      const errorMessage = err.message || "Failed to generate AI quiz.";
      setError(errorMessage);
      toast({
        title: "AI Generation Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAiQuiz(false);
    }
  };

  const handleAiDocumentSelection = (documentId: string) => {
    setAiSelectedDocIds((prev) => {
      if (prev.includes(documentId)) {
        return prev.filter((id) => id !== documentId);
      } else {
        return [...prev, documentId];
      }
    });
  };

  return (
    <div className="bg-slate-800 shadow-md rounded-lg p-4 md:p-6 mt-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-purple-400">
          {isAiMode ? "Generate More Questions with AI" : "Create New Quiz Manually"}
        </h3>
        <div className="flex items-center space-x-2">
          <Label htmlFor="ai-mode-switch" className="text-purple-300">
            Use AI
          </Label>
          <Switch
            id="ai-mode-switch"
            checked={isAiMode}
            onCheckedChange={setIsAiMode}
            disabled={isGeneratingAiQuiz || loading}
          />
        </div>
      </div>

      {error && (
        <p className="text-red-400 text-sm mb-3 bg-red-900/30 p-2 rounded-md">
          {error}
        </p>
      )}

      {/* Common fields for both modes */}
      <form
        onSubmit={isAiMode ? (e) => e.preventDefault() : handleManualSubmit}
        className="space-y-4"
      >
        <div>
          <Label htmlFor="quizName" className="text-purple-300">
            Quiz Name*
          </Label>
          <Input
            id="quizName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g. Chapter 1 Review"
            required
            className="mt-1 bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus-visible:ring-purple-500"
            disabled={loading || isGeneratingAiQuiz}
          />
        </div>
        <div>
          <Label htmlFor="quizDescription" className="text-purple-300">
            Description (Optional)
          </Label>
          <Textarea
            id="quizDescription"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
            className="input-class mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500" // Added w-full and basic styling
            placeholder="Short description of this quiz..."
            disabled={loading || isGeneratingAiQuiz}
          />
        </div>

        {!isAiMode && (
          <Button
            type="submit"
            variant="outline"
            disabled={loading || isGeneratingAiQuiz}
            className="w-full"
          >
            {loading && <Spinner size="sm" />}
            {loading ? "Creating..." : "Create Quiz Manually"}
          </Button>
        )}
      </form>

      {isAiMode && (
        <div className="mt-6 pt-4 border-t border-slate-700 space-y-4">
          <div>
            <Label className="text-purple-300 mb-2 block">
              Select Documents for AI Generation
            </Label>
            {loadingAiDocs ? (
              <div className="flex justify-center items-center py-4">
                <Spinner size="sm" />{" "}
                <span className="ml-2 text-purple-300">
                  Loading documents...
                </span>
              </div>
            ) : availableDocuments.length === 0 ? (
              <p className="text-sm text-purple-300 p-3 bg-slate-700 rounded-md">
                No extracted documents available. Please upload and process
                documents first.
              </p>
            ) : (
              <div className="max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600">
                {availableDocuments.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors"
                  >
                    <Checkbox
                      id={`ai-doc-${doc.id}`}
                      checked={aiSelectedDocIds.includes(doc.id)}
                      onCheckedChange={() => handleAiDocumentSelection(doc.id)}
                      disabled={isGeneratingAiQuiz}
                    />
                    <Label
                      htmlFor={`ai-doc-${doc.id}`}
                      className="font-normal text-purple-300 cursor-pointer flex-1 truncate"
                      title={doc.file_name}
                    >
                      {doc.file_name}
                    </Label>
                  </div>
                ))}
              </div>
            )}
            {aiSelectedDocIds.length > 0 && (
              <p className="text-xs text-purple-400 mt-1">
                {aiSelectedDocIds.length} document(s) selected.
              </p>
            )}
          </div>

          <div>
            <Label htmlFor="customPrompt" className="text-purple-300">
              Custom Prompt (Optional)
            </Label>
            <Textarea
              id="customPrompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'"
              rows={3}
              className="input-class mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500"
              disabled={isGeneratingAiQuiz}
            />
            <p className="text-xs text-purple-400 mt-1">
              Add specific instructions for the AI on what kind of questions you
              want.
            </p>
          </div>

          <AiQuizGenerationOptions
            generationOptions={generationOptions}
            setGenerationOptions={setGenerationOptions}
            isGenerating={isGeneratingAiQuiz}
            onGenerate={handleAiGenerateQuiz}
            documentIds={aiSelectedDocIds} // Pass selected IDs to enable/disable generate button
          />
        </div>
      )}
    </div>
  );
};
