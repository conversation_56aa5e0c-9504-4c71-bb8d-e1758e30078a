import React, { useState } from "react";
// Removed direct Supabase import - using backend API endpoints via useAuth
import { useAuth } from "../../hooks/useAuth";
import styles from "./authStyles.module.css";

export const SignInForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  const handleSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setMessage(null);
    setLoading(true);
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) throw error;
      setMessage("Signed in successfully! You will be redirected shortly.");
      // Typically, redirection is handled by the AuthProvider or a router watching auth state
    } catch (error: any) {
      setError(error.error_description || error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`max-w-md mx-auto mt-10 p-6 rounded-lg shadow-md ${styles.formContainer}`}>
      <h2 className={`text-2xl font-semibold text-center mb-6 ${styles.formTitle}`}>
        Sign In
      </h2>
      {error && (
        <p className="text-red-400 text-sm text-center mb-4 p-2 bg-red-900/30 rounded-md">{error}</p>
      )}
      {message && (
        <p className="text-green-400 text-sm text-center mb-4 p-2 bg-green-900/30 rounded-md">{message}</p>
      )}
      <form onSubmit={handleSignIn}>
        <div className="mb-4">
          <label
            htmlFor="email"
            className="block text-purple-300 text-sm font-bold mb-2"
          >
            Email
          </label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className={`shadow appearance-none border rounded w-full py-3 px-4 leading-tight ${styles.darkInput}`}
          />
        </div>
        <div className="mb-6">
          <label
            htmlFor="password"
            className="block text-purple-300 text-sm font-bold mb-2"
          >
            Password
          </label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className={`shadow appearance-none border rounded w-full py-3 px-4 leading-tight mb-3 ${styles.darkInput}`}
          />
        </div>
        <div className="flex items-center justify-between">
          <button
            type="submit"
            disabled={loading}
            className={`font-bold py-2 px-4 rounded ${styles.signInButton}`}
          >
            {loading ? "Signing In..." : "Sign In"}
          </button>
        </div>
      </form>
    </div>
  );
};
