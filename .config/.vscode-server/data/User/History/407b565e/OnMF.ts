import { useEffect, useState } from "react";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "./useAuth";
import { Tables } from "../types/supabase";
import { getQuizQuestionsDueForReview } from "@/lib/srs";

type QuizQuestion = Tables<"quiz_questions">;

export interface SRSStats {
  totalQuestions: number;
  dueForReview: number;
  reviewedToday: number;
  averageSRSLevel: number;
  nextReviewDate: string | null;
  streakDays: number;
  loading: boolean;
  error: string | null;
}

export const useSRSStats = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<SRSStats>({
    totalQuestions: 0,
    dueForReview: 0,
    reviewedToday: 0,
    averageSRSLevel: 0,
    nextReviewDate: null,
    streakDays: 0,
    loading: true,
    error: null,
  });

  const fetchSRSStats = async () => {
    if (!user) {
      setStats(prev => ({ ...prev, loading: false }));
      return;
    }

    setStats(prev => ({ ...prev, loading: true, error: null }));

    try {
      // Fetch quiz questions from backend API
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/quiz-questions', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch quiz questions');
      }

      const questionsData = await response.json();

      const allQuestions = questionsData || [];

      // Calculate SRS statistics
      const dueQuestions = getQuizQuestionsDueForReview(allQuestions);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const reviewedToday = allQuestions.filter((q: any) => {
        if (!q.last_reviewed_at) return false;
        const reviewDate = new Date(q.last_reviewed_at);
        reviewDate.setHours(0, 0, 0, 0);
        return reviewDate.getTime() === today.getTime();
      }).length;

      const averageSRSLevel = allQuestions.length > 0
        ? allQuestions.reduce((sum: number, q: any) => sum + (q.srs_level || 0), 0) / allQuestions.length
        : 0;

      // Find next review date
      const futureReviews = allQuestions
        .filter((q: any) => q.due_at && new Date(q.due_at) > new Date())
        .sort((a: any, b: any) => new Date(a.due_at!).getTime() - new Date(b.due_at!).getTime());

      const nextReviewDate = futureReviews.length > 0 ? futureReviews[0].due_at : null;

      // Calculate streak (simplified - days with reviews in the last week)
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      const recentReviews = allQuestions.filter((q: any) =>
        q.last_reviewed_at && new Date(q.last_reviewed_at) >= weekAgo
      );
      const uniqueReviewDays = new Set(
        recentReviews.map((q: any) => new Date(q.last_reviewed_at!).toDateString())
      );

      setStats({
        totalQuestions: allQuestions.length,
        dueForReview: dueQuestions.length,
        reviewedToday,
        averageSRSLevel: Math.round(averageSRSLevel * 10) / 10,
        nextReviewDate,
        streakDays: uniqueReviewDays.size,
        loading: false,
        error: null,
      });

    } catch (error: any) {
      console.error("Error fetching SRS data:", error);
      setStats(prev => ({
        ...prev,
        loading: false,
        error: error.message || "Failed to load SRS statistics",
      }));
    }
  };

  useEffect(() => {
    fetchSRSStats();
  }, [user]);

  return {
    stats,
    refreshStats: fetchSRSStats,
  };
}; 