# Development & Documentation
- Always maintain and update markdown files in /docs/ folder with accurate, specific information about the codebase, keeping documentation current with any changes made.
- Development server debugging: Port conflicts resolved by killing all npm/tsx/vite processes, waiting for port release, then restarting - backend changes require server restart to apply fixes.

# Security
- Backend should handle all AI provider credentials securely by retrieving encrypted keys from database during API calls, never transmitting API keys to frontend.
- Frontend should check backend for credential existence rather than localStorage.
- Frontend should never directly access Supabase - all database operations must go through Express.js backend API endpoints with JWT authentication, storing tokens in localStorage and using Authorization headers for secure communication.

# Architecture
- Architecture should use backend-only Supabase with no direct frontend database access - all Supabase operations must go through Express.js API endpoints, eliminating any Supabase client usage or console logs from React frontend.
- Flashcard system architecture: Current Supabase schema only includes basic columns (front_text, back_text, set_id, user_id, created_at, updated_at) without SRS fields - any SRS implementation requires schema migration or separate tracking system.

# Deployment
- ChewyAI production setup completed with security-first architecture, optimized builds, proper environment variable handling, comprehensive documentation, and successful Replit deployment configuration.

# Bug Fixes
- Critical flashcard system debugging completed: Fixed database schema mismatch causing 500 errors in /api/flashcard-sets/{id}/due endpoint by removing queries for non-existent SRS columns like due_at, updated card count calculation, and restored full flashcard review functionality.
`