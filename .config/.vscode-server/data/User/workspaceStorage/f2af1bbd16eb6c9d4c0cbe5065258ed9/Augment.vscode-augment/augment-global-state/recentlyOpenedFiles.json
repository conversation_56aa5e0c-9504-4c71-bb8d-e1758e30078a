[["/home/<USER>/workspace/docs/SECURITY_AUDIT_REPORT.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/SECURITY_AUDIT_REPORT.md"}}], ["/home/<USER>/workspace/client/.env.example", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/.env.example"}}], ["/home/<USER>/workspace/docs/SECURITY_MIGRATION_PLAN.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/SECURITY_MIGRATION_PLAN.md"}}], ["/home/<USER>/workspace/client/src/hooks/useDocuments.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useDocuments.tsx"}}], ["/home/<USER>/workspace/client/src/lib/file-parser.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/file-parser.ts"}}], ["/home/<USER>/workspace/client/src/lib/api/quizApi.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/api/quizApi.ts"}}], ["/home/<USER>/workspace/client/src/lib/storage.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/storage.ts"}}], ["/home/<USER>/workspace/client/src/pages/DashboardPage.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/DashboardPage.tsx"}}], ["/home/<USER>/workspace/client/src/components/quiz/QuizQuestionManager.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizQuestionManager.tsx"}}], ["/home/<USER>/workspace/client/src/lib/api.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/api.ts"}}], ["/home/<USER>/workspace/client/src/pages/FlashcardEditPage.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardEditPage.tsx"}}], ["/home/<USER>/workspace/client/src/pages/FlashcardReview.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardReview.tsx"}}], ["/home/<USER>/workspace/client/src/components/document/InlineDocumentViewer.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/InlineDocumentViewer.tsx"}}], ["/home/<USER>/workspace/client/src/components/document/DocumentViewer.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/DocumentViewer.tsx"}}]]