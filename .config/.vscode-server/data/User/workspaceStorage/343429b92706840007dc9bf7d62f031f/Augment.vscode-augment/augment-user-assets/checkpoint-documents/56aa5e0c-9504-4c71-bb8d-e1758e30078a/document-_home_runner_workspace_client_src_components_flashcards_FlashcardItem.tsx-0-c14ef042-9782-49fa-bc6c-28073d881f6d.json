{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardItem.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport { Flashcard as FlashcardType } from \"@shared/types/flashcards\"; // Using the shared type\nimport {\n  <PERSON>,\n  CardContent,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n} from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipP<PERSON>ider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport { RotateCw } from \"lucide-react\"; // For a flip icon\n\ninterface FlashcardItemProps {\n  flashcard: FlashcardType;\n  showAnswerOverride?: boolean; // For quiz mode or always show answer\n}\n\nconst FlashcardItem: React.FC<FlashcardItemProps> = ({\n  flashcard,\n  showAnswerOverride,\n}) => {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const handleFlip = () => {\n    if (showAnswerOverride === undefined) {\n      // Only allow manual flip if not overridden\n      setIsFlipped(!isFlipped);\n    }\n  };\n\n  const shouldShowAnswer =\n    showAnswerOverride !== undefined ? showAnswerOverride : isFlipped;\n\n  return (\n    <Card\n      className={`w-full max-w-md h-80 perspective-1000 cursor-pointer \n                    transition-transform duration-700 transform-style-preserve-3d \n                    ${shouldShowAnswer ? \"rotate-y-180\" : \"\"}\n                    bg-purple-800 bg-opacity-20 backdrop-blur-md border-purple-700 text-slate-100\n                  `}\n      onClick={handleFlip}\n    >\n      {/* Front of the card (Question) */}\n      <div\n        className={`absolute w-full h-full backface-hidden \n                      ${shouldShowAnswer ? \"opacity-0\" : \"opacity-100\"} \n                      transition-opacity duration-300 delay-300`}\n      >\n        <CardHeader className=\"h-1/4\">\n          <CardTitle className=\"text-lg font-semibold text-purple-400\">\n            Question\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"h-3/4 flex items-center justify-center p-6\">\n          <p className=\"text-xl text-center text-slate-50\">\n            {flashcard.question}\n          </p>\n        </CardContent>\n      </div>\n\n      {/* Back of the card (Answer) */}\n      <div\n        className={`absolute w-full h-full backface-hidden rotate-y-180 \n                      ${shouldShowAnswer ? \"opacity-100\" : \"opacity-0\"}\n                      transition-opacity duration-300 delay-300`}\n      >\n        <CardHeader className=\"h-1/4\">\n          <CardTitle className=\"text-lg font-semibold text-green-400\">\n            Answer\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"h-3/4 flex items-center justify-center p-6\">\n          <p className=\"text-xl text-center text-slate-50\">\n            {flashcard.answer}\n          </p>\n        </CardContent>\n      </div>\n\n      {showAnswerOverride === undefined && (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleFlip();\n                }}\n                className=\"absolute bottom-4 right-4 text-purple-300 hover:text-purple-100 z-10\"\n                aria-label={shouldShowAnswer ? \"Show question\" : \"Show answer\"}\n              >\n                <RotateCw size={20} />\n              </Button>\n            </TooltipTrigger>\n            <TooltipContent\n              side=\"left\"\n              className=\"bg-slate-800 border-slate-600 text-slate-200\"\n            >\n              <p>{shouldShowAnswer ? \"Show question\" : \"Show answer\"}</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )}\n    </Card>\n  );\n};\n\nexport default FlashcardItem;\n", "modifiedCode": "import React, { useState } from \"react\";\nimport { Flashcard as FlashcardType } from \"@shared/types/flashcards\"; // Using the shared type\nimport {\n  <PERSON>,\n  CardContent,\n  CardHeader,\n  CardTitle,\n  CardDescription,\n} from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipP<PERSON>ider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport { RotateCw } from \"lucide-react\"; // For a flip icon\n\ninterface FlashcardItemProps {\n  flashcard: FlashcardType;\n  showAnswerOverride?: boolean; // For quiz mode or always show answer\n}\n\nconst FlashcardItem: React.FC<FlashcardItemProps> = ({\n  flashcard,\n  showAnswerOverride,\n}) => {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const handleFlip = () => {\n    if (showAnswerOverride === undefined) {\n      // Only allow manual flip if not overridden\n      setIsFlipped(!isFlipped);\n    }\n  };\n\n  const shouldShowAnswer =\n    showAnswerOverride !== undefined ? showAnswerOverride : isFlipped;\n\n  return (\n    <Card\n      className={`w-full max-w-md h-80 perspective-1000 cursor-pointer \n                    transition-transform duration-700 transform-style-preserve-3d \n                    ${shouldShowAnswer ? \"rotate-y-180\" : \"\"}\n                    bg-purple-800 bg-opacity-20 backdrop-blur-md border-purple-700 text-slate-100\n                  `}\n      onClick={handleFlip}\n    >\n      {/* Front of the card (Question) */}\n      <div\n        className={`absolute w-full h-full backface-hidden \n                      ${shouldShowAnswer ? \"opacity-0\" : \"opacity-100\"} \n                      transition-opacity duration-300 delay-300`}\n      >\n        <CardHeader className=\"h-1/4\">\n          <CardTitle className=\"text-lg font-semibold text-purple-400\">\n            Question\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"h-3/4 flex items-center justify-center p-6\">\n          <p className=\"text-xl text-center text-slate-50\">\n            {flashcard.question}\n          </p>\n        </CardContent>\n      </div>\n\n      {/* Back of the card (Answer) */}\n      <div\n        className={`absolute w-full h-full backface-hidden rotate-y-180 \n                      ${shouldShowAnswer ? \"opacity-100\" : \"opacity-0\"}\n                      transition-opacity duration-300 delay-300`}\n      >\n        <CardHeader className=\"h-1/4\">\n          <CardTitle className=\"text-lg font-semibold text-green-400\">\n            Answer\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"h-3/4 flex items-center justify-center p-6\">\n          <p className=\"text-xl text-center text-slate-50\">\n            {flashcard.answer}\n          </p>\n        </CardContent>\n      </div>\n\n      {showAnswerOverride === undefined && (\n        <TooltipProvider>\n          <Tooltip>\n            <TooltipTrigger asChild>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleFlip();\n                }}\n                className=\"absolute bottom-4 right-4 text-purple-300 hover:text-purple-100 z-10\"\n                aria-label={shouldShowAnswer ? \"Show question\" : \"Show answer\"}\n              >\n                <RotateCw size={20} />\n              </Button>\n            </TooltipTrigger>\n            <TooltipContent\n              side=\"left\"\n              className=\"bg-slate-800 border-slate-600 text-slate-200\"\n            >\n              <p>{shouldShowAnswer ? \"Show question\" : \"Show answer\"}</p>\n            </TooltipContent>\n          </Tooltip>\n        </TooltipProvider>\n      )}\n    </Card>\n  );\n};\n\nexport default FlashcardItem;\n"}