{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizSettingsToggle.tsx"}, "originalCode": "import React from 'react';\nimport { Settings } from 'lucide-react';\nimport { useQuizSettings } from '@/contexts/QuizSettingsContext';\nimport { Button } from '@/components/ui/button';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover';\n\ninterface QuizSettingsToggleProps {\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'default' | 'ghost' | 'outline';\n  showLabel?: boolean;\n}\n\nexport const QuizSettingsToggle: React.FC<QuizSettingsToggleProps> = ({ \n  size = 'md', \n  variant = 'ghost',\n  showLabel = false \n}) => {\n  const { settings, updateSetting } = useQuizSettings();\n\n  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;\n\n  return (\n    <Popover>\n      <PopoverTrigger asChild>\n        <Button\n          variant={variant}\n          size={size}\n          className=\"transition-all duration-200 hover:scale-105\"\n          aria-label=\"Quiz Settings\"\n        >\n          <Settings size={iconSize} />\n          {showLabel && <span className=\"ml-2\">Quiz Settings</span>}\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-80 bg-slate-800 border-slate-700\" align=\"end\">\n        <Card className=\"bg-transparent border-0 shadow-none\">\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-lg text-slate-200 flex items-center gap-2\">\n              <Settings className=\"h-5 w-5 text-purple-400\" />\n              Quiz Settings\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"instant-feedback\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Instant Feedback</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Show correct/incorrect answers immediately\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"instant-feedback\"\n                checked={settings.instantFeedback}\n                onCheckedChange={(checked) => updateSetting('instantFeedback', checked)}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"show-correct\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Show Correct Answers</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Highlight correct options when instant feedback is on\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"show-correct\"\n                checked={settings.showCorrectAnswers}\n                onCheckedChange={(checked) => updateSetting('showCorrectAnswers', checked)}\n                disabled={!settings.instantFeedback}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"auto-advance\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Auto Advance</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Automatically move to next question after submission\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"auto-advance\"\n                checked={settings.autoAdvance}\n                onCheckedChange={(checked) => updateSetting('autoAdvance', checked)}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"show-explanations\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Show Explanations</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Display explanations after answering questions\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"show-explanations\"\n                checked={settings.showExplanations}\n                onCheckedChange={(checked) => updateSetting('showExplanations', checked)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n      </PopoverContent>\n    </Popover>\n  );\n};\n", "modifiedCode": "import React from 'react';\nimport { Settings } from 'lucide-react';\nimport { useQuizSettings } from '@/contexts/QuizSettingsContext';\nimport { Button } from '@/components/ui/button';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover';\n\ninterface QuizSettingsToggleProps {\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'default' | 'ghost' | 'outline';\n  showLabel?: boolean;\n}\n\nexport const QuizSettingsToggle: React.FC<QuizSettingsToggleProps> = ({ \n  size = 'md', \n  variant = 'ghost',\n  showLabel = false \n}) => {\n  const { settings, updateSetting } = useQuizSettings();\n\n  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;\n\n  return (\n    <Popover>\n      <PopoverTrigger asChild>\n        <Button\n          variant={variant}\n          size={size === \"md\" ? \"default\" : size}\n          className=\"transition-all duration-200 hover:scale-105\"\n          aria-label=\"Quiz Settings\"\n        >\n          <Settings size={iconSize} />\n          {showLabel && <span className=\"ml-2\">Quiz Settings</span>}\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-80 bg-slate-800 border-slate-700\" align=\"end\">\n        <Card className=\"bg-transparent border-0 shadow-none\">\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-lg text-slate-200 flex items-center gap-2\">\n              <Settings className=\"h-5 w-5 text-purple-400\" />\n              Quiz Settings\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"instant-feedback\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Instant Feedback</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Show correct/incorrect answers immediately\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"instant-feedback\"\n                checked={settings.instantFeedback}\n                onCheckedChange={(checked) => updateSetting('instantFeedback', checked)}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"show-correct\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Show Correct Answers</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Highlight correct options when instant feedback is on\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"show-correct\"\n                checked={settings.showCorrectAnswers}\n                onCheckedChange={(checked) => updateSetting('showCorrectAnswers', checked)}\n                disabled={!settings.instantFeedback}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"auto-advance\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Auto Advance</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Automatically move to next question after submission\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"auto-advance\"\n                checked={settings.autoAdvance}\n                onCheckedChange={(checked) => updateSetting('autoAdvance', checked)}\n              />\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <Label htmlFor=\"show-explanations\" className=\"text-slate-300 flex-1\">\n                <div>\n                  <div className=\"font-medium\">Show Explanations</div>\n                  <div className=\"text-xs text-slate-400\">\n                    Display explanations after answering questions\n                  </div>\n                </div>\n              </Label>\n              <Switch\n                id=\"show-explanations\"\n                checked={settings.showExplanations}\n                onCheckedChange={(checked) => updateSetting('showExplanations', checked)}\n              />\n            </div>\n          </CardContent>\n        </Card>\n      </PopoverContent>\n    </Popover>\n  );\n};\n"}