{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useSRSStats.ts"}, "originalCode": "import { useEffect, useState } from \"react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"./useAuth\";\nimport { Tables } from \"../types/supabase\";\nimport { getQuizQuestionsDueForReview } from \"@/lib/srs\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\nexport interface SRSStats {\n  totalQuestions: number;\n  dueForReview: number;\n  reviewedToday: number;\n  averageSRSLevel: number;\n  nextReviewDate: string | null;\n  streakDays: number;\n  loading: boolean;\n  error: string | null;\n}\n\nexport const useSRSStats = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<SRSStats>({\n    totalQuestions: 0,\n    dueForReview: 0,\n    reviewedToday: 0,\n    averageSRSLevel: 0,\n    nextReviewDate: null,\n    streakDays: 0,\n    loading: true,\n    error: null,\n  });\n\n  const fetchSRSStats = async () => {\n    if (!user) {\n      setStats(prev => ({ ...prev, loading: false }));\n      return;\n    }\n\n    setStats(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const { data: questionsData, error } = await supabase\n        .from(\"quiz_questions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: true });\n\n      if (error) throw error;\n\n      const allQuestions = questionsData || [];\n\n      // Calculate SRS statistics\n      const dueQuestions = getQuizQuestionsDueForReview(allQuestions);\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      const reviewedToday = allQuestions.filter(q => {\n        if (!q.last_reviewed_at) return false;\n        const reviewDate = new Date(q.last_reviewed_at);\n        reviewDate.setHours(0, 0, 0, 0);\n        return reviewDate.getTime() === today.getTime();\n      }).length;\n\n      const averageSRSLevel = allQuestions.length > 0\n        ? allQuestions.reduce((sum, q) => sum + (q.srs_level || 0), 0) / allQuestions.length\n        : 0;\n\n      // Find next review date\n      const futureReviews = allQuestions\n        .filter(q => q.due_at && new Date(q.due_at) > new Date())\n        .sort((a, b) => new Date(a.due_at!).getTime() - new Date(b.due_at!).getTime());\n\n      const nextReviewDate = futureReviews.length > 0 ? futureReviews[0].due_at : null;\n\n      // Calculate streak (simplified - days with reviews in the last week)\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const recentReviews = allQuestions.filter(q => \n        q.last_reviewed_at && new Date(q.last_reviewed_at) >= weekAgo\n      );\n      const uniqueReviewDays = new Set(\n        recentReviews.map(q => new Date(q.last_reviewed_at!).toDateString())\n      );\n\n      setStats({\n        totalQuestions: allQuestions.length,\n        dueForReview: dueQuestions.length,\n        reviewedToday,\n        averageSRSLevel: Math.round(averageSRSLevel * 10) / 10,\n        nextReviewDate,\n        streakDays: uniqueReviewDays.size,\n        loading: false,\n        error: null,\n      });\n\n    } catch (error: any) {\n      console.error(\"Error fetching SRS data:\", error);\n      setStats(prev => ({\n        ...prev,\n        loading: false,\n        error: error.message || \"Failed to load SRS statistics\",\n      }));\n    }\n  };\n\n  useEffect(() => {\n    fetchSRSStats();\n  }, [user]);\n\n  return {\n    stats,\n    refreshStats: fetchSRSStats,\n  };\n}; ", "modifiedCode": "import { useEffect, useState } from \"react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"./useAuth\";\nimport { Tables } from \"../types/supabase\";\nimport { getQuizQuestionsDueForReview } from \"@/lib/srs\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\nexport interface SRSStats {\n  totalQuestions: number;\n  dueForReview: number;\n  reviewedToday: number;\n  averageSRSLevel: number;\n  nextReviewDate: string | null;\n  streakDays: number;\n  loading: boolean;\n  error: string | null;\n}\n\nexport const useSRSStats = () => {\n  const { user } = useAuth();\n  const [stats, setStats] = useState<SRSStats>({\n    totalQuestions: 0,\n    dueForReview: 0,\n    reviewedToday: 0,\n    averageSRSLevel: 0,\n    nextReviewDate: null,\n    streakDays: 0,\n    loading: true,\n    error: null,\n  });\n\n  const fetchSRSStats = async () => {\n    if (!user) {\n      setStats(prev => ({ ...prev, loading: false }));\n      return;\n    }\n\n    setStats(prev => ({ ...prev, loading: true, error: null }));\n\n    try {\n      const { data: questionsData, error } = await supabase\n        .from(\"quiz_questions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: true });\n\n      if (error) throw error;\n\n      const allQuestions = questionsData || [];\n\n      // Calculate SRS statistics\n      const dueQuestions = getQuizQuestionsDueForReview(allQuestions);\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      const reviewedToday = allQuestions.filter(q => {\n        if (!q.last_reviewed_at) return false;\n        const reviewDate = new Date(q.last_reviewed_at);\n        reviewDate.setHours(0, 0, 0, 0);\n        return reviewDate.getTime() === today.getTime();\n      }).length;\n\n      const averageSRSLevel = allQuestions.length > 0\n        ? allQuestions.reduce((sum, q) => sum + (q.srs_level || 0), 0) / allQuestions.length\n        : 0;\n\n      // Find next review date\n      const futureReviews = allQuestions\n        .filter(q => q.due_at && new Date(q.due_at) > new Date())\n        .sort((a, b) => new Date(a.due_at!).getTime() - new Date(b.due_at!).getTime());\n\n      const nextReviewDate = futureReviews.length > 0 ? futureReviews[0].due_at : null;\n\n      // Calculate streak (simplified - days with reviews in the last week)\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const recentReviews = allQuestions.filter(q => \n        q.last_reviewed_at && new Date(q.last_reviewed_at) >= weekAgo\n      );\n      const uniqueReviewDays = new Set(\n        recentReviews.map(q => new Date(q.last_reviewed_at!).toDateString())\n      );\n\n      setStats({\n        totalQuestions: allQuestions.length,\n        dueForReview: dueQuestions.length,\n        reviewedToday,\n        averageSRSLevel: Math.round(averageSRSLevel * 10) / 10,\n        nextReviewDate,\n        streakDays: uniqueReviewDays.size,\n        loading: false,\n        error: null,\n      });\n\n    } catch (error: any) {\n      console.error(\"Error fetching SRS data:\", error);\n      setStats(prev => ({\n        ...prev,\n        loading: false,\n        error: error.message || \"Failed to load SRS statistics\",\n      }));\n    }\n  };\n\n  useEffect(() => {\n    fetchSRSStats();\n  }, [user]);\n\n  return {\n    stats,\n    refreshStats: fetchSRSStats,\n  };\n}; "}