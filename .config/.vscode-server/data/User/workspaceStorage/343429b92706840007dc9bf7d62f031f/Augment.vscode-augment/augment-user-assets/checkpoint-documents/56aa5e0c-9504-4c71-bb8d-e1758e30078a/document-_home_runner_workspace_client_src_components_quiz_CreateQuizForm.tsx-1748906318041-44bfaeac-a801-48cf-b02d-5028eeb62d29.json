{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/CreateQuizForm.tsx"}, "originalCode": "import React, { useState, FormEvent, useEffect } from \"react\";\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { useToast } from \"../../hooks/use-toast\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport {\n  AiQuizGenerationOptions,\n  GenerationOptions,\n} from \"./AiQuizGenerationOptions\";\nimport {\n  generateAiQuizAPI,\n  GenerateAiQuizApiResponse,\n  createQuizAPI,\n  CreateQuizApiResponse,\n  GenerateAiQuizApiPayload,\n} from \"../../lib/api\";\nimport { Tables } from \"@/types/supabase\";\nimport { getAIProviderSettings } from \"@/lib/ai-provider\";\nimport { AIProviderConfig } from \"@shared/types/quiz\";\n\ninterface CreateQuizFormProps {\n  studyDocumentId?: string;\n  onQuizCreated: (quizId: string, quizName: string) => void;\n}\n\nexport const CreateQuizForm: React.FC<CreateQuizFormProps> = ({\n  studyDocumentId,\n  onQuizCreated,\n}) => {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n  const [name, setName] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false); // For manual creation\n  const [isGeneratingAiQuiz, setIsGeneratingAiQuiz] = useState(false); // For AI generation\n  const [error, setError] = useState<string | null>(null);\n  const [isAiMode, setIsAiMode] = useState(false);\n\n  // State for AI Mode specific inputs\n  const [availableDocuments, setAvailableDocuments] = useState<\n    Tables<\"study_documents\">[]\n  >([]);\n  const [aiSelectedDocIds, setAiSelectedDocIds] = useState<string[]>([]);\n  const [customPrompt, setCustomPrompt] = useState<string>(\"\");\n  const [loadingAiDocs, setLoadingAiDocs] = useState(false);\n  const [generationOptions, setGenerationOptions] = useState<GenerationOptions>(\n    {\n      numberOfQuestions: 5,\n      questionTypes: [\"multiple_choice\"], // Fixed: use underscore to match UI options\n    }\n  );\n\n  useEffect(() => {\n    const fetchDocumentsForAI = async () => {\n      if (isAiMode && user) {\n        setLoadingAiDocs(true);\n        try {\n          const { data, error: dbError } = await supabase\n            .from(\"study_documents\")\n            .select(\"id, file_name, created_at\")\n            .eq(\"user_id\", user.id)\n            .eq(\"status\", \"extracted\") // Only use documents with extracted text\n            .order(\"created_at\", { ascending: false });\n\n          if (dbError) throw dbError;\n          // Cast the data to the expected type to satisfy TypeScript\n          setAvailableDocuments(data as unknown as Tables<\"study_documents\">[] || []);\n        } catch (err: any) {\n          console.error(\"Error fetching documents for AI quiz:\", err);\n          toast({\n            title: \"Error\",\n            description: \"Could not load documents for AI quiz.\",\n            variant: \"destructive\",\n          });\n          setAvailableDocuments([]);\n        } finally {\n          setLoadingAiDocs(false);\n        }\n      } else {\n        setAvailableDocuments([]); // Clear if not in AI mode or no user\n      }\n    };\n\n    fetchDocumentsForAI();\n  }, [isAiMode, user, toast]);\n\n  const handleManualSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    if (!user) {\n      setError(\"You must be logged in to create a quiz.\");\n      toast({\n        title: \"Error\",\n        description: \"You must be logged in.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (!name.trim()) {\n      setError(\"Quiz name is required.\");\n      toast({\n        title: \"Error\",\n        description: \"Quiz name is required.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      const payload = {\n        name: name.trim(),\n        description: description || undefined,\n        // study_document_id: effectiveDocumentIds.length > 0 ? effectiveDocumentIds[0] : undefined, // Manual creation might not link to docs this way\n      };\n\n      const result: CreateQuizApiResponse = await createQuizAPI(payload);\n\n      toast({\n        title: \"Success!\",\n        description: `Quiz \"${result.name}\" created successfully.`,\n      });\n      queryClient.invalidateQueries({ queryKey: [\"quizzes\"] });\n      onQuizCreated(result.id, result.name);\n      setName(\"\");\n      setDescription(\"\");\n    } catch (err: any) {\n      const errorMessage = err.message || \"Failed to create quiz.\";\n      setError(errorMessage);\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\",\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAiGenerateQuiz = async () => {\n    if (!user) {\n      setError(\"You must be logged in to generate an AI quiz.\");\n      toast({\n        title: \"Error\",\n        description: \"You must be logged in.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (aiSelectedDocIds.length === 0) {\n      setError(\n        \"At least one study document must be selected to generate an AI quiz.\"\n      );\n      toast({\n        title: \"Error\",\n        description: \"Please select at least one document.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (!name.trim()) {\n      setError(\"Quiz name is required for AI generation as well.\");\n      toast({\n        title: \"Error\",\n        description: \"Quiz name is required.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (generationOptions.questionTypes.length === 0) {\n      setError(\"Please select at least one question type for AI generation.\");\n      toast({\n        title: \"Error\",\n        description: \"Select at least one question type.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsGeneratingAiQuiz(true);\n    setError(null);\n    try {\n      const payload: GenerateAiQuizApiPayload = {\n        documentIds: aiSelectedDocIds,\n        quizName: name,\n        customPrompt: customPrompt || undefined,\n        quizDescription: description,\n        generationOptions,\n        // aiConfig removed - credentials are retrieved from secure backend storage\n      };\n\n      // Debug logging\n      console.log(\"Frontend: Sending AI quiz generation payload:\", payload);\n      console.log(\"Frontend: Question types being sent:\", generationOptions.questionTypes);\n\n      const result: GenerateAiQuizApiResponse = await generateAiQuizAPI(\n        payload\n      );\n      toast({\n        title: \"Success!\",\n        description: `AI Quiz \"${result.name}\" generated successfully.`,\n      });\n      queryClient.invalidateQueries({ queryKey: [\"quizzes\"] });\n      onQuizCreated(result.id, result.name);\n      // Reset form\n      setName(\"\");\n      setDescription(\"\");\n      setGenerationOptions({\n        numberOfQuestions: 5,\n        questionTypes: [\"multiple_choice\"], // Fixed: use underscore to match UI options\n      });\n      setAiSelectedDocIds([]);\n      setCustomPrompt(\"\");\n      setIsAiMode(false); // Optionally switch back to manual or stay in AI mode\n    } catch (err: any) {\n      const errorMessage = err.message || \"Failed to generate AI quiz.\";\n      setError(errorMessage);\n      toast({\n        title: \"AI Generation Error\",\n        description: errorMessage,\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsGeneratingAiQuiz(false);\n    }\n  };\n\n  const handleAiDocumentSelection = (documentId: string) => {\n    setAiSelectedDocIds((prev) => {\n      if (prev.includes(documentId)) {\n        return prev.filter((id) => id !== documentId);\n      } else {\n        return [...prev, documentId];\n      }\n    });\n  };\n\n  return (\n    <div className=\"bg-slate-800 shadow-md rounded-lg p-4 md:p-6 mt-6\">\n      <div className=\"flex justify-between items-center mb-4\">\n        <h3 className=\"text-lg font-semibold text-purple-400\">\n          {isAiMode ? \"Generate More Questions with AI\" : \"Create New Quiz Manually\"}\n        </h3>\n        <div className=\"flex items-center space-x-2\">\n          <Label htmlFor=\"ai-mode-switch\" className=\"text-purple-300\">\n            Use AI\n          </Label>\n          <Switch\n            id=\"ai-mode-switch\"\n            checked={isAiMode}\n            onCheckedChange={setIsAiMode}\n            disabled={isGeneratingAiQuiz || loading}\n          />\n        </div>\n      </div>\n\n      {error && (\n        <p className=\"text-red-400 text-sm mb-3 bg-red-900/30 p-2 rounded-md\">\n          {error}\n        </p>\n      )}\n\n      {/* Common fields for both modes */}\n      <form\n        onSubmit={isAiMode ? (e) => e.preventDefault() : handleManualSubmit}\n        className=\"space-y-4\"\n      >\n        <div>\n          <Label htmlFor=\"quizName\" className=\"text-purple-300\">\n            Quiz Name*\n          </Label>\n          <Input\n            id=\"quizName\"\n            value={name}\n            onChange={(e) => setName(e.target.value)}\n            placeholder=\"e.g. Chapter 1 Review\"\n            required\n            className=\"mt-1 bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus-visible:ring-purple-500\"\n            disabled={loading || isGeneratingAiQuiz}\n          />\n        </div>\n        <div>\n          <Label htmlFor=\"quizDescription\" className=\"text-purple-300\">\n            Description (Optional)\n          </Label>\n          <Textarea\n            id=\"quizDescription\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            rows={3}\n            className=\"input-class mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\" // Added w-full and basic styling\n            placeholder=\"Short description of this quiz...\"\n            disabled={loading || isGeneratingAiQuiz}\n          />\n        </div>\n\n        {!isAiMode && (\n          <Button\n            type=\"submit\"\n            variant=\"outline\"\n            disabled={loading || isGeneratingAiQuiz}\n            className=\"w-full\"\n          >\n            {loading && <Spinner size=\"sm\" />}\n            {loading ? \"Creating...\" : \"Create Quiz Manually\"}\n          </Button>\n        )}\n      </form>\n\n      {isAiMode && (\n        <div className=\"mt-6 pt-4 border-t border-slate-700 space-y-4\">\n          <div>\n            <Label className=\"text-purple-300 mb-2 block\">\n              Select Documents for AI Generation\n            </Label>\n            {loadingAiDocs ? (\n              <div className=\"flex justify-center items-center py-4\">\n                <Spinner size=\"sm\" />{\" \"}\n                <span className=\"ml-2 text-purple-300\">\n                  Loading documents...\n                </span>\n              </div>\n            ) : availableDocuments.length === 0 ? (\n              <p className=\"text-sm text-purple-300 p-3 bg-slate-700 rounded-md\">\n                No extracted documents available. Please upload and process\n                documents first.\n              </p>\n            ) : (\n              <div className=\"max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600\">\n                {availableDocuments.map((doc) => (\n                  <div\n                    key={doc.id}\n                    className=\"flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors\"\n                  >\n                    <Checkbox\n                      id={`ai-doc-${doc.id}`}\n                      checked={aiSelectedDocIds.includes(doc.id)}\n                      onCheckedChange={() => handleAiDocumentSelection(doc.id)}\n                      disabled={isGeneratingAiQuiz}\n                    />\n                    <Label\n                      htmlFor={`ai-doc-${doc.id}`}\n                      className=\"font-normal text-purple-300 cursor-pointer flex-1 truncate\"\n                      title={doc.file_name}\n                    >\n                      {doc.file_name}\n                    </Label>\n                  </div>\n                ))}\n              </div>\n            )}\n            {aiSelectedDocIds.length > 0 && (\n              <p className=\"text-xs text-purple-400 mt-1\">\n                {aiSelectedDocIds.length} document(s) selected.\n              </p>\n            )}\n          </div>\n\n          <div>\n            <Label htmlFor=\"customPrompt\" className=\"text-purple-300\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              value={customPrompt}\n              onChange={(e) => setCustomPrompt(e.target.value)}\n              placeholder=\"e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'\"\n              rows={3}\n              className=\"input-class mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n              disabled={isGeneratingAiQuiz}\n            />\n            <p className=\"text-xs text-purple-400 mt-1\">\n              Add specific instructions for the AI on what kind of questions you\n              want.\n            </p>\n          </div>\n\n          <AiQuizGenerationOptions\n            generationOptions={generationOptions}\n            setGenerationOptions={setGenerationOptions}\n            isGenerating={isGeneratingAiQuiz}\n            onGenerate={handleAiGenerateQuiz}\n            documentIds={aiSelectedDocIds} // Pass selected IDs to enable/disable generate button\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useState, FormEvent, useEffect } from \"react\";\nimport { useQueryClient } from \"@tanstack/react-query\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { useToast } from \"../../hooks/use-toast\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport {\n  AiQuizGenerationOptions,\n  GenerationOptions,\n} from \"./AiQuizGenerationOptions\";\nimport {\n  generateAiQuizAPI,\n  GenerateAiQuizApiResponse,\n  createQuizAPI,\n  CreateQuizApiResponse,\n  GenerateAiQuizApiPayload,\n} from \"../../lib/api\";\nimport { Tables } from \"@/types/supabase\";\nimport { getAIProviderSettings } from \"@/lib/ai-provider\";\nimport { AIProviderConfig } from \"@shared/types/quiz\";\n\ninterface CreateQuizFormProps {\n  studyDocumentId?: string;\n  onQuizCreated: (quizId: string, quizName: string) => void;\n}\n\nexport const CreateQuizForm: React.FC<CreateQuizFormProps> = ({\n  studyDocumentId,\n  onQuizCreated,\n}) => {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n  const [name, setName] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false); // For manual creation\n  const [isGeneratingAiQuiz, setIsGeneratingAiQuiz] = useState(false); // For AI generation\n  const [error, setError] = useState<string | null>(null);\n  const [isAiMode, setIsAiMode] = useState(false);\n\n  // State for AI Mode specific inputs\n  const [availableDocuments, setAvailableDocuments] = useState<\n    Tables<\"study_documents\">[]\n  >([]);\n  const [aiSelectedDocIds, setAiSelectedDocIds] = useState<string[]>([]);\n  const [customPrompt, setCustomPrompt] = useState<string>(\"\");\n  const [loadingAiDocs, setLoadingAiDocs] = useState(false);\n  const [generationOptions, setGenerationOptions] = useState<GenerationOptions>(\n    {\n      numberOfQuestions: 5,\n      questionTypes: [\"multiple_choice\"], // Fixed: use underscore to match UI options\n    }\n  );\n\n  useEffect(() => {\n    const fetchDocumentsForAI = async () => {\n      if (isAiMode && user) {\n        setLoadingAiDocs(true);\n        try {\n          const { data, error: dbError } = await supabase\n            .from(\"study_documents\")\n            .select(\"id, file_name, created_at\")\n            .eq(\"user_id\", user.id)\n            .eq(\"status\", \"extracted\") // Only use documents with extracted text\n            .order(\"created_at\", { ascending: false });\n\n          if (dbError) throw dbError;\n          // Cast the data to the expected type to satisfy TypeScript\n          setAvailableDocuments(data as unknown as Tables<\"study_documents\">[] || []);\n        } catch (err: any) {\n          console.error(\"Error fetching documents for AI quiz:\", err);\n          toast({\n            title: \"Error\",\n            description: \"Could not load documents for AI quiz.\",\n            variant: \"destructive\",\n          });\n          setAvailableDocuments([]);\n        } finally {\n          setLoadingAiDocs(false);\n        }\n      } else {\n        setAvailableDocuments([]); // Clear if not in AI mode or no user\n      }\n    };\n\n    fetchDocumentsForAI();\n  }, [isAiMode, user, toast]);\n\n  const handleManualSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    if (!user) {\n      setError(\"You must be logged in to create a quiz.\");\n      toast({\n        title: \"Error\",\n        description: \"You must be logged in.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (!name.trim()) {\n      setError(\"Quiz name is required.\");\n      toast({\n        title: \"Error\",\n        description: \"Quiz name is required.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      const payload = {\n        name: name.trim(),\n        description: description || undefined,\n        // study_document_id: effectiveDocumentIds.length > 0 ? effectiveDocumentIds[0] : undefined, // Manual creation might not link to docs this way\n      };\n\n      const result: CreateQuizApiResponse = await createQuizAPI(payload);\n\n      toast({\n        title: \"Success!\",\n        description: `Quiz \"${result.name}\" created successfully.`,\n      });\n      queryClient.invalidateQueries({ queryKey: [\"quizzes\"] });\n      onQuizCreated(result.id, result.name);\n      setName(\"\");\n      setDescription(\"\");\n    } catch (err: any) {\n      const errorMessage = err.message || \"Failed to create quiz.\";\n      setError(errorMessage);\n      toast({\n        title: \"Error\",\n        description: errorMessage,\n        variant: \"destructive\",\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAiGenerateQuiz = async () => {\n    if (!user) {\n      setError(\"You must be logged in to generate an AI quiz.\");\n      toast({\n        title: \"Error\",\n        description: \"You must be logged in.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (aiSelectedDocIds.length === 0) {\n      setError(\n        \"At least one study document must be selected to generate an AI quiz.\"\n      );\n      toast({\n        title: \"Error\",\n        description: \"Please select at least one document.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (!name.trim()) {\n      setError(\"Quiz name is required for AI generation as well.\");\n      toast({\n        title: \"Error\",\n        description: \"Quiz name is required.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n    if (generationOptions.questionTypes.length === 0) {\n      setError(\"Please select at least one question type for AI generation.\");\n      toast({\n        title: \"Error\",\n        description: \"Select at least one question type.\",\n        variant: \"destructive\",\n      });\n      return;\n    }\n\n    setIsGeneratingAiQuiz(true);\n    setError(null);\n    try {\n      const payload: GenerateAiQuizApiPayload = {\n        documentIds: aiSelectedDocIds,\n        quizName: name,\n        customPrompt: customPrompt || undefined,\n        quizDescription: description,\n        generationOptions,\n        // aiConfig removed - credentials are retrieved from secure backend storage\n      };\n\n      // Debug logging\n      console.log(\"Frontend: Sending AI quiz generation payload:\", payload);\n      console.log(\"Frontend: Question types being sent:\", generationOptions.questionTypes);\n\n      const result: GenerateAiQuizApiResponse = await generateAiQuizAPI(\n        payload\n      );\n      toast({\n        title: \"Success!\",\n        description: `AI Quiz \"${result.name}\" generated successfully.`,\n      });\n      queryClient.invalidateQueries({ queryKey: [\"quizzes\"] });\n      onQuizCreated(result.id, result.name);\n      // Reset form\n      setName(\"\");\n      setDescription(\"\");\n      setGenerationOptions({\n        numberOfQuestions: 5,\n        questionTypes: [\"multiple_choice\"], // Fixed: use underscore to match UI options\n      });\n      setAiSelectedDocIds([]);\n      setCustomPrompt(\"\");\n      setIsAiMode(false); // Optionally switch back to manual or stay in AI mode\n    } catch (err: any) {\n      const errorMessage = err.message || \"Failed to generate AI quiz.\";\n      setError(errorMessage);\n      toast({\n        title: \"AI Generation Error\",\n        description: errorMessage,\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsGeneratingAiQuiz(false);\n    }\n  };\n\n  const handleAiDocumentSelection = (documentId: string) => {\n    setAiSelectedDocIds((prev) => {\n      if (prev.includes(documentId)) {\n        return prev.filter((id) => id !== documentId);\n      } else {\n        return [...prev, documentId];\n      }\n    });\n  };\n\n  return (\n    <div className=\"bg-slate-800 shadow-md rounded-lg p-4 md:p-6 mt-6\">\n      <div className=\"flex justify-between items-center mb-4\">\n        <h3 className=\"text-lg font-semibold text-purple-400\">\n          {isAiMode ? \"Generate More Questions with AI\" : \"Create New Quiz Manually\"}\n        </h3>\n        <div className=\"flex items-center space-x-2\">\n          <Label htmlFor=\"ai-mode-switch\" className=\"text-purple-300\">\n            Use AI\n          </Label>\n          <Switch\n            id=\"ai-mode-switch\"\n            checked={isAiMode}\n            onCheckedChange={setIsAiMode}\n            disabled={isGeneratingAiQuiz || loading}\n          />\n        </div>\n      </div>\n\n      {error && (\n        <p className=\"text-red-400 text-sm mb-3 bg-red-900/30 p-2 rounded-md\">\n          {error}\n        </p>\n      )}\n\n      {/* Common fields for both modes */}\n      <form\n        onSubmit={isAiMode ? (e) => e.preventDefault() : handleManualSubmit}\n        className=\"space-y-4\"\n      >\n        <div>\n          <Label htmlFor=\"quizName\" className=\"text-purple-300\">\n            Quiz Name*\n          </Label>\n          <Input\n            id=\"quizName\"\n            value={name}\n            onChange={(e) => setName(e.target.value)}\n            placeholder=\"e.g. Chapter 1 Review\"\n            required\n            className=\"mt-1 bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus-visible:ring-purple-500\"\n            disabled={loading || isGeneratingAiQuiz}\n          />\n        </div>\n        <div>\n          <Label htmlFor=\"quizDescription\" className=\"text-purple-300\">\n            Description (Optional)\n          </Label>\n          <Textarea\n            id=\"quizDescription\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            rows={3}\n            className=\"input-class mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\" // Added w-full and basic styling\n            placeholder=\"Short description of this quiz...\"\n            disabled={loading || isGeneratingAiQuiz}\n          />\n        </div>\n\n        {!isAiMode && (\n          <Button\n            type=\"submit\"\n            variant=\"outline\"\n            disabled={loading || isGeneratingAiQuiz}\n            className=\"w-full\"\n          >\n            {loading && <Spinner size=\"sm\" />}\n            {loading ? \"Creating...\" : \"Create Quiz Manually\"}\n          </Button>\n        )}\n      </form>\n\n      {isAiMode && (\n        <div className=\"mt-6 pt-4 border-t border-slate-700 space-y-4\">\n          <div>\n            <Label className=\"text-purple-300 mb-2 block\">\n              Select Documents for AI Generation\n            </Label>\n            {loadingAiDocs ? (\n              <div className=\"flex justify-center items-center py-4\">\n                <Spinner size=\"sm\" />{\" \"}\n                <span className=\"ml-2 text-purple-300\">\n                  Loading documents...\n                </span>\n              </div>\n            ) : availableDocuments.length === 0 ? (\n              <p className=\"text-sm text-purple-300 p-3 bg-slate-700 rounded-md\">\n                No extracted documents available. Please upload and process\n                documents first.\n              </p>\n            ) : (\n              <div className=\"max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600\">\n                {availableDocuments.map((doc) => (\n                  <div\n                    key={doc.id}\n                    className=\"flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors\"\n                  >\n                    <Checkbox\n                      id={`ai-doc-${doc.id}`}\n                      checked={aiSelectedDocIds.includes(doc.id)}\n                      onCheckedChange={() => handleAiDocumentSelection(doc.id)}\n                      disabled={isGeneratingAiQuiz}\n                    />\n                    <Label\n                      htmlFor={`ai-doc-${doc.id}`}\n                      className=\"font-normal text-purple-300 cursor-pointer flex-1 truncate\"\n                      title={doc.file_name}\n                    >\n                      {doc.file_name}\n                    </Label>\n                  </div>\n                ))}\n              </div>\n            )}\n            {aiSelectedDocIds.length > 0 && (\n              <p className=\"text-xs text-purple-400 mt-1\">\n                {aiSelectedDocIds.length} document(s) selected.\n              </p>\n            )}\n          </div>\n\n          <div>\n            <Label htmlFor=\"customPrompt\" className=\"text-purple-300\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              value={customPrompt}\n              onChange={(e) => setCustomPrompt(e.target.value)}\n              placeholder=\"e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'\"\n              rows={3}\n              className=\"input-class mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n              disabled={isGeneratingAiQuiz}\n            />\n            <p className=\"text-xs text-purple-400 mt-1\">\n              Add specific instructions for the AI on what kind of questions you\n              want.\n            </p>\n          </div>\n\n          <AiQuizGenerationOptions\n            generationOptions={generationOptions}\n            setGenerationOptions={setGenerationOptions}\n            isGenerating={isGeneratingAiQuiz}\n            onGenerate={handleAiGenerateQuiz}\n            documentIds={aiSelectedDocIds} // Pass selected IDs to enable/disable generate button\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n"}