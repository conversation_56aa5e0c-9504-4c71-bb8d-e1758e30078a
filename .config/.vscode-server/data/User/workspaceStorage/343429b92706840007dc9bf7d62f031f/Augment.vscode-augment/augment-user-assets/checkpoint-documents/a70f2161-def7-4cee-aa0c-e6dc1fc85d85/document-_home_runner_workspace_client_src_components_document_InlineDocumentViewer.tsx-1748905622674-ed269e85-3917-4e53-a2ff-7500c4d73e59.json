{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/InlineDocumentViewer.tsx"}, "originalCode": "import React, { useEffect, useState, useRef } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { Tables } from \"../../types/supabase\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { X, FileText, Download, ZoomIn, ZoomOut, Trash2 } from \"lucide-react\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport * as pdfjsLib from 'pdfjs-dist';\nimport MarkdownRenderer from \"./MarkdownRenderer\";\n\ntype StudyDocument = Tables<\"study_documents\">;\n\ninterface InlineDocumentViewerProps {\n  document: StudyDocument;\n  onClose: () => void;\n  onDelete?: (document: StudyDocument) => void;\n}\n\nconst API_BASE_URL =\n  import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";\n\n// Set up PDF.js worker\npdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.4.168/pdf.worker.min.mjs';\n\n// Helper to get auth token\nasync function getAuthToken(): Promise<string | null> {\n  try {\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n    return session?.access_token || null;\n  } catch (error) {\n    console.error(\"Error getting auth token:\", error);\n    return null;\n  }\n}\n\nexport const InlineDocumentViewer: React.FC<InlineDocumentViewerProps> = ({\n  document,\n  onClose,\n  onDelete,\n}) => {\n  const [content, setContent] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pdfDoc, setPdfDoc] = useState<pdfjsLib.PDFDocumentProxy | null>(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(0);\n  const [scale, setScale] = useState(1.0);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [isPdf, setIsPdf] = useState(false);\n\n  const isPdfFile = (contentType: string | null, fileName: string) => {\n    return contentType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf');\n  };\n\n  useEffect(() => {\n    const fetchDocumentContent = async () => {\n      if (!document?.id) {\n        setContent(null);\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setContent(null);\n      setPdfDoc(null);\n      setCurrentPage(1);\n      setTotalPages(0);\n\n      const isDocumentPdf = isPdfFile(document.content_type, document.file_name);\n      setIsPdf(isDocumentPdf);\n\n      try {\n        const token = await getAuthToken();\n        if (!token) {\n          throw new Error(\"Authentication required\");\n        }\n\n        if (isDocumentPdf) {\n          // Try to fetch PDF binary data first\n          try {\n            const response = await fetch(\n              `${API_BASE_URL}/documents/${document.id}/file`,\n              {\n                method: \"GET\",\n                headers: {\n                  Authorization: `Bearer ${token}`,\n                },\n              }\n            );\n\n            if (response.ok) {\n              const arrayBuffer = await response.arrayBuffer();\n              const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });\n              const pdf = await loadingTask.promise;\n\n              setPdfDoc(pdf);\n              setTotalPages(pdf.numPages);\n            } else {\n              // If original PDF is not available, fall back to extracted text\n              console.log(\"Original PDF not available, falling back to extracted text\");\n              setIsPdf(false);\n              throw new Error(\"Original PDF not available\");\n            }\n          } catch (pdfError) {\n            // Fall back to extracted text for PDFs when original file is not available\n            console.log(\"PDF loading failed, falling back to extracted text:\", pdfError);\n            setIsPdf(false);\n            // Continue to text content fetch below\n          }\n        }\n\n        if (!isPdf || !pdfDoc) {\n          // Fetch extracted text content for non-PDF files or when PDF original is not available\n          const response = await fetch(\n            `${API_BASE_URL}/documents/${document.id}/content`,\n            {\n              method: \"GET\",\n              headers: {\n                Authorization: `Bearer ${token}`,\n              },\n            }\n          );\n\n          if (!response.ok) {\n            const errorData = await response.json().catch(() => ({}));\n            throw new Error(\n              errorData.error || `HTTP ${response.status}: ${response.statusText}`\n            );\n          }\n\n          const textContent = await response.text();\n          setContent(textContent);\n        }\n      } catch (err: any) {\n        console.error(\"Error fetching document content:\", err);\n        setError(err.message || \"Failed to load document content.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDocumentContent();\n  }, [document]);\n\n  // Render PDF page\n  useEffect(() => {\n    const renderPage = async () => {\n      if (!pdfDoc || !canvasRef.current) return;\n\n      try {\n        const page = await pdfDoc.getPage(currentPage);\n        const viewport = page.getViewport({ scale });\n        \n        const canvas = canvasRef.current;\n        const context = canvas.getContext('2d');\n        if (!context) return;\n\n        canvas.height = viewport.height;\n        canvas.width = viewport.width;\n\n        const renderContext = {\n          canvasContext: context,\n          viewport: viewport,\n        };\n\n        await page.render(renderContext).promise;\n      } catch (err) {\n        console.error(\"Error rendering PDF page:\", err);\n      }\n    };\n\n    if (pdfDoc && currentPage) {\n      renderPage();\n    }\n  }, [pdfDoc, currentPage, scale]);\n\n  const handleDownload = async () => {\n    if (!content || !document) return;\n\n    try {\n      const blob = new Blob([content], { type: \"text/plain\" });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `${document.file_name}_extracted.txt`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (err) {\n      console.error(\"Error downloading content:\", err);\n    }\n  };\n\n  const nextPage = () => {\n    if (currentPage < totalPages) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n\n  const prevPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  const zoomIn = () => {\n    setScale(Math.min(scale * 1.2, 3.0));\n  };\n\n  const zoomOut = () => {\n    setScale(Math.max(scale / 1.2, 0.5));\n  };\n\n  const handleDelete = () => {\n    if (onDelete && document) {\n      const confirmDelete = window.confirm(\n        `Are you sure you want to delete \"${document.file_name}\"? This action cannot be undone.`\n      );\n      if (confirmDelete) {\n        onDelete(document);\n      }\n    }\n  };\n\n  return (\n    <Card className=\"h-[60vh] bg-slate-800 border-slate-700 flex flex-col\">\n      <CardHeader className=\"flex flex-row items-center justify-between py-3 px-4 border-b border-slate-700\">\n        <div className=\"flex items-center space-x-2 min-w-0 flex-1\">\n          <FileText className=\"h-4 w-4 text-purple-400 flex-shrink-0\" />\n          <CardTitle className=\"text-sm text-purple-400 truncate\">\n            {document?.file_name || \"Document Viewer\"}\n          </CardTitle>\n        </div>\n        <div className=\"flex items-center space-x-1 flex-shrink-0\">\n          {isPdf && pdfDoc && (\n            <>\n              <Button\n                onClick={zoomOut}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n              >\n                <ZoomOut className=\"h-3 w-3\" />\n              </Button>\n              <Button\n                onClick={zoomIn}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n              >\n                <ZoomIn className=\"h-3 w-3\" />\n              </Button>\n              <span className=\"text-xs text-slate-400 px-2\">\n                {currentPage}/{totalPages}\n              </span>\n              <Button\n                onClick={prevPage}\n                disabled={currentPage <= 1}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs text-slate-300 hover:bg-slate-700 disabled:opacity-50\"\n              >\n                Prev\n              </Button>\n              <Button\n                onClick={nextPage}\n                disabled={currentPage >= totalPages}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs text-slate-300 hover:bg-slate-700 disabled:opacity-50\"\n              >\n                Next\n              </Button>\n            </>\n          )}\n          {content && !isPdf && (\n            <Button\n              onClick={handleDownload}\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n            >\n              <Download className=\"h-3 w-3\" />\n            </Button>\n          )}\n          {onDelete && (\n            <Button\n              onClick={handleDelete}\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-7 w-7 p-0 text-red-400 hover:bg-red-500/20 hover:text-red-300\"\n            >\n              <Trash2 className=\"h-3 w-3\" />\n            </Button>\n          )}\n          <Button\n            onClick={onClose}\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 p-0 overflow-hidden\">\n        {loading && (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"text-center\">\n              <Spinner size=\"lg\" />\n              <p className=\"text-slate-300 mt-4 text-sm\">\n                Loading document...\n              </p>\n            </div>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"flex items-center justify-center h-full p-4\">\n            <div className=\"text-center\">\n              <p className=\"text-red-400 mb-4 text-sm\">{error}</p>\n              <Button\n                onClick={() => window.location.reload()}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n              >\n                Retry\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {isPdf && pdfDoc && !loading && !error && (\n          <div className=\"h-full overflow-auto bg-slate-900\">\n            <div className=\"flex justify-center p-4\">\n              <canvas\n                ref={canvasRef}\n                className=\"border border-slate-600 shadow-lg\"\n                style={{ maxWidth: '100%', height: 'auto' }}\n              />\n            </div>\n          </div>\n        )}\n\n        {!isPdf && content && !loading && !error && (\n          <ScrollArea className=\"h-full\">\n            <div className=\"p-4\">\n              <div className=\"bg-slate-900 p-4 rounded-lg\">\n                <div className=\"mb-4 text-xs text-slate-400 border-b border-slate-700 pb-2\">\n                  <p><strong>File:</strong> {document.file_name}</p>\n                  <p><strong>Type:</strong> {document.content_type}</p>\n                  <p><strong>Size:</strong> {document.size_bytes ? `${(document.size_bytes / 1024).toFixed(1)} KB` : \"Unknown\"}</p>\n                </div>\n                <div>\n                  <h3 className=\"text-slate-300 font-medium mb-3 text-sm\">\n                    Extracted Content:\n                  </h3>\n                  <MarkdownRenderer\n                    content={content}\n                    className=\"text-xs leading-relaxed\"\n                  />\n                </div>\n              </div>\n            </div>\n          </ScrollArea>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n", "modifiedCode": "import React, { useEffect, useState, useRef } from \"react\";\nimport { Tables } from \"../../types/supabase\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { X, FileText, Download, ZoomIn, ZoomOut, Trash2 } from \"lucide-react\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport * as pdfjsLib from 'pdfjs-dist';\nimport MarkdownRenderer from \"./MarkdownRenderer\";\n\ntype StudyDocument = Tables<\"study_documents\">;\n\ninterface InlineDocumentViewerProps {\n  document: StudyDocument;\n  onClose: () => void;\n  onDelete?: (document: StudyDocument) => void;\n}\n\nconst API_BASE_URL =\n  import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";\n\n// Set up PDF.js worker\npdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.4.168/pdf.worker.min.mjs';\n\n// Helper to get auth token\nasync function getAuthToken(): Promise<string | null> {\n  try {\n    const token = localStorage.getItem('auth_token');\n    return token || null;\n  } catch (error) {\n    console.error(\"Error getting auth token:\", error);\n    return null;\n  }\n}\n\nexport const InlineDocumentViewer: React.FC<InlineDocumentViewerProps> = ({\n  document,\n  onClose,\n  onDelete,\n}) => {\n  const [content, setContent] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pdfDoc, setPdfDoc] = useState<pdfjsLib.PDFDocumentProxy | null>(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(0);\n  const [scale, setScale] = useState(1.0);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const [isPdf, setIsPdf] = useState(false);\n\n  const isPdfFile = (contentType: string | null, fileName: string) => {\n    return contentType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf');\n  };\n\n  useEffect(() => {\n    const fetchDocumentContent = async () => {\n      if (!document?.id) {\n        setContent(null);\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setContent(null);\n      setPdfDoc(null);\n      setCurrentPage(1);\n      setTotalPages(0);\n\n      const isDocumentPdf = isPdfFile(document.content_type, document.file_name);\n      setIsPdf(isDocumentPdf);\n\n      try {\n        const token = await getAuthToken();\n        if (!token) {\n          throw new Error(\"Authentication required\");\n        }\n\n        if (isDocumentPdf) {\n          // Try to fetch PDF binary data first\n          try {\n            const response = await fetch(\n              `${API_BASE_URL}/documents/${document.id}/file`,\n              {\n                method: \"GET\",\n                headers: {\n                  Authorization: `Bearer ${token}`,\n                },\n              }\n            );\n\n            if (response.ok) {\n              const arrayBuffer = await response.arrayBuffer();\n              const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });\n              const pdf = await loadingTask.promise;\n\n              setPdfDoc(pdf);\n              setTotalPages(pdf.numPages);\n            } else {\n              // If original PDF is not available, fall back to extracted text\n              console.log(\"Original PDF not available, falling back to extracted text\");\n              setIsPdf(false);\n              throw new Error(\"Original PDF not available\");\n            }\n          } catch (pdfError) {\n            // Fall back to extracted text for PDFs when original file is not available\n            console.log(\"PDF loading failed, falling back to extracted text:\", pdfError);\n            setIsPdf(false);\n            // Continue to text content fetch below\n          }\n        }\n\n        if (!isPdf || !pdfDoc) {\n          // Fetch extracted text content for non-PDF files or when PDF original is not available\n          const response = await fetch(\n            `${API_BASE_URL}/documents/${document.id}/content`,\n            {\n              method: \"GET\",\n              headers: {\n                Authorization: `Bearer ${token}`,\n              },\n            }\n          );\n\n          if (!response.ok) {\n            const errorData = await response.json().catch(() => ({}));\n            throw new Error(\n              errorData.error || `HTTP ${response.status}: ${response.statusText}`\n            );\n          }\n\n          const textContent = await response.text();\n          setContent(textContent);\n        }\n      } catch (err: any) {\n        console.error(\"Error fetching document content:\", err);\n        setError(err.message || \"Failed to load document content.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDocumentContent();\n  }, [document]);\n\n  // Render PDF page\n  useEffect(() => {\n    const renderPage = async () => {\n      if (!pdfDoc || !canvasRef.current) return;\n\n      try {\n        const page = await pdfDoc.getPage(currentPage);\n        const viewport = page.getViewport({ scale });\n        \n        const canvas = canvasRef.current;\n        const context = canvas.getContext('2d');\n        if (!context) return;\n\n        canvas.height = viewport.height;\n        canvas.width = viewport.width;\n\n        const renderContext = {\n          canvasContext: context,\n          viewport: viewport,\n        };\n\n        await page.render(renderContext).promise;\n      } catch (err) {\n        console.error(\"Error rendering PDF page:\", err);\n      }\n    };\n\n    if (pdfDoc && currentPage) {\n      renderPage();\n    }\n  }, [pdfDoc, currentPage, scale]);\n\n  const handleDownload = async () => {\n    if (!content || !document) return;\n\n    try {\n      const blob = new Blob([content], { type: \"text/plain\" });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `${document.file_name}_extracted.txt`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (err) {\n      console.error(\"Error downloading content:\", err);\n    }\n  };\n\n  const nextPage = () => {\n    if (currentPage < totalPages) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n\n  const prevPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  const zoomIn = () => {\n    setScale(Math.min(scale * 1.2, 3.0));\n  };\n\n  const zoomOut = () => {\n    setScale(Math.max(scale / 1.2, 0.5));\n  };\n\n  const handleDelete = () => {\n    if (onDelete && document) {\n      const confirmDelete = window.confirm(\n        `Are you sure you want to delete \"${document.file_name}\"? This action cannot be undone.`\n      );\n      if (confirmDelete) {\n        onDelete(document);\n      }\n    }\n  };\n\n  return (\n    <Card className=\"h-[60vh] bg-slate-800 border-slate-700 flex flex-col\">\n      <CardHeader className=\"flex flex-row items-center justify-between py-3 px-4 border-b border-slate-700\">\n        <div className=\"flex items-center space-x-2 min-w-0 flex-1\">\n          <FileText className=\"h-4 w-4 text-purple-400 flex-shrink-0\" />\n          <CardTitle className=\"text-sm text-purple-400 truncate\">\n            {document?.file_name || \"Document Viewer\"}\n          </CardTitle>\n        </div>\n        <div className=\"flex items-center space-x-1 flex-shrink-0\">\n          {isPdf && pdfDoc && (\n            <>\n              <Button\n                onClick={zoomOut}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n              >\n                <ZoomOut className=\"h-3 w-3\" />\n              </Button>\n              <Button\n                onClick={zoomIn}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n              >\n                <ZoomIn className=\"h-3 w-3\" />\n              </Button>\n              <span className=\"text-xs text-slate-400 px-2\">\n                {currentPage}/{totalPages}\n              </span>\n              <Button\n                onClick={prevPage}\n                disabled={currentPage <= 1}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs text-slate-300 hover:bg-slate-700 disabled:opacity-50\"\n              >\n                Prev\n              </Button>\n              <Button\n                onClick={nextPage}\n                disabled={currentPage >= totalPages}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs text-slate-300 hover:bg-slate-700 disabled:opacity-50\"\n              >\n                Next\n              </Button>\n            </>\n          )}\n          {content && !isPdf && (\n            <Button\n              onClick={handleDownload}\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n            >\n              <Download className=\"h-3 w-3\" />\n            </Button>\n          )}\n          {onDelete && (\n            <Button\n              onClick={handleDelete}\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-7 w-7 p-0 text-red-400 hover:bg-red-500/20 hover:text-red-300\"\n            >\n              <Trash2 className=\"h-3 w-3\" />\n            </Button>\n          )}\n          <Button\n            onClick={onClose}\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"h-7 w-7 p-0 text-slate-300 hover:bg-slate-700\"\n          >\n            <X className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"flex-1 p-0 overflow-hidden\">\n        {loading && (\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"text-center\">\n              <Spinner size=\"lg\" />\n              <p className=\"text-slate-300 mt-4 text-sm\">\n                Loading document...\n              </p>\n            </div>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"flex items-center justify-center h-full p-4\">\n            <div className=\"text-center\">\n              <p className=\"text-red-400 mb-4 text-sm\">{error}</p>\n              <Button\n                onClick={() => window.location.reload()}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n              >\n                Retry\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {isPdf && pdfDoc && !loading && !error && (\n          <div className=\"h-full overflow-auto bg-slate-900\">\n            <div className=\"flex justify-center p-4\">\n              <canvas\n                ref={canvasRef}\n                className=\"border border-slate-600 shadow-lg\"\n                style={{ maxWidth: '100%', height: 'auto' }}\n              />\n            </div>\n          </div>\n        )}\n\n        {!isPdf && content && !loading && !error && (\n          <ScrollArea className=\"h-full\">\n            <div className=\"p-4\">\n              <div className=\"bg-slate-900 p-4 rounded-lg\">\n                <div className=\"mb-4 text-xs text-slate-400 border-b border-slate-700 pb-2\">\n                  <p><strong>File:</strong> {document.file_name}</p>\n                  <p><strong>Type:</strong> {document.content_type}</p>\n                  <p><strong>Size:</strong> {document.size_bytes ? `${(document.size_bytes / 1024).toFixed(1)} KB` : \"Unknown\"}</p>\n                </div>\n                <div>\n                  <h3 className=\"text-slate-300 font-medium mb-3 text-sm\">\n                    Extracted Content:\n                  </h3>\n                  <MarkdownRenderer\n                    content={content}\n                    className=\"text-xs leading-relaxed\"\n                  />\n                </div>\n              </div>\n            </div>\n          </ScrollArea>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n"}