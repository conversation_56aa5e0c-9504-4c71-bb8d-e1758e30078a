{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardForm.tsx"}, "originalCode": "import React, { useState, FormEvent } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { Terminal } from \"lucide-react\";\nimport { supabase } from \"@/lib/supabaseClient\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables, TablesInsert } from \"@/types/supabase\";\n\ntype Flashcard = Tables<\"flashcards\">;\ntype FlashcardInsert = TablesInsert<\"flashcards\">;\n\ninterface FlashcardFormProps {\n  selectedDeckId: string;\n  editingFlashcard?: Flashcard | null;\n  onFlashcardSaved: () => void;\n  onCancel: () => void;\n}\n\nconst FlashcardForm: React.FC<FlashcardFormProps> = ({\n  selectedDeckId,\n  editingFlashcard,\n  onFlashcardSaved,\n  onCancel,\n}) => {\n  const { user } = useAuth();\n  const [question, setQuestion] = useState(editingFlashcard?.front_text || \"\");\n  const [answer, setAnswer] = useState(editingFlashcard?.back_text || \"\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n\n    if (!question.trim() || !answer.trim()) {\n      setError(\"Both question and answer are required.\");\n      return;\n    }\n\n    if (!user) {\n      setError(\"You must be logged in to save flashcards.\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      if (editingFlashcard) {\n        // Update existing flashcard\n        const { error: updateError } = await supabase\n          .from(\"flashcards\")\n          .update({\n            front_text: question.trim(),\n            back_text: answer.trim(),\n            updated_at: new Date().toISOString(),\n          })\n          .eq(\"id\", editingFlashcard.id)\n          .eq(\"user_id\", user.id);\n\n        if (updateError) throw updateError;\n      } else {\n        // Create new flashcard\n        const newFlashcard: FlashcardInsert = {\n          set_id: selectedDeckId,\n          user_id: user.id,\n          front_text: question.trim(),\n          back_text: answer.trim(),\n        };\n\n        const { error: insertError } = await supabase\n          .from(\"flashcards\")\n          .insert(newFlashcard);\n\n        if (insertError) throw insertError;\n      }\n\n      onFlashcardSaved();\n\n      // Reset form if adding new\n      if (!editingFlashcard) {\n        setQuestion(\"\");\n        setAnswer(\"\");\n      }\n    } catch (err: any) {\n      console.error(\"Error saving flashcard:\", err);\n      setError(\n        err.message || \"Failed to save flashcard.\"\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"flex items-center justify-between border-b border-slate-700 pb-4\">\n        <h5 className=\"text-lg font-bold text-slate-200 flex items-center\">\n          {editingFlashcard ? (\n            <>\n              <svg\n                className=\"w-5 h-5 mr-2 text-yellow-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n              </svg>\n              Edit Flashcard\n            </>\n          ) : (\n            <>\n              <svg\n                className=\"w-5 h-5 mr-2 text-green-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              Add New Flashcard\n            </>\n          )}\n        </h5>\n        {editingFlashcard && (\n          <Button\n            type=\"button\"\n            variant=\"ghost\"\n            onClick={onCancel}\n            className=\"text-slate-400 hover:text-slate-300\"\n          >\n            Cancel\n          </Button>\n        )}\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <Terminal className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      <div className=\"space-y-4\">\n        <div>\n          <Label htmlFor=\"question\" className=\"text-slate-300\">\n            Question (Front of Card)\n          </Label>\n          <Textarea\n            id=\"question\"\n            value={question}\n            onChange={(e) => setQuestion(e.target.value)}\n            placeholder=\"Enter the question or prompt...\"\n            rows={3}\n            className=\"mt-1 w-full p-3 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n            disabled={loading}\n            required\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"answer\" className=\"text-slate-300\">\n            Answer (Back of Card)\n          </Label>\n          <Textarea\n            id=\"answer\"\n            value={answer}\n            onChange={(e) => setAnswer(e.target.value)}\n            placeholder=\"Enter the answer or explanation...\"\n            rows={3}\n            className=\"mt-1 w-full p-3 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n            disabled={loading}\n            required\n          />\n        </div>\n      </div>\n\n      <div className=\"flex justify-end space-x-3\">\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={onCancel}\n          disabled={loading}\n          className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n        >\n          Cancel\n        </Button>\n        <Button\n          type=\"submit\"\n          disabled={loading || !question.trim() || !answer.trim()}\n          className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n        >\n          {loading\n            ? editingFlashcard\n              ? \"Saving...\"\n              : \"Adding...\"\n            : editingFlashcard\n            ? \"Save Changes\"\n            : \"Add Flashcard\"}\n        </Button>\n      </div>\n    </form>\n  );\n};\n\nexport default FlashcardForm;\n", "modifiedCode": "import React, { useState, FormEvent } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { Terminal } from \"lucide-react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { Tables, TablesInsert } from \"@/types/supabase\";\n\ntype Flashcard = Tables<\"flashcards\">;\ntype FlashcardInsert = TablesInsert<\"flashcards\">;\n\ninterface FlashcardFormProps {\n  selectedDeckId: string;\n  editingFlashcard?: Flashcard | null;\n  onFlashcardSaved: () => void;\n  onCancel: () => void;\n}\n\nconst FlashcardForm: React.FC<FlashcardFormProps> = ({\n  selectedDeckId,\n  editingFlashcard,\n  onFlashcardSaved,\n  onCancel,\n}) => {\n  const { user } = useAuth();\n  const [question, setQuestion] = useState(editingFlashcard?.front_text || \"\");\n  const [answer, setAnswer] = useState(editingFlashcard?.back_text || \"\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n\n    if (!question.trim() || !answer.trim()) {\n      setError(\"Both question and answer are required.\");\n      return;\n    }\n\n    if (!user) {\n      setError(\"You must be logged in to save flashcards.\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      if (editingFlashcard) {\n        // Update existing flashcard\n        const { error: updateError } = await supabase\n          .from(\"flashcards\")\n          .update({\n            front_text: question.trim(),\n            back_text: answer.trim(),\n            updated_at: new Date().toISOString(),\n          })\n          .eq(\"id\", editingFlashcard.id)\n          .eq(\"user_id\", user.id);\n\n        if (updateError) throw updateError;\n      } else {\n        // Create new flashcard\n        const newFlashcard: FlashcardInsert = {\n          set_id: selectedDeckId,\n          user_id: user.id,\n          front_text: question.trim(),\n          back_text: answer.trim(),\n        };\n\n        const { error: insertError } = await supabase\n          .from(\"flashcards\")\n          .insert(newFlashcard);\n\n        if (insertError) throw insertError;\n      }\n\n      onFlashcardSaved();\n\n      // Reset form if adding new\n      if (!editingFlashcard) {\n        setQuestion(\"\");\n        setAnswer(\"\");\n      }\n    } catch (err: any) {\n      console.error(\"Error saving flashcard:\", err);\n      setError(\n        err.message || \"Failed to save flashcard.\"\n      );\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <div className=\"flex items-center justify-between border-b border-slate-700 pb-4\">\n        <h5 className=\"text-lg font-bold text-slate-200 flex items-center\">\n          {editingFlashcard ? (\n            <>\n              <svg\n                className=\"w-5 h-5 mr-2 text-yellow-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n              </svg>\n              Edit Flashcard\n            </>\n          ) : (\n            <>\n              <svg\n                className=\"w-5 h-5 mr-2 text-green-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              Add New Flashcard\n            </>\n          )}\n        </h5>\n        {editingFlashcard && (\n          <Button\n            type=\"button\"\n            variant=\"ghost\"\n            onClick={onCancel}\n            className=\"text-slate-400 hover:text-slate-300\"\n          >\n            Cancel\n          </Button>\n        )}\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <Terminal className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      <div className=\"space-y-4\">\n        <div>\n          <Label htmlFor=\"question\" className=\"text-slate-300\">\n            Question (Front of Card)\n          </Label>\n          <Textarea\n            id=\"question\"\n            value={question}\n            onChange={(e) => setQuestion(e.target.value)}\n            placeholder=\"Enter the question or prompt...\"\n            rows={3}\n            className=\"mt-1 w-full p-3 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n            disabled={loading}\n            required\n          />\n        </div>\n\n        <div>\n          <Label htmlFor=\"answer\" className=\"text-slate-300\">\n            Answer (Back of Card)\n          </Label>\n          <Textarea\n            id=\"answer\"\n            value={answer}\n            onChange={(e) => setAnswer(e.target.value)}\n            placeholder=\"Enter the answer or explanation...\"\n            rows={3}\n            className=\"mt-1 w-full p-3 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n            disabled={loading}\n            required\n          />\n        </div>\n      </div>\n\n      <div className=\"flex justify-end space-x-3\">\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={onCancel}\n          disabled={loading}\n          className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n        >\n          Cancel\n        </Button>\n        <Button\n          type=\"submit\"\n          disabled={loading || !question.trim() || !answer.trim()}\n          className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n        >\n          {loading\n            ? editingFlashcard\n              ? \"Saving...\"\n              : \"Adding...\"\n            : editingFlashcard\n            ? \"Save Changes\"\n            : \"Add Flashcard\"}\n        </Button>\n      </div>\n    </form>\n  );\n};\n\nexport default FlashcardForm;\n"}