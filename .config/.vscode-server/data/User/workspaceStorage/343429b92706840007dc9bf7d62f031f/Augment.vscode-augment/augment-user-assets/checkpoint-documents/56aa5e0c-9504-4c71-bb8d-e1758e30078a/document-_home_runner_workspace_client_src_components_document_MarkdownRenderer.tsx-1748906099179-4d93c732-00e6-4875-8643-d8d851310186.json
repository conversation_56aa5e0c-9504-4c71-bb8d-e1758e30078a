{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/MarkdownRenderer.tsx"}, "originalCode": "import React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport rehypeHighlight from 'rehype-highlight';\nimport rehypeRaw from 'rehype-raw';\nimport 'highlight.js/styles/github-dark.css';\n\ninterface MarkdownRendererProps {\n  content: string;\n  className?: string;\n}\n\n// Function to preprocess content and improve formatting\nconst preprocessContent = (content: string): string => {\n  if (!content) return '';\n\n  // Split content into sentences and add proper spacing\n  let processed = content\n    // Handle specific patterns first\n    // Convert \"Question for the semester:\" style headers\n    .replace(/(Question for the [^:]+:)/g, '\\n\\n## $1\\n\\n')\n    // Convert section headers (words in all caps)\n    .replace(/\\b([A-Z][A-Z\\s]{4,}[A-Z])\\b/g, '\\n\\n## $1\\n\\n')\n    // Handle bullet points (● symbol) - make sure they're on new lines\n    .replace(/●\\s*/g, '\\n\\n• ')\n    // Handle \"O\" bullet points (common in academic texts)\n    .replace(/\\sO\\s+([A-Z])/g, '\\n\\n• $1')\n    // Handle numbered lists\n    .replace(/(\\d+)\\.\\s+/g, '\\n\\n$1. ')\n    // Add line breaks after sentences that end with periods followed by capital letters\n    .replace(/\\.\\s+([A-Z][a-z])/g, '.\\n\\n$1')\n    // Add line breaks after question marks followed by capital letters\n    .replace(/\\?\\s+([A-Z][a-z])/g, '?\\n\\n$1')\n    // Add line breaks after exclamation marks followed by capital letters\n    .replace(/!\\s+([A-Z][a-z])/g, '!\\n\\n$1')\n    // Handle \"Also:\" style connectors\n    .replace(/\\bAlso:\\s*/g, '\\n\\n**Also:**\\n\\n')\n    // Clean up multiple consecutive line breaks\n    .replace(/\\n{3,}/g, '\\n\\n')\n    // Trim whitespace\n    .trim();\n\n  return processed;\n};\n\nconst MarkdownRenderer: React.FC<MarkdownRendererProps> = ({\n  content,\n  className = ''\n}) => {\n  // If content is empty or just whitespace, show a fallback\n  if (!content || content.trim() === '') {\n    return (\n      <div className={`text-slate-400 italic ${className}`}>\n        No content available\n      </div>\n    );\n  }\n\n  // Preprocess the content to improve formatting\n  const processedContent = preprocessContent(content);\n\n  return (\n    <div className={`markdown-content max-w-none ${className}`}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeHighlight, rehypeRaw]}\n        components={{\n          // Headings\n          h1: ({ children }) => (\n            <h1 className=\"text-2xl font-bold text-slate-100 mb-4 mt-6 border-b border-slate-600 pb-2\">\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className=\"text-xl font-semibold text-slate-200 mb-3 mt-5\">\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className=\"text-lg font-medium text-slate-200 mb-2 mt-4\">\n              {children}\n            </h3>\n          ),\n          h4: ({ children }) => (\n            <h4 className=\"text-base font-medium text-slate-300 mb-2 mt-3\">\n              {children}\n            </h4>\n          ),\n          h5: ({ children }) => (\n            <h5 className=\"text-sm font-medium text-slate-300 mb-1 mt-2\">\n              {children}\n            </h5>\n          ),\n          h6: ({ children }) => (\n            <h6 className=\"text-sm font-medium text-slate-400 mb-1 mt-2\">\n              {children}\n            </h6>\n          ),\n          \n          // Paragraphs\n          p: ({ children }) => {\n            // Handle long text blocks by adding better spacing and line height\n            return (\n              <p className=\"text-slate-200 mb-4 leading-relaxed text-base\">\n                {children}\n              </p>\n            );\n          },\n          \n          // Lists\n          ul: ({ children }) => (\n            <ul className=\"list-disc list-inside text-slate-200 mb-3 space-y-1 ml-4\">\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className=\"list-decimal list-inside text-slate-200 mb-3 space-y-1 ml-4\">\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className=\"text-slate-200 leading-relaxed\">\n              {children}\n            </li>\n          ),\n          \n          // Code\n          code: ({ inline, children, className }) => {\n            if (inline) {\n              return (\n                <code className=\"bg-slate-800 text-purple-300 px-1.5 py-0.5 rounded text-sm font-mono\">\n                  {children}\n                </code>\n              );\n            }\n            return (\n              <code className={`${className} text-sm`}>\n                {children}\n              </code>\n            );\n          },\n          pre: ({ children }) => (\n            <pre className=\"bg-slate-900 border border-slate-700 rounded-lg p-4 mb-4 overflow-x-auto\">\n              {children}\n            </pre>\n          ),\n          \n          // Blockquotes\n          blockquote: ({ children }) => (\n            <blockquote className=\"border-l-4 border-purple-500 pl-4 py-2 mb-4 bg-slate-800/50 rounded-r-lg\">\n              <div className=\"text-slate-300 italic\">\n                {children}\n              </div>\n            </blockquote>\n          ),\n          \n          // Tables\n          table: ({ children }) => (\n            <div className=\"overflow-x-auto mb-4\">\n              <table className=\"min-w-full border border-slate-600 rounded-lg overflow-hidden\">\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className=\"bg-slate-700\">\n              {children}\n            </thead>\n          ),\n          tbody: ({ children }) => (\n            <tbody className=\"bg-slate-800\">\n              {children}\n            </tbody>\n          ),\n          tr: ({ children }) => (\n            <tr className=\"border-b border-slate-600\">\n              {children}\n            </tr>\n          ),\n          th: ({ children }) => (\n            <th className=\"px-4 py-2 text-left text-slate-200 font-medium\">\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className=\"px-4 py-2 text-slate-300\">\n              {children}\n            </td>\n          ),\n          \n          // Links\n          a: ({ href, children }) => (\n            <a \n              href={href} \n              className=\"text-purple-400 hover:text-purple-300 underline transition-colors\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {children}\n            </a>\n          ),\n          \n          // Horizontal rules\n          hr: () => (\n            <hr className=\"border-slate-600 my-6\" />\n          ),\n          \n          // Strong/Bold\n          strong: ({ children }) => (\n            <strong className=\"font-semibold text-slate-100\">\n              {children}\n            </strong>\n          ),\n          \n          // Emphasis/Italic\n          em: ({ children }) => (\n            <em className=\"italic text-slate-200\">\n              {children}\n            </em>\n          ),\n        }}\n      >\n        {processedContent}\n      </ReactMarkdown>\n    </div>\n  );\n};\n\nexport default MarkdownRenderer;\n", "modifiedCode": "import React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport rehypeHighlight from 'rehype-highlight';\nimport rehypeRaw from 'rehype-raw';\nimport 'highlight.js/styles/github-dark.css';\n\ninterface MarkdownRendererProps {\n  content: string;\n  className?: string;\n}\n\n// Function to preprocess content and improve formatting\nconst preprocessContent = (content: string): string => {\n  if (!content) return '';\n\n  // Split content into sentences and add proper spacing\n  let processed = content\n    // Handle specific patterns first\n    // Convert \"Question for the semester:\" style headers\n    .replace(/(Question for the [^:]+:)/g, '\\n\\n## $1\\n\\n')\n    // Convert section headers (words in all caps)\n    .replace(/\\b([A-Z][A-Z\\s]{4,}[A-Z])\\b/g, '\\n\\n## $1\\n\\n')\n    // Handle bullet points (● symbol) - make sure they're on new lines\n    .replace(/●\\s*/g, '\\n\\n• ')\n    // Handle \"O\" bullet points (common in academic texts)\n    .replace(/\\sO\\s+([A-Z])/g, '\\n\\n• $1')\n    // Handle numbered lists\n    .replace(/(\\d+)\\.\\s+/g, '\\n\\n$1. ')\n    // Add line breaks after sentences that end with periods followed by capital letters\n    .replace(/\\.\\s+([A-Z][a-z])/g, '.\\n\\n$1')\n    // Add line breaks after question marks followed by capital letters\n    .replace(/\\?\\s+([A-Z][a-z])/g, '?\\n\\n$1')\n    // Add line breaks after exclamation marks followed by capital letters\n    .replace(/!\\s+([A-Z][a-z])/g, '!\\n\\n$1')\n    // Handle \"Also:\" style connectors\n    .replace(/\\bAlso:\\s*/g, '\\n\\n**Also:**\\n\\n')\n    // Clean up multiple consecutive line breaks\n    .replace(/\\n{3,}/g, '\\n\\n')\n    // Trim whitespace\n    .trim();\n\n  return processed;\n};\n\nconst MarkdownRenderer: React.FC<MarkdownRendererProps> = ({\n  content,\n  className = ''\n}) => {\n  // If content is empty or just whitespace, show a fallback\n  if (!content || content.trim() === '') {\n    return (\n      <div className={`text-slate-400 italic ${className}`}>\n        No content available\n      </div>\n    );\n  }\n\n  // Preprocess the content to improve formatting\n  const processedContent = preprocessContent(content);\n\n  return (\n    <div className={`markdown-content max-w-none ${className}`}>\n      <ReactMarkdown\n        remarkPlugins={[remarkGfm]}\n        rehypePlugins={[rehypeHighlight, rehypeRaw]}\n        components={{\n          // Headings\n          h1: ({ children }) => (\n            <h1 className=\"text-2xl font-bold text-slate-100 mb-4 mt-6 border-b border-slate-600 pb-2\">\n              {children}\n            </h1>\n          ),\n          h2: ({ children }) => (\n            <h2 className=\"text-xl font-semibold text-slate-200 mb-3 mt-5\">\n              {children}\n            </h2>\n          ),\n          h3: ({ children }) => (\n            <h3 className=\"text-lg font-medium text-slate-200 mb-2 mt-4\">\n              {children}\n            </h3>\n          ),\n          h4: ({ children }) => (\n            <h4 className=\"text-base font-medium text-slate-300 mb-2 mt-3\">\n              {children}\n            </h4>\n          ),\n          h5: ({ children }) => (\n            <h5 className=\"text-sm font-medium text-slate-300 mb-1 mt-2\">\n              {children}\n            </h5>\n          ),\n          h6: ({ children }) => (\n            <h6 className=\"text-sm font-medium text-slate-400 mb-1 mt-2\">\n              {children}\n            </h6>\n          ),\n          \n          // Paragraphs\n          p: ({ children }) => {\n            // Handle long text blocks by adding better spacing and line height\n            return (\n              <p className=\"text-slate-200 mb-4 leading-relaxed text-base\">\n                {children}\n              </p>\n            );\n          },\n          \n          // Lists\n          ul: ({ children }) => (\n            <ul className=\"list-disc list-inside text-slate-200 mb-3 space-y-1 ml-4\">\n              {children}\n            </ul>\n          ),\n          ol: ({ children }) => (\n            <ol className=\"list-decimal list-inside text-slate-200 mb-3 space-y-1 ml-4\">\n              {children}\n            </ol>\n          ),\n          li: ({ children }) => (\n            <li className=\"text-slate-200 leading-relaxed\">\n              {children}\n            </li>\n          ),\n          \n          // Code\n          code: ({ inline, children, className }: any) => {\n            if (inline) {\n              return (\n                <code className=\"bg-slate-800 text-purple-300 px-1.5 py-0.5 rounded text-sm font-mono\">\n                  {children}\n                </code>\n              );\n            }\n            return (\n              <code className={`${className} text-sm`}>\n                {children}\n              </code>\n            );\n          },\n          pre: ({ children }) => (\n            <pre className=\"bg-slate-900 border border-slate-700 rounded-lg p-4 mb-4 overflow-x-auto\">\n              {children}\n            </pre>\n          ),\n          \n          // Blockquotes\n          blockquote: ({ children }) => (\n            <blockquote className=\"border-l-4 border-purple-500 pl-4 py-2 mb-4 bg-slate-800/50 rounded-r-lg\">\n              <div className=\"text-slate-300 italic\">\n                {children}\n              </div>\n            </blockquote>\n          ),\n          \n          // Tables\n          table: ({ children }) => (\n            <div className=\"overflow-x-auto mb-4\">\n              <table className=\"min-w-full border border-slate-600 rounded-lg overflow-hidden\">\n                {children}\n              </table>\n            </div>\n          ),\n          thead: ({ children }) => (\n            <thead className=\"bg-slate-700\">\n              {children}\n            </thead>\n          ),\n          tbody: ({ children }) => (\n            <tbody className=\"bg-slate-800\">\n              {children}\n            </tbody>\n          ),\n          tr: ({ children }) => (\n            <tr className=\"border-b border-slate-600\">\n              {children}\n            </tr>\n          ),\n          th: ({ children }) => (\n            <th className=\"px-4 py-2 text-left text-slate-200 font-medium\">\n              {children}\n            </th>\n          ),\n          td: ({ children }) => (\n            <td className=\"px-4 py-2 text-slate-300\">\n              {children}\n            </td>\n          ),\n          \n          // Links\n          a: ({ href, children }) => (\n            <a \n              href={href} \n              className=\"text-purple-400 hover:text-purple-300 underline transition-colors\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              {children}\n            </a>\n          ),\n          \n          // Horizontal rules\n          hr: () => (\n            <hr className=\"border-slate-600 my-6\" />\n          ),\n          \n          // Strong/Bold\n          strong: ({ children }) => (\n            <strong className=\"font-semibold text-slate-100\">\n              {children}\n            </strong>\n          ),\n          \n          // Emphasis/Italic\n          em: ({ children }) => (\n            <em className=\"italic text-slate-200\">\n              {children}\n            </em>\n          ),\n        }}\n      >\n        {processedContent}\n      </ReactMarkdown>\n    </div>\n  );\n};\n\nexport default MarkdownRenderer;\n"}