{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/dataSync.ts"}, "originalCode": "import React from \"react\";\nimport localforage from \"localforage\";\nimport { getUserDocuments } from \"./api\";\nimport { getFlashcardDecks } from \"./storage\";\nimport { notify } from \"./notifications\";\n\nexport interface SyncStatus {\n  lastSyncTime: number | null;\n  isOnline: boolean;\n  pendingUploads: number;\n  pendingDownloads: number;\n  syncInProgress: boolean;\n}\n\nexport interface SyncOptions {\n  forceSync?: boolean;\n  backgroundSync?: boolean;\n  onProgress?: (progress: number) => void;\n  onStatusChange?: (status: SyncStatus) => void;\n}\n\nclass DataSyncManager {\n  private syncStatus: SyncStatus = {\n    lastSyncTime: null,\n    isOnline: navigator.onLine,\n    pendingUploads: 0,\n    pendingDownloads: 0,\n    syncInProgress: false,\n  };\n\n  private statusCallbacks: Set<(status: SyncStatus) => void> = new Set();\n  private syncInterval: NodeJS.Timeout | null = null;\n\n  constructor() {\n    this.initializeEventListeners();\n    this.loadSyncStatus();\n    this.startPeriodicSync();\n  }\n\n  private initializeEventListeners() {\n    window.addEventListener(\"online\", () => {\n      this.updateSyncStatus({ isOnline: true });\n      this.triggerSync({ backgroundSync: true });\n    });\n\n    window.addEventListener(\"offline\", () => {\n      this.updateSyncStatus({ isOnline: false });\n    });\n\n    document.addEventListener(\"visibilitychange\", () => {\n      if (!document.hidden && this.syncStatus.isOnline) {\n        this.triggerSync({ backgroundSync: true });\n      }\n    });\n  }\n\n  private async loadSyncStatus() {\n    try {\n      const stored = await localforage.getItem<SyncStatus>(\"sync-status\");\n      if (stored) {\n        this.syncStatus = { ...this.syncStatus, ...stored };\n      }\n    } catch (error) {\n      console.warn(\"Failed to load sync status:\", error);\n    }\n  }\n\n  private async saveSyncStatus() {\n    try {\n      await localforage.setItem(\"sync-status\", this.syncStatus);\n    } catch (error) {\n      console.warn(\"Failed to save sync status:\", error);\n    }\n  }\n\n  private updateSyncStatus(updates: Partial<SyncStatus>) {\n    this.syncStatus = { ...this.syncStatus, ...updates };\n    this.saveSyncStatus();\n    this.notifyStatusChange();\n  }\n\n  private notifyStatusChange() {\n    this.statusCallbacks.forEach((callback) => {\n      try {\n        callback(this.syncStatus);\n      } catch (error) {\n        console.error(\"Error in sync status callback:\", error);\n      }\n    });\n  }\n\n  private startPeriodicSync() {\n    this.syncInterval = setInterval(() => {\n      if (this.syncStatus.isOnline && !this.syncStatus.syncInProgress) {\n        this.triggerSync({ backgroundSync: true });\n      }\n    }, 5 * 60 * 1000); // Every 5 minutes\n  }\n\n  public onStatusChange(callback: (status: SyncStatus) => void) {\n    this.statusCallbacks.add(callback);\n    return () => this.statusCallbacks.delete(callback);\n  }\n\n  public getSyncStatus(): SyncStatus {\n    return { ...this.syncStatus };\n  }\n\n  public async triggerSync(options: SyncOptions = {}): Promise<void> {\n    if (!this.syncStatus.isOnline) {\n      if (!options.backgroundSync) {\n        notify.warning({\n          title: \"Offline\",\n          description:\n            \"Cannot sync while offline. Data will sync when connection is restored.\",\n        });\n      }\n      return;\n    }\n\n    if (this.syncStatus.syncInProgress) {\n      if (!options.backgroundSync) {\n        notify.info({\n          title: \"Sync in progress\",\n          description: \"A sync operation is already running.\",\n        });\n      }\n      return;\n    }\n\n    this.updateSyncStatus({ syncInProgress: true });\n\n    try {\n      await this.performSync(options);\n      this.updateSyncStatus({\n        lastSyncTime: Date.now(),\n        syncInProgress: false,\n      });\n\n      if (!options.backgroundSync) {\n        notify.success({\n          title: \"Sync complete\",\n          description: \"Your data has been synchronized successfully.\",\n        });\n      }\n    } catch (error) {\n      this.updateSyncStatus({ syncInProgress: false });\n\n      if (!options.backgroundSync) {\n        notify.error({\n          title: \"Sync failed\",\n          description:\n            error instanceof Error ? error.message : \"Unknown sync error\",\n        });\n      }\n\n      console.error(\"Sync failed:\", error);\n      throw error;\n    }\n  }\n\n  private async performSync(options: SyncOptions): Promise<void> {\n    const totalSteps = 4;\n    let currentStep = 0;\n\n    const updateProgress = () => {\n      currentStep++;\n      const progress = (currentStep / totalSteps) * 100;\n      options.onProgress?.(progress);\n    };\n\n    try {\n      await this.syncDocuments();\n      updateProgress();\n\n      await this.syncFlashcards();\n      updateProgress();\n\n      await this.syncQuizzes();\n      updateProgress();\n\n      await this.cleanupOrphanedData();\n      updateProgress();\n    } catch (error) {\n      console.error(\"Sync operation failed:\", error);\n      throw error;\n    }\n  }\n\n  private async syncDocuments(): Promise<void> {\n    try {\n      const cloudDocuments = await getUserDocuments();\n      const localDocuments =\n        (await localforage.getItem<any[]>(\"uploaded-documents\")) || [];\n\n      const localDocumentIds = new Set(localDocuments.map((doc) => doc.id));\n      const cloudDocumentIds = new Set(cloudDocuments.map((doc) => doc.id));\n\n      const documentsToDownload = cloudDocuments.filter(\n        (doc) => !localDocumentIds.has(doc.id)\n      );\n      const documentsToRemove = localDocuments.filter(\n        (doc) => !cloudDocumentIds.has(doc.id)\n      );\n\n      if (documentsToDownload.length > 0) {\n        const updatedLocalDocuments = [\n          ...localDocuments,\n          ...documentsToDownload,\n        ];\n        await localforage.setItem(\"uploaded-documents\", updatedLocalDocuments);\n      }\n\n      if (documentsToRemove.length > 0) {\n        const filteredLocalDocuments = localDocuments.filter((doc) =>\n          cloudDocumentIds.has(doc.id)\n        );\n        await localforage.setItem(\"uploaded-documents\", filteredLocalDocuments);\n      }\n\n      this.updateSyncStatus({\n        pendingDownloads: 0,\n      });\n    } catch (error) {\n      console.error(\"Document sync failed:\", error);\n      throw new Error(\"Failed to sync documents\");\n    }\n  }\n\n  private async syncFlashcards(): Promise<void> {\n    try {\n      const localDecks = await getFlashcardDecks();\n\n      for (const deck of localDecks) {\n        if (!deck.synced) {\n          // Mark as synced after successful save to avoid duplicate uploads\n          deck.synced = true;\n          await localforage.setItem(`flashcard-deck-${deck.id}`, deck);\n        }\n      }\n\n      this.updateSyncStatus({\n        pendingUploads: 0,\n      });\n    } catch (error) {\n      console.error(\"Flashcard sync failed:\", error);\n      throw new Error(\"Failed to sync flashcards\");\n    }\n  }\n\n  private async syncQuizzes(): Promise<void> {\n    try {\n      // Quiz sync implementation would go here\n      // For now, just mark as complete\n    } catch (error) {\n      console.error(\"Quiz sync failed:\", error);\n      throw new Error(\"Failed to sync quizzes\");\n    }\n  }\n\n  private async cleanupOrphanedData(): Promise<void> {\n    try {\n      // Clean up any orphaned local data\n      const allKeys = await localforage.keys();\n      const documentKeys = allKeys.filter((key) =>\n        key.startsWith(\"document-content-\")\n      );\n      const validDocuments =\n        (await localforage.getItem<any[]>(\"uploaded-documents\")) || [];\n      const validDocumentIds = new Set(validDocuments.map((doc) => doc.id));\n\n      for (const key of documentKeys) {\n        const documentId = key.replace(\"document-content-\", \"\");\n        if (!validDocumentIds.has(documentId)) {\n          await localforage.removeItem(key);\n        }\n      }\n    } catch (error) {\n      console.error(\"Cleanup failed:\", error);\n    }\n  }\n\n  public async clearAllData(): Promise<void> {\n    try {\n      await localforage.clear();\n      this.updateSyncStatus({\n        lastSyncTime: null,\n        pendingUploads: 0,\n        pendingDownloads: 0,\n      });\n\n      notify.success({\n        title: \"Data cleared\",\n        description: \"All local data has been cleared successfully.\",\n      });\n    } catch (error) {\n      notify.error({\n        title: \"Clear failed\",\n        description: \"Failed to clear local data.\",\n      });\n      throw error;\n    }\n  }\n\n  public async exportAllData(): Promise<Blob> {\n    try {\n      const allData: Record<string, any> = {};\n      const keys = await localforage.keys();\n\n      for (const key of keys) {\n        allData[key] = await localforage.getItem(key);\n      }\n\n      const dataString = JSON.stringify(allData, null, 2);\n      return new Blob([dataString], { type: \"application/json\" });\n    } catch (error) {\n      console.error(\"Export failed:\", error);\n      throw new Error(\"Failed to export data\");\n    }\n  }\n\n  public async importData(jsonData: string): Promise<void> {\n    try {\n      const data = JSON.parse(jsonData);\n\n      for (const [key, value] of Object.entries(data)) {\n        await localforage.setItem(key, value);\n      }\n\n      await this.triggerSync({ forceSync: true });\n\n      notify.success({\n        title: \"Data imported\",\n        description: \"Your data has been imported and synced successfully.\",\n      });\n    } catch (error) {\n      notify.error({\n        title: \"Import failed\",\n        description: \"Failed to import data. Please check the file format.\",\n      });\n      throw error;\n    }\n  }\n\n  public destroy() {\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval);\n    }\n    window.removeEventListener(\"online\", this.triggerSync);\n    window.removeEventListener(\"offline\", this.triggerSync);\n    document.removeEventListener(\"visibilitychange\", this.triggerSync);\n  }\n}\n\nexport const dataSync = new DataSyncManager();\n\nexport const useSyncStatus = () => {\n  const [status, setStatus] = React.useState<SyncStatus>(\n    dataSync.getSyncStatus()\n  );\n\n  React.useEffect(() => {\n    return dataSync.onStatusChange(setStatus);\n  }, []);\n\n  return status;\n};\n\nexport const syncOperations = {\n  triggerSync: (options?: SyncOptions) => dataSync.triggerSync(options),\n  clearAllData: () => dataSync.clearAllData(),\n  exportAllData: () => dataSync.exportAllData(),\n  importData: (data: string) => dataSync.importData(data),\n  getSyncStatus: () => dataSync.getSyncStatus(),\n};\n", "modifiedCode": "import React from \"react\";\nimport localforage from \"localforage\";\nimport { getUserDocuments } from \"./api\";\nimport { getFlashcardDecks } from \"./storage\";\nimport { notify } from \"./notifications\";\n\nexport interface SyncStatus {\n  lastSyncTime: number | null;\n  isOnline: boolean;\n  pendingUploads: number;\n  pendingDownloads: number;\n  syncInProgress: boolean;\n}\n\nexport interface SyncOptions {\n  forceSync?: boolean;\n  backgroundSync?: boolean;\n  onProgress?: (progress: number) => void;\n  onStatusChange?: (status: SyncStatus) => void;\n}\n\nclass DataSyncManager {\n  private syncStatus: SyncStatus = {\n    lastSyncTime: null,\n    isOnline: navigator.onLine,\n    pendingUploads: 0,\n    pendingDownloads: 0,\n    syncInProgress: false,\n  };\n\n  private statusCallbacks: Set<(status: SyncStatus) => void> = new Set();\n  private syncInterval: NodeJS.Timeout | null = null;\n\n  constructor() {\n    this.initializeEventListeners();\n    this.loadSyncStatus();\n    this.startPeriodicSync();\n  }\n\n  private initializeEventListeners() {\n    window.addEventListener(\"online\", () => {\n      this.updateSyncStatus({ isOnline: true });\n      this.triggerSync({ backgroundSync: true });\n    });\n\n    window.addEventListener(\"offline\", () => {\n      this.updateSyncStatus({ isOnline: false });\n    });\n\n    document.addEventListener(\"visibilitychange\", () => {\n      if (!document.hidden && this.syncStatus.isOnline) {\n        this.triggerSync({ backgroundSync: true });\n      }\n    });\n  }\n\n  private async loadSyncStatus() {\n    try {\n      const stored = await localforage.getItem<SyncStatus>(\"sync-status\");\n      if (stored) {\n        this.syncStatus = { ...this.syncStatus, ...stored };\n      }\n    } catch (error) {\n      console.warn(\"Failed to load sync status:\", error);\n    }\n  }\n\n  private async saveSyncStatus() {\n    try {\n      await localforage.setItem(\"sync-status\", this.syncStatus);\n    } catch (error) {\n      console.warn(\"Failed to save sync status:\", error);\n    }\n  }\n\n  private updateSyncStatus(updates: Partial<SyncStatus>) {\n    this.syncStatus = { ...this.syncStatus, ...updates };\n    this.saveSyncStatus();\n    this.notifyStatusChange();\n  }\n\n  private notifyStatusChange() {\n    this.statusCallbacks.forEach((callback) => {\n      try {\n        callback(this.syncStatus);\n      } catch (error) {\n        console.error(\"Error in sync status callback:\", error);\n      }\n    });\n  }\n\n  private startPeriodicSync() {\n    this.syncInterval = setInterval(() => {\n      if (this.syncStatus.isOnline && !this.syncStatus.syncInProgress) {\n        this.triggerSync({ backgroundSync: true });\n      }\n    }, 5 * 60 * 1000); // Every 5 minutes\n  }\n\n  public onStatusChange(callback: (status: SyncStatus) => void) {\n    this.statusCallbacks.add(callback);\n    return () => this.statusCallbacks.delete(callback);\n  }\n\n  public getSyncStatus(): SyncStatus {\n    return { ...this.syncStatus };\n  }\n\n  public async triggerSync(options: SyncOptions = {}): Promise<void> {\n    if (!this.syncStatus.isOnline) {\n      if (!options.backgroundSync) {\n        notify.warning({\n          title: \"Offline\",\n          description:\n            \"Cannot sync while offline. Data will sync when connection is restored.\",\n        });\n      }\n      return;\n    }\n\n    if (this.syncStatus.syncInProgress) {\n      if (!options.backgroundSync) {\n        notify.info({\n          title: \"Sync in progress\",\n          description: \"A sync operation is already running.\",\n        });\n      }\n      return;\n    }\n\n    this.updateSyncStatus({ syncInProgress: true });\n\n    try {\n      await this.performSync(options);\n      this.updateSyncStatus({\n        lastSyncTime: Date.now(),\n        syncInProgress: false,\n      });\n\n      if (!options.backgroundSync) {\n        notify.success({\n          title: \"Sync complete\",\n          description: \"Your data has been synchronized successfully.\",\n        });\n      }\n    } catch (error) {\n      this.updateSyncStatus({ syncInProgress: false });\n\n      if (!options.backgroundSync) {\n        notify.error({\n          title: \"Sync failed\",\n          description:\n            error instanceof Error ? error.message : \"Unknown sync error\",\n        });\n      }\n\n      console.error(\"Sync failed:\", error);\n      throw error;\n    }\n  }\n\n  private async performSync(options: SyncOptions): Promise<void> {\n    const totalSteps = 4;\n    let currentStep = 0;\n\n    const updateProgress = () => {\n      currentStep++;\n      const progress = (currentStep / totalSteps) * 100;\n      options.onProgress?.(progress);\n    };\n\n    try {\n      await this.syncDocuments();\n      updateProgress();\n\n      await this.syncFlashcards();\n      updateProgress();\n\n      await this.syncQuizzes();\n      updateProgress();\n\n      await this.cleanupOrphanedData();\n      updateProgress();\n    } catch (error) {\n      console.error(\"Sync operation failed:\", error);\n      throw error;\n    }\n  }\n\n  private async syncDocuments(): Promise<void> {\n    try {\n      const cloudDocuments = await getUserDocuments();\n      const localDocuments =\n        (await localforage.getItem<any[]>(\"uploaded-documents\")) || [];\n\n      const localDocumentIds = new Set(localDocuments.map((doc) => doc.id));\n      const cloudDocumentIds = new Set(cloudDocuments.map((doc) => doc.id));\n\n      const documentsToDownload = cloudDocuments.filter(\n        (doc) => !localDocumentIds.has(doc.id)\n      );\n      const documentsToRemove = localDocuments.filter(\n        (doc) => !cloudDocumentIds.has(doc.id)\n      );\n\n      if (documentsToDownload.length > 0) {\n        const updatedLocalDocuments = [\n          ...localDocuments,\n          ...documentsToDownload,\n        ];\n        await localforage.setItem(\"uploaded-documents\", updatedLocalDocuments);\n      }\n\n      if (documentsToRemove.length > 0) {\n        const filteredLocalDocuments = localDocuments.filter((doc) =>\n          cloudDocumentIds.has(doc.id)\n        );\n        await localforage.setItem(\"uploaded-documents\", filteredLocalDocuments);\n      }\n\n      this.updateSyncStatus({\n        pendingDownloads: 0,\n      });\n    } catch (error) {\n      console.error(\"Document sync failed:\", error);\n      throw new Error(\"Failed to sync documents\");\n    }\n  }\n\n  private async syncFlashcards(): Promise<void> {\n    try {\n      const localDecks = await getFlashcardDecks();\n\n      for (const deck of localDecks) {\n        if (!deck.synced) {\n          // Mark as synced after successful save to avoid duplicate uploads\n          deck.synced = true;\n          await localforage.setItem(`flashcard-deck-${deck.id}`, deck);\n        }\n      }\n\n      this.updateSyncStatus({\n        pendingUploads: 0,\n      });\n    } catch (error) {\n      console.error(\"Flashcard sync failed:\", error);\n      throw new Error(\"Failed to sync flashcards\");\n    }\n  }\n\n  private async syncQuizzes(): Promise<void> {\n    try {\n      // Quiz sync implementation would go here\n      // For now, just mark as complete\n    } catch (error) {\n      console.error(\"Quiz sync failed:\", error);\n      throw new Error(\"Failed to sync quizzes\");\n    }\n  }\n\n  private async cleanupOrphanedData(): Promise<void> {\n    try {\n      // Clean up any orphaned local data\n      const allKeys = await localforage.keys();\n      const documentKeys = allKeys.filter((key) =>\n        key.startsWith(\"document-content-\")\n      );\n      const validDocuments =\n        (await localforage.getItem<any[]>(\"uploaded-documents\")) || [];\n      const validDocumentIds = new Set(validDocuments.map((doc) => doc.id));\n\n      for (const key of documentKeys) {\n        const documentId = key.replace(\"document-content-\", \"\");\n        if (!validDocumentIds.has(documentId)) {\n          await localforage.removeItem(key);\n        }\n      }\n    } catch (error) {\n      console.error(\"Cleanup failed:\", error);\n    }\n  }\n\n  public async clearAllData(): Promise<void> {\n    try {\n      await localforage.clear();\n      this.updateSyncStatus({\n        lastSyncTime: null,\n        pendingUploads: 0,\n        pendingDownloads: 0,\n      });\n\n      notify.success({\n        title: \"Data cleared\",\n        description: \"All local data has been cleared successfully.\",\n      });\n    } catch (error) {\n      notify.error({\n        title: \"Clear failed\",\n        description: \"Failed to clear local data.\",\n      });\n      throw error;\n    }\n  }\n\n  public async exportAllData(): Promise<Blob> {\n    try {\n      const allData: Record<string, any> = {};\n      const keys = await localforage.keys();\n\n      for (const key of keys) {\n        allData[key] = await localforage.getItem(key);\n      }\n\n      const dataString = JSON.stringify(allData, null, 2);\n      return new Blob([dataString], { type: \"application/json\" });\n    } catch (error) {\n      console.error(\"Export failed:\", error);\n      throw new Error(\"Failed to export data\");\n    }\n  }\n\n  public async importData(jsonData: string): Promise<void> {\n    try {\n      const data = JSON.parse(jsonData);\n\n      for (const [key, value] of Object.entries(data)) {\n        await localforage.setItem(key, value);\n      }\n\n      await this.triggerSync({ forceSync: true });\n\n      notify.success({\n        title: \"Data imported\",\n        description: \"Your data has been imported and synced successfully.\",\n      });\n    } catch (error) {\n      notify.error({\n        title: \"Import failed\",\n        description: \"Failed to import data. Please check the file format.\",\n      });\n      throw error;\n    }\n  }\n\n  public destroy() {\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval);\n    }\n    window.removeEventListener(\"online\", this.triggerSync);\n    window.removeEventListener(\"offline\", this.triggerSync);\n    document.removeEventListener(\"visibilitychange\", this.triggerSync);\n  }\n}\n\nexport const dataSync = new DataSyncManager();\n\nexport const useSyncStatus = () => {\n  const [status, setStatus] = React.useState<SyncStatus>(\n    dataSync.getSyncStatus()\n  );\n\n  React.useEffect(() => {\n    return dataSync.onStatusChange(setStatus);\n  }, []);\n\n  return status;\n};\n\nexport const syncOperations = {\n  triggerSync: (options?: SyncOptions) => dataSync.triggerSync(options),\n  clearAllData: () => dataSync.clearAllData(),\n  exportAllData: () => dataSync.exportAllData(),\n  importData: (data: string) => dataSync.importData(data),\n  getSyncStatus: () => dataSync.getSyncStatus(),\n};\n"}