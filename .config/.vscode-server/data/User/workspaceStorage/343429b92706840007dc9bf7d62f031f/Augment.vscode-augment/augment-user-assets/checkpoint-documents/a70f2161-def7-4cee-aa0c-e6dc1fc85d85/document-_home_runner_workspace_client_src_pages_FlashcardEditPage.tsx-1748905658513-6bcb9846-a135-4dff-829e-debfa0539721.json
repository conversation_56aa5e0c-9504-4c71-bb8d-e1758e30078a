{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardEditPage.tsx"}, "originalCode": "import React, { useEffect, useState } from \"react\";\nimport { useParams, useLocation } from \"wouter\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport FlashcardManager from \"@/components/flashcards/FlashcardEditManager\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { AlertTriangle } from \"lucide-react\";\nimport { supabase } from \"@/lib/supabaseClient\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface DeckData {\n  id: string;\n  name: string;\n}\n\nconst FlashcardEditPage: React.FC = () => {\n  const params = useParams<{ deckId: string }>();\n  const [, navigate] = useLocation();\n  const { user } = useAuth();\n  const deckId = params?.deckId;\n\n  const [deck, setDeck] = useState<DeckData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchDeckDataFromBackend = async () => {\n      if (!user || !deckId) {\n        setIsLoading(false);\n        if (!user) setError(\"User not authenticated.\");\n        if (!deckId) setError(\"Deck ID not provided.\");\n        return;\n      }\n\n      setError(null);\n      setIsLoading(true);\n\n      try {\n        const {\n          data: { session },\n        } = await supabase.auth.getSession();\n        const token = session?.access_token;\n\n        if (!token) {\n          setError(\"Authentication token not found. Please log in.\");\n          return;\n        }\n\n        const response = await fetch(`/api/flashcard-sets/${deckId}`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          let errorMsg = `Failed to fetch flashcard set: ${response.statusText} (Status: ${response.status})`;\n          if (response.status === 404) {\n            errorMsg = \"Flashcard set not found or you don't have access.\";\n          }\n          try {\n            const errorData = await response.json();\n            errorMsg = errorData.error || errorData.message || errorMsg;\n          } catch (e) {\n            // Ignore if response is not JSON\n          }\n          setError(errorMsg);\n          return;\n        }\n\n        const data: DeckData = await response.json();\n        setDeck(data);\n      } catch (err: any) {\n        console.error(\"Error fetching flashcard set from backend:\", err);\n        if (!error) {\n          setError(\n            err.message ||\n              \"An unexpected error occurred while fetching the flashcard set.\"\n          );\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchDeckDataFromBackend();\n  }, [user, deckId]);\n\n  const handleClose = () => {\n    navigate(\"/flashcards\");\n  };\n\n  if (isLoading) {\n    return (\n      <AppLayout title=\"Loading...\">\n        <div className=\"container mx-auto px-4 py-8 flex justify-center items-center h-64\">\n          <Spinner size=\"lg\" />\n          <span className=\"ml-3 text-slate-300\">Loading flashcard set...</span>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  if (error || !deck) {\n    return (\n      <AppLayout title=\"Error\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6 text-center\">\n              <AlertTriangle className=\"h-12 w-12 text-yellow-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium mb-2 text-purple-400\">\n                {error ? \"Error Loading Deck\" : \"Flashcard Set Not Found\"}\n              </h3>\n              <p className=\"text-slate-300 mb-4\">\n                {error ||\n                  \"The flashcard set you're looking for doesn't exist or you don't have permission to access it.\"}\n              </p>\n              <Button\n                onClick={handleClose}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Return to Flashcards\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout title={`Edit: ${deck.name}`}>\n      <div className=\"container mx-auto px-4 py-8\">\n        <FlashcardManager\n          selectedDeckId={deck.id}\n          selectedDeckName={deck.name}\n          onClose={handleClose}\n        />\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default FlashcardEditPage;\n", "modifiedCode": "import React, { useEffect, useState } from \"react\";\nimport { useParams, useLocation } from \"wouter\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport FlashcardManager from \"@/components/flashcards/FlashcardEditManager\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { AlertTriangle } from \"lucide-react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface DeckData {\n  id: string;\n  name: string;\n}\n\nconst FlashcardEditPage: React.FC = () => {\n  const params = useParams<{ deckId: string }>();\n  const [, navigate] = useLocation();\n  const { user } = useAuth();\n  const deckId = params?.deckId;\n\n  const [deck, setDeck] = useState<DeckData | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchDeckDataFromBackend = async () => {\n      if (!user || !deckId) {\n        setIsLoading(false);\n        if (!user) setError(\"User not authenticated.\");\n        if (!deckId) setError(\"Deck ID not provided.\");\n        return;\n      }\n\n      setError(null);\n      setIsLoading(true);\n\n      try {\n        const token = localStorage.getItem('auth_token');\n\n        if (!token) {\n          setError(\"Authentication token not found. Please log in.\");\n          return;\n        }\n\n        const response = await fetch(`/api/flashcard-sets/${deckId}`, {\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (!response.ok) {\n          let errorMsg = `Failed to fetch flashcard set: ${response.statusText} (Status: ${response.status})`;\n          if (response.status === 404) {\n            errorMsg = \"Flashcard set not found or you don't have access.\";\n          }\n          try {\n            const errorData = await response.json();\n            errorMsg = errorData.error || errorData.message || errorMsg;\n          } catch (e) {\n            // Ignore if response is not JSON\n          }\n          setError(errorMsg);\n          return;\n        }\n\n        const data: DeckData = await response.json();\n        setDeck(data);\n      } catch (err: any) {\n        console.error(\"Error fetching flashcard set from backend:\", err);\n        if (!error) {\n          setError(\n            err.message ||\n              \"An unexpected error occurred while fetching the flashcard set.\"\n          );\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchDeckDataFromBackend();\n  }, [user, deckId]);\n\n  const handleClose = () => {\n    navigate(\"/flashcards\");\n  };\n\n  if (isLoading) {\n    return (\n      <AppLayout title=\"Loading...\">\n        <div className=\"container mx-auto px-4 py-8 flex justify-center items-center h-64\">\n          <Spinner size=\"lg\" />\n          <span className=\"ml-3 text-slate-300\">Loading flashcard set...</span>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  if (error || !deck) {\n    return (\n      <AppLayout title=\"Error\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <Card className=\"bg-slate-800 border-slate-700\">\n            <CardContent className=\"p-6 text-center\">\n              <AlertTriangle className=\"h-12 w-12 text-yellow-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium mb-2 text-purple-400\">\n                {error ? \"Error Loading Deck\" : \"Flashcard Set Not Found\"}\n              </h3>\n              <p className=\"text-slate-300 mb-4\">\n                {error ||\n                  \"The flashcard set you're looking for doesn't exist or you don't have permission to access it.\"}\n              </p>\n              <Button\n                onClick={handleClose}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Return to Flashcards\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </AppLayout>\n    );\n  }\n\n  return (\n    <AppLayout title={`Edit: ${deck.name}`}>\n      <div className=\"container mx-auto px-4 py-8\">\n        <FlashcardManager\n          selectedDeckId={deck.id}\n          selectedDeckName={deck.name}\n          onClose={handleClose}\n        />\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default FlashcardEditPage;\n"}