{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuestionForm.tsx"}, "originalCode": "import React, { useState, FormEvent } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables, TablesInsert, Enums } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\ntype QuizQuestionInsert = TablesInsert<\"quiz_questions\">;\ntype QuestionType = Enums<\"question_type\">;\n\ninterface McqOption {\n  text: string;\n  is_correct: boolean;\n  id?: string;\n}\n\ninterface QuestionFormProps {\n  selectedQuizId: string;\n  editingQuestion?: QuizQuestion | null;\n  onQuestionSaved: () => void;\n  onCancel: () => void;\n}\n\nexport const QuestionForm: React.FC<QuestionFormProps> = ({\n  selectedQuizId,\n  editingQuestion,\n  onQuestionSaved,\n  onCancel,\n}) => {\n  const { user } = useAuth();\n  const [questionText, setQuestionText] = useState(\n    editingQuestion?.question_text || \"\"\n  );\n  const [questionType, setQuestionType] = useState<QuestionType>(\n    editingQuestion?.type || \"multiple_choice\"\n  );\n  const [mcqOptions, setMcqOptions] = useState<McqOption[]>(() => {\n    if (\n      editingQuestion?.type === \"multiple_choice\" &&\n      editingQuestion.options &&\n      Array.isArray(editingQuestion.options)\n    ) {\n      return (editingQuestion.options as Array<Omit<McqOption, \"id\">>).map(\n        (opt) => ({\n          ...opt,\n          id: crypto.randomUUID(),\n        })\n      );\n    }\n    return [{ text: \"\", is_correct: false, id: crypto.randomUUID() }];\n  });\n  const [selectAllOptions, setSelectAllOptions] = useState<McqOption[]>(() => {\n    if (\n      editingQuestion?.type === \"select_all_that_apply\" &&\n      editingQuestion.options &&\n      Array.isArray(editingQuestion.options)\n    ) {\n      return (editingQuestion.options as Array<Omit<McqOption, \"id\">>).map(\n        (opt) => ({\n          ...opt,\n          id: crypto.randomUUID(),\n        })\n      );\n    }\n    return [{ text: \"\", is_correct: false, id: crypto.randomUUID() }];\n  });\n  const [correctAnswerTF, setCorrectAnswerTF] = useState(\n    editingQuestion?.correct_answer || \"true\"\n  );\n  const [correctAnswerShort, setCorrectAnswerShort] = useState(\n    editingQuestion?.correct_answer || \"\"\n  );\n  const [explanation, setExplanation] = useState(\n    editingQuestion?.explanation || \"\"\n  );\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleMcqOptionChange = (\n    index: number,\n    field: keyof McqOption,\n    value: string | boolean\n  ) => {\n    const newOptions = [...mcqOptions];\n    (newOptions[index] as any)[field] = value;\n    if (field === \"is_correct\" && value === true) {\n      newOptions.forEach((opt, i) => {\n        if (i !== index) opt.is_correct = false;\n      });\n    }\n    setMcqOptions(newOptions);\n  };\n\n  const addMcqOption = () =>\n    setMcqOptions([\n      ...mcqOptions,\n      { text: \"\", is_correct: false, id: crypto.randomUUID() },\n    ]);\n\n  const removeMcqOption = (index: number) =>\n    setMcqOptions(mcqOptions.filter((_, i) => i !== index));\n\n  const handleSelectAllOptionChange = (\n    index: number,\n    field: keyof McqOption,\n    value: string | boolean\n  ) => {\n    const newOptions = [...selectAllOptions];\n    (newOptions[index] as any)[field] = value;\n    setSelectAllOptions(newOptions);\n  };\n\n  const addSelectAllOption = () =>\n    setSelectAllOptions([\n      ...selectAllOptions,\n      { text: \"\", is_correct: false, id: crypto.randomUUID() },\n    ]);\n\n  const removeSelectAllOption = (index: number) =>\n    setSelectAllOptions(selectAllOptions.filter((_, i) => i !== index));\n\n  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    if (!questionText.trim()) {\n      setError(\"Question text is required.\");\n      return;\n    }\n    setLoading(true);\n    setError(null);\n\n    let optionsPayload: any = null;\n    let correctAnswerPayload: string | null = null;\n\n    if (questionType === \"multiple_choice\") {\n      if (mcqOptions.some((opt) => !opt.text.trim())) {\n        setError(\"All MCQ option texts are required.\");\n        setLoading(false);\n        return;\n      }\n      if (!mcqOptions.some((opt) => opt.is_correct)) {\n        setError(\"One MCQ option must be marked correct.\");\n        setLoading(false);\n        return;\n      }\n      optionsPayload = mcqOptions.map(({ id, ...rest }) => rest);\n    } else if (questionType === \"select_all_that_apply\") {\n      if (selectAllOptions.some((opt) => !opt.text.trim())) {\n        setError(\"All select-all option texts are required.\");\n        setLoading(false);\n        return;\n      }\n      if (!selectAllOptions.some((opt) => opt.is_correct)) {\n        setError(\"At least one select-all option must be marked correct.\");\n        setLoading(false);\n        return;\n      }\n      optionsPayload = selectAllOptions.map(({ id, ...rest }) => rest);\n    } else if (questionType === \"true_false\") {\n      correctAnswerPayload = correctAnswerTF;\n    } else if (questionType === \"short_answer\") {\n      if (!correctAnswerShort.trim()) {\n        setError(\"Correct answer for short answer is required.\");\n        setLoading(false);\n        return;\n      }\n      correctAnswerPayload = correctAnswerShort;\n    }\n\n    const questionData: Omit<QuizQuestionInsert, \"quiz_id\" | \"user_id\"> = {\n      question_text: questionText,\n      type: questionType,\n      options: optionsPayload,\n      correct_answer: correctAnswerPayload,\n      explanation: explanation || null,\n    };\n\n    try {\n      if (editingQuestion) {\n        const { error: updateError } = await supabase\n          .from(\"quiz_questions\")\n          .update(questionData)\n          .match({ id: editingQuestion.id, user_id: user?.id });\n        if (updateError) throw updateError;\n      } else {\n        const finalQuestionData: QuizQuestionInsert = {\n          ...questionData,\n          quiz_id: selectedQuizId,\n          user_id: user!.id,\n        };\n        const { error: insertError } = await supabase\n          .from(\"quiz_questions\")\n          .insert(finalQuestionData);\n        if (insertError) throw insertError;\n      }\n      onQuestionSaved();\n    } catch (err: any) {\n      setError(err.message || \"Failed to save question.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-slate-900 border border-slate-600 rounded-xl p-6 shadow-lg\">\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"flex items-center justify-between border-b border-slate-700 pb-4\">\n          <h5 className=\"text-lg font-bold text-slate-200 flex items-center\">\n            {editingQuestion ? (\n              <>\n                <svg\n                  className=\"w-5 h-5 mr-2 text-yellow-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n                </svg>\n                Edit Question\n              </>\n            ) : (\n              <>\n                <svg\n                  className=\"w-5 h-5 mr-2 text-green-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                Add New Question\n              </>\n            )}\n          </h5>\n          {editingQuestion && (\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              onClick={onCancel}\n              className=\"text-slate-400 hover:text-slate-300\"\n            >\n              Cancel\n            </Button>\n          )}\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/20 border border-red-500/30 text-red-400 text-sm p-3 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"lg:col-span-2\">\n            <Label\n              htmlFor=\"questionText\"\n              className=\"text-slate-300 font-semibold\"\n            >\n              Question Text*\n            </Label>\n            <textarea\n              id=\"questionText\"\n              value={questionText}\n              onChange={(e) => setQuestionText(e.target.value)}\n              required\n              rows={4}\n              className=\"block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors resize-none\"\n              placeholder=\"Enter your question here...\"\n            />\n          </div>\n          <div>\n            <Label\n              htmlFor=\"questionType\"\n              className=\"text-slate-300 font-semibold\"\n            >\n              Question Type*\n            </Label>\n            <select\n              id=\"questionType\"\n              value={questionType}\n              onChange={(e) => setQuestionType(e.target.value as QuestionType)}\n              className=\"block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors\"\n            >\n              <option value=\"multiple_choice\">📝 Multiple Choice</option>\n              <option value=\"select_all_that_apply\">\n                ☑️ Select All That Apply\n              </option>\n              <option value=\"true_false\">✓ True/False</option>\n              <option value=\"short_answer\">💭 Short Answer</option>\n            </select>\n          </div>\n        </div>\n\n        {questionType === \"multiple_choice\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <Label className=\"text-slate-300 font-semibold flex items-center\">\n                <svg\n                  className=\"w-4 h-4 mr-2 text-blue-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                Multiple Choice Options\n              </Label>\n              <span className=\"text-xs text-slate-400 bg-slate-800 px-2 py-1 rounded\">\n                Select one correct answer\n              </span>\n            </div>\n            <div className=\"space-y-3\">\n              {mcqOptions.map((opt, index) => (\n                <div\n                  key={opt.id}\n                  className=\"flex items-center space-x-3 bg-slate-800/50 p-3 rounded-lg border border-slate-600\"\n                >\n                  <span className=\"text-slate-400 font-mono text-sm w-6\">\n                    {String.fromCharCode(65 + index)}.\n                  </span>\n                  <Input\n                    type=\"text\"\n                    placeholder={`Enter option ${index + 1}`}\n                    value={opt.text}\n                    onChange={(e) =>\n                      handleMcqOptionChange(index, \"text\", e.target.value)\n                    }\n                    required\n                    className=\"flex-grow bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500\"\n                  />\n                  <label className=\"flex items-center space-x-2 cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={opt.is_correct}\n                      onChange={(e) =>\n                        handleMcqOptionChange(\n                          index,\n                          \"is_correct\",\n                          e.target.checked\n                        )\n                      }\n                      className=\"h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500\"\n                    />\n                    <span className=\"text-sm text-slate-300\">Correct</span>\n                  </label>\n                  {mcqOptions.length > 1 && (\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeMcqOption(index)}\n                      className=\"text-red-400 hover:text-red-300 p-1\"\n                    >\n                      ×\n                    </Button>\n                  )}\n                </div>\n              ))}\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={addMcqOption}\n                className=\"border-blue-500/30 text-blue-400\"\n              >\n                Add Option\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {questionType === \"select_all_that_apply\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <Label className=\"text-slate-300 font-semibold flex items-center\">\n                <svg\n                  className=\"w-4 h-4 mr-2 text-green-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                Select All That Apply Options\n              </Label>\n              <span className=\"text-xs text-slate-400 bg-slate-800 px-2 py-1 rounded\">\n                Multiple correct answers allowed\n              </span>\n            </div>\n            <div className=\"space-y-3\">\n              {selectAllOptions.map((opt, index) => (\n                <div\n                  key={opt.id}\n                  className=\"flex items-center space-x-3 bg-slate-800/50 p-3 rounded-lg border border-slate-600\"\n                >\n                  <span className=\"text-slate-400 font-mono text-sm w-6\">\n                    {index + 1}.\n                  </span>\n                  <Input\n                    type=\"text\"\n                    placeholder={`Enter option ${index + 1}`}\n                    value={opt.text}\n                    onChange={(e) =>\n                      handleSelectAllOptionChange(index, \"text\", e.target.value)\n                    }\n                    required\n                    className=\"flex-grow bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500\"\n                  />\n                  <label className=\"flex items-center space-x-2 cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={opt.is_correct}\n                      onChange={(e) =>\n                        handleSelectAllOptionChange(\n                          index,\n                          \"is_correct\",\n                          e.target.checked\n                        )\n                      }\n                      className=\"h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500\"\n                    />\n                    <span className=\"text-sm text-slate-300\">Correct</span>\n                  </label>\n                  {selectAllOptions.length > 1 && (\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeSelectAllOption(index)}\n                      className=\"text-red-400 hover:text-red-300 p-1\"\n                    >\n                      ×\n                    </Button>\n                  )}\n                </div>\n              ))}\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={addSelectAllOption}\n                className=\"border-blue-500/30 text-blue-400\"\n              >\n                Add Option\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {questionType === \"true_false\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <Label className=\"text-slate-300 font-semibold mb-3 flex items-center\">\n              <svg\n                className=\"w-4 h-4 mr-2 text-yellow-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              Correct Answer\n            </Label>\n            <div className=\"grid grid-cols-2 gap-3\">\n              {[\"true\", \"false\"].map((value) => (\n                <label\n                  key={value}\n                  className={`flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all ${\n                    correctAnswerTF === value\n                      ? \"border-purple-500 bg-purple-900/20 text-purple-300\"\n                      : \"border-slate-600 bg-slate-800/50 text-slate-300 hover:border-slate-500\"\n                  }`}\n                >\n                  <input\n                    type=\"radio\"\n                    name=\"trueFalse\"\n                    value={value}\n                    checked={correctAnswerTF === value}\n                    onChange={(e) => setCorrectAnswerTF(e.target.value)}\n                    className=\"sr-only\"\n                  />\n                  <span className=\"font-medium capitalize\">{value}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {questionType === \"short_answer\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <Label\n              htmlFor=\"correctShort\"\n              className=\"text-slate-300 font-semibold mb-3 flex items-center\"\n            >\n              <svg\n                className=\"w-4 h-4 mr-2 text-orange-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              Correct Answer*\n            </Label>\n            <Input\n              id=\"correctShort\"\n              value={correctAnswerShort}\n              onChange={(e) => setCorrectAnswerShort(e.target.value)}\n              required\n              placeholder=\"Enter the expected answer...\"\n              className=\"bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500\"\n            />\n          </div>\n        )}\n\n        <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n          <Label\n            htmlFor=\"explanation\"\n            className=\"text-slate-300 font-semibold mb-3 flex items-center\"\n          >\n            <svg\n              className=\"w-4 h-4 mr-2 text-indigo-400\"\n              fill=\"currentColor\"\n              viewBox=\"0 0 20 20\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n            Explanation <span className=\"text-slate-500\">(Optional)</span>\n          </Label>\n          <textarea\n            id=\"explanation\"\n            value={explanation}\n            onChange={(e) => setExplanation(e.target.value)}\n            rows={3}\n            placeholder=\"Provide an explanation for the answer (helps students learn)...\"\n            className=\"block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors resize-none\"\n          />\n        </div>\n\n        <div className=\"flex items-center justify-between pt-4 border-t border-slate-700\">\n          <Button\n            type=\"submit\"\n            disabled={loading}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            {loading ? (\n              <>\n                <svg\n                  className=\"animate-spin h-4 w-4 mr-2\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <circle\n                    className=\"opacity-25\"\n                    cx=\"12\"\n                    cy=\"12\"\n                    r=\"10\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"4\"\n                  ></circle>\n                  <path\n                    className=\"opacity-75\"\n                    fill=\"currentColor\"\n                    d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                  ></path>\n                </svg>\n                {editingQuestion ? \"Saving...\" : \"Adding...\"}\n              </>\n            ) : (\n              <>\n                <svg\n                  className=\"w-4 h-4 mr-2\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                {editingQuestion ? \"Save Changes\" : \"Add Question\"}\n              </>\n            )}\n          </Button>\n          {editingQuestion && (\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onCancel}\n              className=\"border-slate-600 text-slate-300 hover:text-slate-100\"\n            >\n              Cancel\n            </Button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useState, FormEvent } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables, TablesInsert, Enums } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\ntype QuizQuestionInsert = TablesInsert<\"quiz_questions\">;\ntype QuestionType = Enums<\"question_type\">;\n\ninterface McqOption {\n  text: string;\n  is_correct: boolean;\n  id?: string;\n}\n\ninterface QuestionFormProps {\n  selectedQuizId: string;\n  editingQuestion?: QuizQuestion | null;\n  onQuestionSaved: () => void;\n  onCancel: () => void;\n}\n\nexport const QuestionForm: React.FC<QuestionFormProps> = ({\n  selectedQuizId,\n  editingQuestion,\n  onQuestionSaved,\n  onCancel,\n}) => {\n  const { user } = useAuth();\n  const [questionText, setQuestionText] = useState(\n    editingQuestion?.question_text || \"\"\n  );\n  const [questionType, setQuestionType] = useState<QuestionType>(\n    editingQuestion?.type || \"multiple_choice\"\n  );\n  const [mcqOptions, setMcqOptions] = useState<McqOption[]>(() => {\n    if (\n      editingQuestion?.type === \"multiple_choice\" &&\n      editingQuestion.options &&\n      Array.isArray(editingQuestion.options)\n    ) {\n      return (editingQuestion.options as Array<Omit<McqOption, \"id\">>).map(\n        (opt) => ({\n          ...opt,\n          id: crypto.randomUUID(),\n        })\n      );\n    }\n    return [{ text: \"\", is_correct: false, id: crypto.randomUUID() }];\n  });\n  const [selectAllOptions, setSelectAllOptions] = useState<McqOption[]>(() => {\n    if (\n      editingQuestion?.type === \"select_all_that_apply\" &&\n      editingQuestion.options &&\n      Array.isArray(editingQuestion.options)\n    ) {\n      return (editingQuestion.options as Array<Omit<McqOption, \"id\">>).map(\n        (opt) => ({\n          ...opt,\n          id: crypto.randomUUID(),\n        })\n      );\n    }\n    return [{ text: \"\", is_correct: false, id: crypto.randomUUID() }];\n  });\n  const [correctAnswerTF, setCorrectAnswerTF] = useState(\n    editingQuestion?.correct_answer || \"true\"\n  );\n  const [correctAnswerShort, setCorrectAnswerShort] = useState(\n    editingQuestion?.correct_answer || \"\"\n  );\n  const [explanation, setExplanation] = useState(\n    editingQuestion?.explanation || \"\"\n  );\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleMcqOptionChange = (\n    index: number,\n    field: keyof McqOption,\n    value: string | boolean\n  ) => {\n    const newOptions = [...mcqOptions];\n    (newOptions[index] as any)[field] = value;\n    if (field === \"is_correct\" && value === true) {\n      newOptions.forEach((opt, i) => {\n        if (i !== index) opt.is_correct = false;\n      });\n    }\n    setMcqOptions(newOptions);\n  };\n\n  const addMcqOption = () =>\n    setMcqOptions([\n      ...mcqOptions,\n      { text: \"\", is_correct: false, id: crypto.randomUUID() },\n    ]);\n\n  const removeMcqOption = (index: number) =>\n    setMcqOptions(mcqOptions.filter((_, i) => i !== index));\n\n  const handleSelectAllOptionChange = (\n    index: number,\n    field: keyof McqOption,\n    value: string | boolean\n  ) => {\n    const newOptions = [...selectAllOptions];\n    (newOptions[index] as any)[field] = value;\n    setSelectAllOptions(newOptions);\n  };\n\n  const addSelectAllOption = () =>\n    setSelectAllOptions([\n      ...selectAllOptions,\n      { text: \"\", is_correct: false, id: crypto.randomUUID() },\n    ]);\n\n  const removeSelectAllOption = (index: number) =>\n    setSelectAllOptions(selectAllOptions.filter((_, i) => i !== index));\n\n  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    if (!questionText.trim()) {\n      setError(\"Question text is required.\");\n      return;\n    }\n    setLoading(true);\n    setError(null);\n\n    let optionsPayload: any = null;\n    let correctAnswerPayload: string | null = null;\n\n    if (questionType === \"multiple_choice\") {\n      if (mcqOptions.some((opt) => !opt.text.trim())) {\n        setError(\"All MCQ option texts are required.\");\n        setLoading(false);\n        return;\n      }\n      if (!mcqOptions.some((opt) => opt.is_correct)) {\n        setError(\"One MCQ option must be marked correct.\");\n        setLoading(false);\n        return;\n      }\n      optionsPayload = mcqOptions.map(({ id, ...rest }) => rest);\n    } else if (questionType === \"select_all_that_apply\") {\n      if (selectAllOptions.some((opt) => !opt.text.trim())) {\n        setError(\"All select-all option texts are required.\");\n        setLoading(false);\n        return;\n      }\n      if (!selectAllOptions.some((opt) => opt.is_correct)) {\n        setError(\"At least one select-all option must be marked correct.\");\n        setLoading(false);\n        return;\n      }\n      optionsPayload = selectAllOptions.map(({ id, ...rest }) => rest);\n    } else if (questionType === \"true_false\") {\n      correctAnswerPayload = correctAnswerTF;\n    } else if (questionType === \"short_answer\") {\n      if (!correctAnswerShort.trim()) {\n        setError(\"Correct answer for short answer is required.\");\n        setLoading(false);\n        return;\n      }\n      correctAnswerPayload = correctAnswerShort;\n    }\n\n    const questionData: Omit<QuizQuestionInsert, \"quiz_id\" | \"user_id\"> = {\n      question_text: questionText,\n      type: questionType,\n      options: optionsPayload,\n      correct_answer: correctAnswerPayload,\n      explanation: explanation || null,\n    };\n\n    try {\n      if (editingQuestion) {\n        const { error: updateError } = await supabase\n          .from(\"quiz_questions\")\n          .update(questionData)\n          .match({ id: editingQuestion.id, user_id: user?.id });\n        if (updateError) throw updateError;\n      } else {\n        const finalQuestionData: QuizQuestionInsert = {\n          ...questionData,\n          quiz_id: selectedQuizId,\n          user_id: user!.id,\n        };\n        const { error: insertError } = await supabase\n          .from(\"quiz_questions\")\n          .insert(finalQuestionData);\n        if (insertError) throw insertError;\n      }\n      onQuestionSaved();\n    } catch (err: any) {\n      setError(err.message || \"Failed to save question.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-slate-900 border border-slate-600 rounded-xl p-6 shadow-lg\">\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div className=\"flex items-center justify-between border-b border-slate-700 pb-4\">\n          <h5 className=\"text-lg font-bold text-slate-200 flex items-center\">\n            {editingQuestion ? (\n              <>\n                <svg\n                  className=\"w-5 h-5 mr-2 text-yellow-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n                </svg>\n                Edit Question\n              </>\n            ) : (\n              <>\n                <svg\n                  className=\"w-5 h-5 mr-2 text-green-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                Add New Question\n              </>\n            )}\n          </h5>\n          {editingQuestion && (\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              onClick={onCancel}\n              className=\"text-slate-400 hover:text-slate-300\"\n            >\n              Cancel\n            </Button>\n          )}\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/20 border border-red-500/30 text-red-400 text-sm p-3 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"lg:col-span-2\">\n            <Label\n              htmlFor=\"questionText\"\n              className=\"text-slate-300 font-semibold\"\n            >\n              Question Text*\n            </Label>\n            <textarea\n              id=\"questionText\"\n              value={questionText}\n              onChange={(e) => setQuestionText(e.target.value)}\n              required\n              rows={4}\n              className=\"block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors resize-none\"\n              placeholder=\"Enter your question here...\"\n            />\n          </div>\n          <div>\n            <Label\n              htmlFor=\"questionType\"\n              className=\"text-slate-300 font-semibold\"\n            >\n              Question Type*\n            </Label>\n            <select\n              id=\"questionType\"\n              value={questionType}\n              onChange={(e) => setQuestionType(e.target.value as QuestionType)}\n              className=\"block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors\"\n            >\n              <option value=\"multiple_choice\">📝 Multiple Choice</option>\n              <option value=\"select_all_that_apply\">\n                ☑️ Select All That Apply\n              </option>\n              <option value=\"true_false\">✓ True/False</option>\n              <option value=\"short_answer\">💭 Short Answer</option>\n            </select>\n          </div>\n        </div>\n\n        {questionType === \"multiple_choice\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <Label className=\"text-slate-300 font-semibold flex items-center\">\n                <svg\n                  className=\"w-4 h-4 mr-2 text-blue-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                Multiple Choice Options\n              </Label>\n              <span className=\"text-xs text-slate-400 bg-slate-800 px-2 py-1 rounded\">\n                Select one correct answer\n              </span>\n            </div>\n            <div className=\"space-y-3\">\n              {mcqOptions.map((opt, index) => (\n                <div\n                  key={opt.id}\n                  className=\"flex items-center space-x-3 bg-slate-800/50 p-3 rounded-lg border border-slate-600\"\n                >\n                  <span className=\"text-slate-400 font-mono text-sm w-6\">\n                    {String.fromCharCode(65 + index)}.\n                  </span>\n                  <Input\n                    type=\"text\"\n                    placeholder={`Enter option ${index + 1}`}\n                    value={opt.text}\n                    onChange={(e) =>\n                      handleMcqOptionChange(index, \"text\", e.target.value)\n                    }\n                    required\n                    className=\"flex-grow bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500\"\n                  />\n                  <label className=\"flex items-center space-x-2 cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={opt.is_correct}\n                      onChange={(e) =>\n                        handleMcqOptionChange(\n                          index,\n                          \"is_correct\",\n                          e.target.checked\n                        )\n                      }\n                      className=\"h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500\"\n                    />\n                    <span className=\"text-sm text-slate-300\">Correct</span>\n                  </label>\n                  {mcqOptions.length > 1 && (\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeMcqOption(index)}\n                      className=\"text-red-400 hover:text-red-300 p-1\"\n                    >\n                      ×\n                    </Button>\n                  )}\n                </div>\n              ))}\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={addMcqOption}\n                className=\"border-blue-500/30 text-blue-400\"\n              >\n                Add Option\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {questionType === \"select_all_that_apply\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <Label className=\"text-slate-300 font-semibold flex items-center\">\n                <svg\n                  className=\"w-4 h-4 mr-2 text-green-400\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                Select All That Apply Options\n              </Label>\n              <span className=\"text-xs text-slate-400 bg-slate-800 px-2 py-1 rounded\">\n                Multiple correct answers allowed\n              </span>\n            </div>\n            <div className=\"space-y-3\">\n              {selectAllOptions.map((opt, index) => (\n                <div\n                  key={opt.id}\n                  className=\"flex items-center space-x-3 bg-slate-800/50 p-3 rounded-lg border border-slate-600\"\n                >\n                  <span className=\"text-slate-400 font-mono text-sm w-6\">\n                    {index + 1}.\n                  </span>\n                  <Input\n                    type=\"text\"\n                    placeholder={`Enter option ${index + 1}`}\n                    value={opt.text}\n                    onChange={(e) =>\n                      handleSelectAllOptionChange(index, \"text\", e.target.value)\n                    }\n                    required\n                    className=\"flex-grow bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500\"\n                  />\n                  <label className=\"flex items-center space-x-2 cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={opt.is_correct}\n                      onChange={(e) =>\n                        handleSelectAllOptionChange(\n                          index,\n                          \"is_correct\",\n                          e.target.checked\n                        )\n                      }\n                      className=\"h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500\"\n                    />\n                    <span className=\"text-sm text-slate-300\">Correct</span>\n                  </label>\n                  {selectAllOptions.length > 1 && (\n                    <Button\n                      type=\"button\"\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeSelectAllOption(index)}\n                      className=\"text-red-400 hover:text-red-300 p-1\"\n                    >\n                      ×\n                    </Button>\n                  )}\n                </div>\n              ))}\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={addSelectAllOption}\n                className=\"border-blue-500/30 text-blue-400\"\n              >\n                Add Option\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {questionType === \"true_false\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <Label className=\"text-slate-300 font-semibold mb-3 flex items-center\">\n              <svg\n                className=\"w-4 h-4 mr-2 text-yellow-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              Correct Answer\n            </Label>\n            <div className=\"grid grid-cols-2 gap-3\">\n              {[\"true\", \"false\"].map((value) => (\n                <label\n                  key={value}\n                  className={`flex items-center justify-center p-3 border-2 rounded-lg cursor-pointer transition-all ${\n                    correctAnswerTF === value\n                      ? \"border-purple-500 bg-purple-900/20 text-purple-300\"\n                      : \"border-slate-600 bg-slate-800/50 text-slate-300 hover:border-slate-500\"\n                  }`}\n                >\n                  <input\n                    type=\"radio\"\n                    name=\"trueFalse\"\n                    value={value}\n                    checked={correctAnswerTF === value}\n                    onChange={(e) => setCorrectAnswerTF(e.target.value)}\n                    className=\"sr-only\"\n                  />\n                  <span className=\"font-medium capitalize\">{value}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {questionType === \"short_answer\" && (\n          <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n            <Label\n              htmlFor=\"correctShort\"\n              className=\"text-slate-300 font-semibold mb-3 flex items-center\"\n            >\n              <svg\n                className=\"w-4 h-4 mr-2 text-orange-400\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              Correct Answer*\n            </Label>\n            <Input\n              id=\"correctShort\"\n              value={correctAnswerShort}\n              onChange={(e) => setCorrectAnswerShort(e.target.value)}\n              required\n              placeholder=\"Enter the expected answer...\"\n              className=\"bg-slate-800 border-slate-600 text-slate-200 focus:border-purple-500\"\n            />\n          </div>\n        )}\n\n        <div className=\"bg-slate-700/30 rounded-lg p-4 border border-slate-600\">\n          <Label\n            htmlFor=\"explanation\"\n            className=\"text-slate-300 font-semibold mb-3 flex items-center\"\n          >\n            <svg\n              className=\"w-4 h-4 mr-2 text-indigo-400\"\n              fill=\"currentColor\"\n              viewBox=\"0 0 20 20\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n            Explanation <span className=\"text-slate-500\">(Optional)</span>\n          </Label>\n          <textarea\n            id=\"explanation\"\n            value={explanation}\n            onChange={(e) => setExplanation(e.target.value)}\n            rows={3}\n            placeholder=\"Provide an explanation for the answer (helps students learn)...\"\n            className=\"block w-full px-4 py-3 bg-slate-800 border border-slate-600 rounded-lg text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 outline-none transition-colors resize-none\"\n          />\n        </div>\n\n        <div className=\"flex items-center justify-between pt-4 border-t border-slate-700\">\n          <Button\n            type=\"submit\"\n            disabled={loading}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            {loading ? (\n              <>\n                <svg\n                  className=\"animate-spin h-4 w-4 mr-2\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <circle\n                    className=\"opacity-25\"\n                    cx=\"12\"\n                    cy=\"12\"\n                    r=\"10\"\n                    stroke=\"currentColor\"\n                    strokeWidth=\"4\"\n                  ></circle>\n                  <path\n                    className=\"opacity-75\"\n                    fill=\"currentColor\"\n                    d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                  ></path>\n                </svg>\n                {editingQuestion ? \"Saving...\" : \"Adding...\"}\n              </>\n            ) : (\n              <>\n                <svg\n                  className=\"w-4 h-4 mr-2\"\n                  fill=\"currentColor\"\n                  viewBox=\"0 0 20 20\"\n                >\n                  <path\n                    fillRule=\"evenodd\"\n                    d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                    clipRule=\"evenodd\"\n                  />\n                </svg>\n                {editingQuestion ? \"Save Changes\" : \"Add Question\"}\n              </>\n            )}\n          </Button>\n          {editingQuestion && (\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onCancel}\n              className=\"border-slate-600 text-slate-300 hover:text-slate-100\"\n            >\n              Cancel\n            </Button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n"}