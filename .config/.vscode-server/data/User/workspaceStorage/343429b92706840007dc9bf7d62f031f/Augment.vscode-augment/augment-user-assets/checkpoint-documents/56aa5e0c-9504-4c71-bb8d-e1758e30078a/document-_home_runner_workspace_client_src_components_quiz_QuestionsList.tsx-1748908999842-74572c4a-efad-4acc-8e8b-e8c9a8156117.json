{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuestionsList.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport { Tables } from \"../../types/supabase\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Button } from \"@/components/ui/button\";\nimport { QuestionForm } from \"./QuestionForm\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface McqOption {\n  text: string;\n  is_correct: boolean;\n}\n\nexport interface QuestionsListProps {\n  questions: QuizQuestion[];\n  loading: boolean;\n  onRefreshQuestions: () => void;\n  selectedQuizId: string;\n}\n\nexport const QuestionsList: React.FC<QuestionsListProps> = ({\n  questions,\n  loading,\n  onRefreshQuestions,\n  selectedQuizId,\n}) => {\n  const { user } = useAuth();\n  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(null);\n\n  const handleDeleteQuestion = async (questionId: string) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this question? This action cannot be undone.\"\n      )\n    )\n      return;\n\n    try {\n      const { error } = await supabase\n        .from(\"quiz_questions\")\n        .delete()\n        .match({ id: questionId, user_id: user?.id });\n\n      if (error) throw error;\n      onRefreshQuestions();\n    } catch (err: any) {\n      console.error(\"Error deleting question:\", err);\n      alert(`Error deleting question: ${err.message}`);\n    }\n  };\n\n  const handleEditClick = (questionId: string) => {\n    setEditingQuestionId(questionId);\n  };\n\n  const handleSaveOrCancelEdit = () => {\n    setEditingQuestionId(null);\n    onRefreshQuestions();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-lg\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-slate-300\">Loading questions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-lg\">\n      <div className=\"flex items-center justify-between border-b border-slate-700 pb-4 mb-6\">\n        <h5 className=\"text-lg font-bold text-slate-200 flex items-center\">\n          <svg\n            className=\"w-5 h-5 mr-2 text-purple-400\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n          Quiz Questions\n        </h5>\n        <span className=\"text-slate-400 text-sm bg-slate-900 px-3 py-1 rounded-lg\">\n          {questions.length} question{questions.length !== 1 ? \"s\" : \"\"}\n        </span>\n      </div>\n\n      {questions.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <svg\n            className=\"w-12 h-12 text-slate-500 mx-auto mb-4\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth=\"2\"\n              d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            />\n          </svg>\n          <p className=\"text-slate-400 text-lg mb-2\">No questions yet</p>\n          <p className=\"text-slate-500 text-sm\">\n            Add your first question or generate questions from a document\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {questions.map((q, index) => (\n            <div\n              key={q.id}\n              className=\"bg-slate-900 border border-slate-600 rounded-lg p-4 hover:border-slate-500 transition-colors\"\n            >\n              {editingQuestionId === q.id ? (\n                <QuestionForm\n                  selectedQuizId={selectedQuizId}\n                  editingQuestion={q}\n                  onQuestionSaved={handleSaveOrCancelEdit}\n                  onCancel={handleSaveOrCancelEdit}\n                />\n              ) : (\n                <>\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <span className=\"text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded\">\n                          Q{index + 1}\n                        </span>\n                        <span\n                          className={`text-xs px-2 py-1 rounded-full ${\n                            q.type === \"multiple_choice\"\n                              ? \"bg-blue-900/30 text-blue-400\"\n                              : q.type === \"select_all_that_apply\"\n                              ? \"bg-green-900/30 text-green-400\"\n                              : q.type === \"true_false\"\n                              ? \"bg-yellow-900/30 text-yellow-400\"\n                              : q.type === \"short_answer\"\n                              ? \"bg-orange-900/30 text-orange-400\"\n                              : \"bg-slate-900/30 text-slate-400\"\n                          }`}\n                        >\n                          {q.type\n                            .replace(/_/g, \" \")\n                            .replace(/\\b\\w/g, (l) => l.toUpperCase())}\n                        </span>\n                      </div>\n                      <p className=\"font-medium text-slate-200 mb-3\">\n                        {q.question_text}\n                      </p>\n\n                      {/* Display options/answer based on type */}\n                      {(q.type === \"multiple_choice\" ||\n                        q.type === \"select_all_that_apply\") &&\n                        q.options &&\n                        Array.isArray(q.options) && (\n                          <div className=\"bg-slate-800/50 rounded-lg p-3 mb-3\">\n                            <p className=\"text-xs text-slate-400 mb-2 font-medium\">\n                              Options:\n                            </p>\n                            <div className=\"space-y-1\">\n                              {(q.options as unknown as McqOption[]).map((opt, i) => (\n                                <div\n                                  key={i}\n                                  className=\"flex items-center space-x-2 text-sm\"\n                                >\n                                  <span className=\"text-slate-500 font-mono w-6\">\n                                    {q.type === \"multiple_choice\"\n                                      ? String.fromCharCode(65 + i)\n                                      : i + 1}\n                                    .\n                                  </span>\n                                  <span\n                                    className={\n                                      opt.is_correct\n                                        ? \"text-green-400 font-medium\"\n                                        : \"text-slate-300\"\n                                    }\n                                  >\n                                    {opt.text}\n                                  </span>\n                                  {opt.is_correct && (\n                                    <svg\n                                      className=\"w-4 h-4 text-green-400\"\n                                      fill=\"currentColor\"\n                                      viewBox=\"0 0 20 20\"\n                                    >\n                                      <path\n                                        fillRule=\"evenodd\"\n                                        d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                                        clipRule=\"evenodd\"\n                                      />\n                                    </svg>\n                                  )}\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n\n                      {(q.type === \"true_false\" || q.type === \"short_answer\") &&\n                        q.correct_answer && (\n                          <div className=\"bg-slate-800/50 rounded-lg p-3 mb-3\">\n                            <p className=\"text-xs text-slate-400 mb-1 font-medium\">\n                              Correct Answer:\n                            </p>\n                            <span className=\"text-green-400 font-medium text-sm\">\n                              {q.correct_answer}\n                            </span>\n                          </div>\n                        )}\n\n                      {q.explanation && (\n                        <div className=\"bg-indigo-900/20 border border-indigo-500/30 rounded-lg p-3 mb-3\">\n                          <p className=\"text-xs text-indigo-400 mb-1 font-medium\">\n                            Explanation:\n                          </p>\n                          <p className=\"text-slate-300 text-sm\">{q.explanation}</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-end space-x-2 pt-3 border-t border-slate-700\">\n                    <Button\n                      onClick={() => handleEditClick(q.id)}\n                      size=\"sm\"\n                      className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n                    >\n                      <svg\n                        className=\"w-4 h-4 mr-1\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n                      </svg>\n                      Edit\n                    </Button>\n                    <Button\n                      onClick={() => handleDeleteQuestion(q.id)}\n                      variant=\"destructive\"\n                      size=\"sm\"\n                    >\n                      <svg\n                        className=\"w-4 h-4 mr-1\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                          clipRule=\"evenodd\"\n                        />\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V7a1 1 0 00-1-1z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                      Delete\n                    </Button>\n                  </div>\n                </>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useState } from \"react\";\nimport { Tables } from \"../../types/supabase\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Button } from \"@/components/ui/button\";\nimport { QuestionForm } from \"./QuestionForm\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface McqOption {\n  text: string;\n  is_correct: boolean;\n}\n\nexport interface QuestionsListProps {\n  questions: QuizQuestion[];\n  loading: boolean;\n  onRefreshQuestions: () => void;\n  selectedQuizId: string;\n}\n\nexport const QuestionsList: React.FC<QuestionsListProps> = ({\n  questions,\n  loading,\n  onRefreshQuestions,\n  selectedQuizId,\n}) => {\n  const { user } = useAuth();\n  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(null);\n\n  const handleDeleteQuestion = async (questionId: string) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this question? This action cannot be undone.\"\n      )\n    )\n      return;\n\n    try {\n      // Delete question via backend API\n      const token = localStorage.getItem('auth_token');\n      const response = await fetch(`/api/quiz-questions/${questionId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to delete question');\n      }\n\n      onRefreshQuestions();\n    } catch (err: any) {\n      console.error(\"Error deleting question:\", err);\n      alert(`Error deleting question: ${err.message}`);\n    }\n  };\n\n  const handleEditClick = (questionId: string) => {\n    setEditingQuestionId(questionId);\n  };\n\n  const handleSaveOrCancelEdit = () => {\n    setEditingQuestionId(null);\n    onRefreshQuestions();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-lg\">\n        <div className=\"text-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-slate-300\">Loading questions...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-slate-800 border border-slate-700 rounded-xl p-6 shadow-lg\">\n      <div className=\"flex items-center justify-between border-b border-slate-700 pb-4 mb-6\">\n        <h5 className=\"text-lg font-bold text-slate-200 flex items-center\">\n          <svg\n            className=\"w-5 h-5 mr-2 text-purple-400\"\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              d=\"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n          Quiz Questions\n        </h5>\n        <span className=\"text-slate-400 text-sm bg-slate-900 px-3 py-1 rounded-lg\">\n          {questions.length} question{questions.length !== 1 ? \"s\" : \"\"}\n        </span>\n      </div>\n\n      {questions.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <svg\n            className=\"w-12 h-12 text-slate-500 mx-auto mb-4\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth=\"2\"\n              d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            />\n          </svg>\n          <p className=\"text-slate-400 text-lg mb-2\">No questions yet</p>\n          <p className=\"text-slate-500 text-sm\">\n            Add your first question or generate questions from a document\n          </p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {questions.map((q, index) => (\n            <div\n              key={q.id}\n              className=\"bg-slate-900 border border-slate-600 rounded-lg p-4 hover:border-slate-500 transition-colors\"\n            >\n              {editingQuestionId === q.id ? (\n                <QuestionForm\n                  selectedQuizId={selectedQuizId}\n                  editingQuestion={q}\n                  onQuestionSaved={handleSaveOrCancelEdit}\n                  onCancel={handleSaveOrCancelEdit}\n                />\n              ) : (\n                <>\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <span className=\"text-slate-400 font-mono text-sm bg-slate-800 px-2 py-1 rounded\">\n                          Q{index + 1}\n                        </span>\n                        <span\n                          className={`text-xs px-2 py-1 rounded-full ${\n                            q.type === \"multiple_choice\"\n                              ? \"bg-blue-900/30 text-blue-400\"\n                              : q.type === \"select_all_that_apply\"\n                              ? \"bg-green-900/30 text-green-400\"\n                              : q.type === \"true_false\"\n                              ? \"bg-yellow-900/30 text-yellow-400\"\n                              : q.type === \"short_answer\"\n                              ? \"bg-orange-900/30 text-orange-400\"\n                              : \"bg-slate-900/30 text-slate-400\"\n                          }`}\n                        >\n                          {q.type\n                            .replace(/_/g, \" \")\n                            .replace(/\\b\\w/g, (l) => l.toUpperCase())}\n                        </span>\n                      </div>\n                      <p className=\"font-medium text-slate-200 mb-3\">\n                        {q.question_text}\n                      </p>\n\n                      {/* Display options/answer based on type */}\n                      {(q.type === \"multiple_choice\" ||\n                        q.type === \"select_all_that_apply\") &&\n                        q.options &&\n                        Array.isArray(q.options) && (\n                          <div className=\"bg-slate-800/50 rounded-lg p-3 mb-3\">\n                            <p className=\"text-xs text-slate-400 mb-2 font-medium\">\n                              Options:\n                            </p>\n                            <div className=\"space-y-1\">\n                              {(q.options as unknown as McqOption[]).map((opt, i) => (\n                                <div\n                                  key={i}\n                                  className=\"flex items-center space-x-2 text-sm\"\n                                >\n                                  <span className=\"text-slate-500 font-mono w-6\">\n                                    {q.type === \"multiple_choice\"\n                                      ? String.fromCharCode(65 + i)\n                                      : i + 1}\n                                    .\n                                  </span>\n                                  <span\n                                    className={\n                                      opt.is_correct\n                                        ? \"text-green-400 font-medium\"\n                                        : \"text-slate-300\"\n                                    }\n                                  >\n                                    {opt.text}\n                                  </span>\n                                  {opt.is_correct && (\n                                    <svg\n                                      className=\"w-4 h-4 text-green-400\"\n                                      fill=\"currentColor\"\n                                      viewBox=\"0 0 20 20\"\n                                    >\n                                      <path\n                                        fillRule=\"evenodd\"\n                                        d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                                        clipRule=\"evenodd\"\n                                      />\n                                    </svg>\n                                  )}\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n\n                      {(q.type === \"true_false\" || q.type === \"short_answer\") &&\n                        q.correct_answer && (\n                          <div className=\"bg-slate-800/50 rounded-lg p-3 mb-3\">\n                            <p className=\"text-xs text-slate-400 mb-1 font-medium\">\n                              Correct Answer:\n                            </p>\n                            <span className=\"text-green-400 font-medium text-sm\">\n                              {q.correct_answer}\n                            </span>\n                          </div>\n                        )}\n\n                      {q.explanation && (\n                        <div className=\"bg-indigo-900/20 border border-indigo-500/30 rounded-lg p-3 mb-3\">\n                          <p className=\"text-xs text-indigo-400 mb-1 font-medium\">\n                            Explanation:\n                          </p>\n                          <p className=\"text-slate-300 text-sm\">{q.explanation}</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-end space-x-2 pt-3 border-t border-slate-700\">\n                    <Button\n                      onClick={() => handleEditClick(q.id)}\n                      size=\"sm\"\n                      className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n                    >\n                      <svg\n                        className=\"w-4 h-4 mr-1\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n                      </svg>\n                      Edit\n                    </Button>\n                    <Button\n                      onClick={() => handleDeleteQuestion(q.id)}\n                      variant=\"destructive\"\n                      size=\"sm\"\n                    >\n                      <svg\n                        className=\"w-4 h-4 mr-1\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\"\n                          clipRule=\"evenodd\"\n                        />\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V7a1 1 0 00-1-1z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                      Delete\n                    </Button>\n                  </div>\n                </>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n"}