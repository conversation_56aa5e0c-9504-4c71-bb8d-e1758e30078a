{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/SRSDashboard.tsx"}, "originalCode": "import React, { useEffect, useState } from \"react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Brain,\n  TrendingUp,\n  Clock,\n  Target,\n  Calendar,\n  PlayCircle,\n  BarChart3,\n} from \"lucide-react\";\nimport { getQuizQuestionsDueForReview, getQuizQuestionsDueCount } from \"@/lib/srs\";\nimport { SRSQuizMode } from \"../quiz/SRSQuizMode\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface SRSStats {\n  totalQuestions: number;\n  dueForReview: number;\n  reviewedToday: number;\n  averageSRSLevel: number;\n  nextReviewDate: string | null;\n  streakDays: number;\n}\n\nexport const SRSDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [stats, setStats] = useState<SRSStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [showSRSMode, setShowSRSMode] = useState(false);\n\n  useEffect(() => {\n    const fetchSRSData = async () => {\n      if (!user) return;\n      \n      setLoading(true);\n      try {\n        const { data: questionsData, error } = await supabase\n          .from(\"quiz_questions\")\n          .select(\"*\")\n          .eq(\"user_id\", user.id)\n          .order(\"created_at\", { ascending: true });\n\n        if (error) throw error;\n\n        const allQuestions = questionsData || [];\n        setQuestions(allQuestions);\n\n        // Calculate SRS statistics\n        const dueQuestions = getQuizQuestionsDueForReview(allQuestions);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n\n        const reviewedToday = allQuestions.filter(q => {\n          if (!q.last_reviewed_at) return false;\n          const reviewDate = new Date(q.last_reviewed_at);\n          reviewDate.setHours(0, 0, 0, 0);\n          return reviewDate.getTime() === today.getTime();\n        }).length;\n\n        const averageSRSLevel = allQuestions.length > 0\n          ? allQuestions.reduce((sum, q) => sum + (q.srs_level || 0), 0) / allQuestions.length\n          : 0;\n\n        // Find next review date\n        const futureReviews = allQuestions\n          .filter(q => q.due_at && new Date(q.due_at) > new Date())\n          .sort((a, b) => new Date(a.due_at!).getTime() - new Date(b.due_at!).getTime());\n\n        const nextReviewDate = futureReviews.length > 0 ? futureReviews[0].due_at : null;\n\n        // Calculate streak (simplified - days with reviews in the last week)\n        const weekAgo = new Date();\n        weekAgo.setDate(weekAgo.getDate() - 7);\n        const recentReviews = allQuestions.filter(q => \n          q.last_reviewed_at && new Date(q.last_reviewed_at) >= weekAgo\n        );\n        const uniqueReviewDays = new Set(\n          recentReviews.map(q => new Date(q.last_reviewed_at!).toDateString())\n        );\n\n        setStats({\n          totalQuestions: allQuestions.length,\n          dueForReview: dueQuestions.length,\n          reviewedToday,\n          averageSRSLevel: Math.round(averageSRSLevel * 10) / 10,\n          nextReviewDate,\n          streakDays: uniqueReviewDays.size,\n        });\n\n      } catch (error) {\n        console.error(\"Error fetching SRS data:\", error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchSRSData();\n  }, [user]);\n\n  const formatNextReview = (dateString: string | null): string => {\n    if (!dateString) return \"No upcoming reviews\";\n    \n    const date = new Date(dateString);\n    const now = new Date();\n    const diffMs = date.getTime() - now.getTime();\n    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffHours < 1) return \"Soon\";\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays === 1) return \"Tomorrow\";\n    if (diffDays < 7) return `${diffDays} days`;\n    return date.toLocaleDateString();\n  };\n\n  if (showSRSMode) {\n    return <SRSQuizMode onExit={() => setShowSRSMode(false)} />;\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-xl text-slate-200\">\n            <Brain className=\"h-6 w-6 text-purple-400\" />\n            Spaced Repetition System\n          </CardTitle>\n          <p className=\"text-slate-400 text-sm\">\n            Optimize your learning with scientifically-proven spaced repetition\n          </p>\n        </CardHeader>\n        <CardContent>\n          {stats && (\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <Target className=\"h-5 w-5 text-purple-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Total Questions</p>\n                <p className=\"text-2xl font-bold text-purple-400\">\n                  {stats.totalQuestions}\n                </p>\n              </div>\n\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <Clock className=\"h-5 w-5 text-orange-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Due for Review</p>\n                <p className=\"text-2xl font-bold text-orange-400\">\n                  {stats.dueForReview}\n                </p>\n              </div>\n\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <TrendingUp className=\"h-5 w-5 text-green-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Avg. SRS Level</p>\n                <p className=\"text-2xl font-bold text-green-400\">\n                  {stats.averageSRSLevel}\n                </p>\n              </div>\n\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <BarChart3 className=\"h-5 w-5 text-blue-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Study Streak</p>\n                <p className=\"text-2xl font-bold text-blue-400\">\n                  {stats.streakDays} days\n                </p>\n              </div>\n            </div>\n          )}\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              onClick={() => setShowSRSMode(true)}\n              disabled={!stats || stats.dueForReview === 0}\n              className=\"bg-purple-600 hover:bg-purple-700 flex-1 sm:flex-none\"\n            >\n              <PlayCircle className=\"h-4 w-4 mr-2\" />\n              Start SRS Review\n              {stats && stats.dueForReview > 0 && (\n                <Badge variant=\"secondary\" className=\"ml-2 bg-orange-900/30 text-orange-400\">\n                  {stats.dueForReview}\n                </Badge>\n              )}\n            </Button>\n\n            {stats && stats.nextReviewDate && (\n              <div className=\"flex items-center justify-center text-sm text-slate-400\">\n                <Calendar className=\"h-4 w-4 mr-2\" />\n                Next review: {formatNextReview(stats.nextReviewDate)}\n              </div>\n            )}\n          </div>\n\n          {stats && stats.dueForReview === 0 && (\n            <div className=\"text-center mt-4\">\n              <p className=\"text-slate-400 text-sm\">\n                🎉 All caught up! No questions are due for review right now.\n              </p>\n              {stats.nextReviewDate && (\n                <p className=\"text-slate-500 text-xs mt-1\">\n                  Next review in {formatNextReview(stats.nextReviewDate)}\n                </p>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {stats && stats.totalQuestions === 0 && (\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-6 text-center\">\n            <Brain className=\"h-16 w-16 mx-auto text-slate-600 mb-4\" />\n            <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n              No Questions Yet\n            </h3>\n            <p className=\"text-slate-400 mb-4\">\n              Create some quizzes to start using the spaced repetition system!\n            </p>\n            <p className=\"text-slate-500 text-sm\">\n              The SRS will help you review questions at optimal intervals to maximize long-term retention.\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}; ", "modifiedCode": "import React, { useEffect, useState } from \"react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Brain,\n  TrendingUp,\n  Clock,\n  Target,\n  Calendar,\n  PlayCircle,\n  BarChart3,\n} from \"lucide-react\";\nimport { getQuizQuestionsDueForReview, getQuizQuestionsDueCount } from \"@/lib/srs\";\nimport { SRSQuizMode } from \"../quiz/SRSQuizMode\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface SRSStats {\n  totalQuestions: number;\n  dueForReview: number;\n  reviewedToday: number;\n  averageSRSLevel: number;\n  nextReviewDate: string | null;\n  streakDays: number;\n}\n\nexport const SRSDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [stats, setStats] = useState<SRSStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [showSRSMode, setShowSRSMode] = useState(false);\n\n  useEffect(() => {\n    const fetchSRSData = async () => {\n      if (!user) return;\n      \n      setLoading(true);\n      try {\n        const { data: questionsData, error } = await supabase\n          .from(\"quiz_questions\")\n          .select(\"*\")\n          .eq(\"user_id\", user.id)\n          .order(\"created_at\", { ascending: true });\n\n        if (error) throw error;\n\n        const allQuestions = questionsData || [];\n        setQuestions(allQuestions);\n\n        // Calculate SRS statistics\n        const dueQuestions = getQuizQuestionsDueForReview(allQuestions);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n\n        const reviewedToday = allQuestions.filter(q => {\n          if (!q.last_reviewed_at) return false;\n          const reviewDate = new Date(q.last_reviewed_at);\n          reviewDate.setHours(0, 0, 0, 0);\n          return reviewDate.getTime() === today.getTime();\n        }).length;\n\n        const averageSRSLevel = allQuestions.length > 0\n          ? allQuestions.reduce((sum, q) => sum + (q.srs_level || 0), 0) / allQuestions.length\n          : 0;\n\n        // Find next review date\n        const futureReviews = allQuestions\n          .filter(q => q.due_at && new Date(q.due_at) > new Date())\n          .sort((a, b) => new Date(a.due_at!).getTime() - new Date(b.due_at!).getTime());\n\n        const nextReviewDate = futureReviews.length > 0 ? futureReviews[0].due_at : null;\n\n        // Calculate streak (simplified - days with reviews in the last week)\n        const weekAgo = new Date();\n        weekAgo.setDate(weekAgo.getDate() - 7);\n        const recentReviews = allQuestions.filter(q => \n          q.last_reviewed_at && new Date(q.last_reviewed_at) >= weekAgo\n        );\n        const uniqueReviewDays = new Set(\n          recentReviews.map(q => new Date(q.last_reviewed_at!).toDateString())\n        );\n\n        setStats({\n          totalQuestions: allQuestions.length,\n          dueForReview: dueQuestions.length,\n          reviewedToday,\n          averageSRSLevel: Math.round(averageSRSLevel * 10) / 10,\n          nextReviewDate,\n          streakDays: uniqueReviewDays.size,\n        });\n\n      } catch (error) {\n        console.error(\"Error fetching SRS data:\", error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchSRSData();\n  }, [user]);\n\n  const formatNextReview = (dateString: string | null): string => {\n    if (!dateString) return \"No upcoming reviews\";\n    \n    const date = new Date(dateString);\n    const now = new Date();\n    const diffMs = date.getTime() - now.getTime();\n    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));\n    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));\n\n    if (diffHours < 1) return \"Soon\";\n    if (diffHours < 24) return `${diffHours}h`;\n    if (diffDays === 1) return \"Tomorrow\";\n    if (diffDays < 7) return `${diffDays} days`;\n    return date.toLocaleDateString();\n  };\n\n  if (showSRSMode) {\n    return <SRSQuizMode onExit={() => setShowSRSMode(false)} />;\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-xl text-slate-200\">\n            <Brain className=\"h-6 w-6 text-purple-400\" />\n            Spaced Repetition System\n          </CardTitle>\n          <p className=\"text-slate-400 text-sm\">\n            Optimize your learning with scientifically-proven spaced repetition\n          </p>\n        </CardHeader>\n        <CardContent>\n          {stats && (\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <Target className=\"h-5 w-5 text-purple-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Total Questions</p>\n                <p className=\"text-2xl font-bold text-purple-400\">\n                  {stats.totalQuestions}\n                </p>\n              </div>\n\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <Clock className=\"h-5 w-5 text-orange-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Due for Review</p>\n                <p className=\"text-2xl font-bold text-orange-400\">\n                  {stats.dueForReview}\n                </p>\n              </div>\n\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <TrendingUp className=\"h-5 w-5 text-green-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Avg. SRS Level</p>\n                <p className=\"text-2xl font-bold text-green-400\">\n                  {stats.averageSRSLevel}\n                </p>\n              </div>\n\n              <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n                <div className=\"flex items-center justify-center mb-2\">\n                  <BarChart3 className=\"h-5 w-5 text-blue-400\" />\n                </div>\n                <p className=\"text-slate-400 text-sm\">Study Streak</p>\n                <p className=\"text-2xl font-bold text-blue-400\">\n                  {stats.streakDays} days\n                </p>\n              </div>\n            </div>\n          )}\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button\n              onClick={() => setShowSRSMode(true)}\n              disabled={!stats || stats.dueForReview === 0}\n              className=\"bg-purple-600 hover:bg-purple-700 flex-1 sm:flex-none\"\n            >\n              <PlayCircle className=\"h-4 w-4 mr-2\" />\n              Start SRS Review\n              {stats && stats.dueForReview > 0 && (\n                <Badge variant=\"secondary\" className=\"ml-2 bg-orange-900/30 text-orange-400\">\n                  {stats.dueForReview}\n                </Badge>\n              )}\n            </Button>\n\n            {stats && stats.nextReviewDate && (\n              <div className=\"flex items-center justify-center text-sm text-slate-400\">\n                <Calendar className=\"h-4 w-4 mr-2\" />\n                Next review: {formatNextReview(stats.nextReviewDate)}\n              </div>\n            )}\n          </div>\n\n          {stats && stats.dueForReview === 0 && (\n            <div className=\"text-center mt-4\">\n              <p className=\"text-slate-400 text-sm\">\n                🎉 All caught up! No questions are due for review right now.\n              </p>\n              {stats.nextReviewDate && (\n                <p className=\"text-slate-500 text-xs mt-1\">\n                  Next review in {formatNextReview(stats.nextReviewDate)}\n                </p>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {stats && stats.totalQuestions === 0 && (\n        <Card className=\"bg-slate-800 border-slate-700\">\n          <CardContent className=\"p-6 text-center\">\n            <Brain className=\"h-16 w-16 mx-auto text-slate-600 mb-4\" />\n            <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n              No Questions Yet\n            </h3>\n            <p className=\"text-slate-400 mb-4\">\n              Create some quizzes to start using the spaced repetition system!\n            </p>\n            <p className=\"text-slate-500 text-sm\">\n              The SRS will help you review questions at optimal intervals to maximize long-term retention.\n            </p>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}; "}