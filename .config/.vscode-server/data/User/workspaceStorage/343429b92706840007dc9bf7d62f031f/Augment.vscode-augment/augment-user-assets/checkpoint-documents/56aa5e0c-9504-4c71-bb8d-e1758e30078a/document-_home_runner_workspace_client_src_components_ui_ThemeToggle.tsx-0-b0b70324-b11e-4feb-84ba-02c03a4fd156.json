{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/ThemeToggle.tsx"}, "originalCode": "import React from 'react';\nimport { Moon, Sun } from 'lucide-react';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { Button } from '@/components/ui/button';\n\ninterface ThemeToggleProps {\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'default' | 'ghost' | 'outline';\n}\n\nexport const ThemeToggle: React.FC<ThemeToggleProps> = ({ \n  size = 'md', \n  variant = 'ghost' \n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;\n\n  return (\n    <Button\n      variant={variant}\n      size={size}\n      onClick={toggleTheme}\n      className=\"relative transition-all duration-200 hover:scale-105\"\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      <div className=\"relative\">\n        <Sun \n          size={iconSize}\n          className={`absolute transition-all duration-300 ${\n            theme === 'dark' \n              ? 'rotate-90 scale-0 opacity-0' \n              : 'rotate-0 scale-100 opacity-100'\n          }`}\n        />\n        <Moon \n          size={iconSize}\n          className={`transition-all duration-300 ${\n            theme === 'dark' \n              ? 'rotate-0 scale-100 opacity-100' \n              : '-rotate-90 scale-0 opacity-0'\n          }`}\n        />\n      </div>\n    </Button>\n  );\n}; ", "modifiedCode": "import React from 'react';\nimport { Moon, Sun } from 'lucide-react';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { Button } from '@/components/ui/button';\n\ninterface ThemeToggleProps {\n  size?: 'sm' | 'md' | 'lg';\n  variant?: 'default' | 'ghost' | 'outline';\n}\n\nexport const ThemeToggle: React.FC<ThemeToggleProps> = ({ \n  size = 'md', \n  variant = 'ghost' \n}) => {\n  const { theme, toggleTheme } = useTheme();\n\n  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;\n\n  return (\n    <Button\n      variant={variant}\n      size={size}\n      onClick={toggleTheme}\n      className=\"relative transition-all duration-200 hover:scale-105\"\n      aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}\n    >\n      <div className=\"relative\">\n        <Sun \n          size={iconSize}\n          className={`absolute transition-all duration-300 ${\n            theme === 'dark' \n              ? 'rotate-90 scale-0 opacity-0' \n              : 'rotate-0 scale-100 opacity-100'\n          }`}\n        />\n        <Moon \n          size={iconSize}\n          className={`transition-all duration-300 ${\n            theme === 'dark' \n              ? 'rotate-0 scale-100 opacity-100' \n              : '-rotate-90 scale-0 opacity-0'\n          }`}\n        />\n      </div>\n    </Button>\n  );\n}; "}