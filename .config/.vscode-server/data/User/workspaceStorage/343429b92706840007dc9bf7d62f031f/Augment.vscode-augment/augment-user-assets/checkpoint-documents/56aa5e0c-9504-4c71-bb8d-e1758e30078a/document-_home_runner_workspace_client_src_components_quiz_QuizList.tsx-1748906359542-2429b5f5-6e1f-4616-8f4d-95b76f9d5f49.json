{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizList.tsx"}, "originalCode": "import React from \"react\";\nimport { useQuery, UseQueryOptions } from \"@tanstack/react-query\";\nimport {\n  getAllQuizzesAPI,\n  deleteQuizAPI,\n} from \"@/lib/api\";\nimport { Quiz } from \"@shared/schema\";\nimport { GetAllQuizzesApiResponse } from \"@shared/types/quiz\";\nimport { Button } from \"@/components/ui/button\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { queryClient } from \"@/lib/queryClient\";\n\ninterface QuizListProps {\n  onSelectQuiz: (quizId: string, quizName: string) => void;\n  onPlayQuiz: (quizId: string) => void;\n}\n\nexport const QuizList: React.FC<QuizListProps> = ({\n  onSelectQuiz,\n  onPlayQuiz,\n}) => {\n  const { toast } = useToast();\n\n  // Define query options with proper TypeScript typing\n  const queryOptions: UseQueryOptions<\n    GetAllQuizzesApiResponse,\n    <PERSON><PERSON>r,\n    GetAllQuizzesApiResponse,\n    string[]\n  > = {\n    queryKey: [\"quizzes\"],\n    queryFn: getAllQuizzesAPI,\n    retry: false,\n  };\n\n  const {\n    data: quizzesResponse,\n    isLoading,\n    error,\n    isError,\n  } = useQuery(queryOptions);\n\n  const handleDeleteQuiz = async (quizId: string) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this quiz? All questions in this quiz will also be deleted.\"\n      )\n    )\n      return;\n\n    try {\n      await deleteQuizAPI(quizId);\n      toast({\n        title: \"Quiz Deleted\",\n        description: \"The quiz has been successfully deleted.\",\n      });\n      queryClient.invalidateQueries({ queryKey: [\"quizzes\"] });\n    } catch (err: any) {\n      toast({\n        title: \"Error Deleting Quiz\",\n        description: err.message || \"An unexpected error occurred.\",\n        variant: \"destructive\",\n      });\n      console.error(\"Error deleting quiz:\", err);\n    }\n  };\n\n  if (isLoading)\n    return (\n      <div className=\"flex justify-center items-center py-6\">\n        <Spinner size=\"md\" />\n      </div>\n    );\n\n  if (isError) {\n    // Check if the error is related to Supabase client\n    const errorMessage = error?.message || \"\";\n    const isSupabaseError =\n      errorMessage.includes(\"Supabase client not available\") ||\n      errorMessage.includes(\"Internal Server Configuration Error\");\n\n    return (\n      <div className=\"py-6 text-center\">\n        {isSupabaseError ? (\n          <>\n            <h3 className=\"text-lg font-medium text-purple-400 mb-4\">\n              Database Connection Issue\n            </h3>\n            <p className=\"text-purple-300 mb-4\">\n              The quiz feature is currently unavailable. You can still use other\n              features of the application.\n            </p>\n            <div className=\"flex flex-col items-center space-y-3\">\n              <p className=\"text-purple-300 text-sm\">\n                This could be due to a temporary database connection issue or\n                configuration setup.\n              </p>\n              <Button\n                variant=\"outline\"\n                onClick={() =>\n                  queryClient.invalidateQueries({ queryKey: [\"quizzes\"] })\n                }\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Try Again\n              </Button>\n            </div>\n          </>\n        ) : (          <p className=\"text-destructive text-sm mb-4\">\n            Error fetching quizzes: {error?.message || \"Unknown error\"}\n          </p>\n        )}\n      </div>\n    );\n  }\n\n  const quizzes = quizzesResponse?.quizzes || [];\n  return (\n    <div>\n      {quizzes.length === 0 ? (\n        <p className=\"text-purple-300\">No quizzes yet. Create one!</p>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {quizzes.map((quiz: Quiz) => (\n            <div\n              key={quiz.id}\n              className=\"bg-slate-800 border border-slate-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-150 ease-in-out\"\n            >\n              <div className=\"mb-3\">\n                <h3 className=\"text-purple-400 font-semibold mb-2 truncate\" title={quiz.name}>\n                  {quiz.name}\n                </h3>\n                {quiz.description && (\n                  <p className=\"text-sm text-purple-300 mb-2 line-clamp-2\">\n                    {quiz.description}\n                  </p>\n                )}\n                <p className=\"text-xs text-purple-300 mb-4\">\n                  Created:{\" \"}\n                  {new Date(quiz.created_at || Date.now()).toLocaleDateString()}\n                </p>\n              </div>\n              <div className=\"flex flex-col gap-2\">\n                <Button\n                  size=\"sm\"\n                  variant=\"outline\"\n                  onClick={() => onPlayQuiz(String(quiz.id))}\n                  className=\"w-full\"\n                >\n                  <span className=\"material-icons md-18 mr-1\">play_arrow</span>\n                  Play\n                </Button>\n                <div className=\"flex gap-2\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => onSelectQuiz(String(quiz.id), quiz.name)}\n                    className=\"flex-1\"\n                  >\n                    <span className=\"material-icons md-18 mr-1\">edit</span>\n                    Manage\n                  </Button>\n                  <Button\n                    size=\"sm\"\n                    variant=\"destructive\"\n                    onClick={() => handleDeleteQuiz(String(quiz.id))}\n                    className=\"flex-1\"\n                  >\n                    <span className=\"material-icons md-18 mr-1\">delete</span>\n                    Delete\n                  </Button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n", "modifiedCode": "import React from \"react\";\nimport { useQuery, UseQueryOptions } from \"@tanstack/react-query\";\nimport {\n  getAllQuizzesAPI,\n  deleteQuizAPI,\n} from \"@/lib/api\";\nimport { Quiz, GetAllQuizzesApiResponse } from \"@shared/types/quiz\";\nimport { Button } from \"@/components/ui/button\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { queryClient } from \"@/lib/queryClient\";\n\ninterface QuizListProps {\n  onSelectQuiz: (quizId: string, quizName: string) => void;\n  onPlayQuiz: (quizId: string) => void;\n}\n\nexport const QuizList: React.FC<QuizListProps> = ({\n  onSelectQuiz,\n  onPlayQuiz,\n}) => {\n  const { toast } = useToast();\n\n  // Define query options with proper TypeScript typing\n  const queryOptions: UseQueryOptions<\n    GetAllQuizzesApiResponse,\n    Error,\n    GetAllQuizzesApiResponse,\n    string[]\n  > = {\n    queryKey: [\"quizzes\"],\n    queryFn: getAllQuizzesAPI,\n    retry: false,\n  };\n\n  const {\n    data: quizzesResponse,\n    isLoading,\n    error,\n    isError,\n  } = useQuery(queryOptions);\n\n  const handleDeleteQuiz = async (quizId: string) => {\n    if (\n      !window.confirm(\n        \"Are you sure you want to delete this quiz? All questions in this quiz will also be deleted.\"\n      )\n    )\n      return;\n\n    try {\n      await deleteQuizAPI(quizId);\n      toast({\n        title: \"Quiz Deleted\",\n        description: \"The quiz has been successfully deleted.\",\n      });\n      queryClient.invalidateQueries({ queryKey: [\"quizzes\"] });\n    } catch (err: any) {\n      toast({\n        title: \"Error Deleting Quiz\",\n        description: err.message || \"An unexpected error occurred.\",\n        variant: \"destructive\",\n      });\n      console.error(\"Error deleting quiz:\", err);\n    }\n  };\n\n  if (isLoading)\n    return (\n      <div className=\"flex justify-center items-center py-6\">\n        <Spinner size=\"md\" />\n      </div>\n    );\n\n  if (isError) {\n    // Check if the error is related to Supabase client\n    const errorMessage = error?.message || \"\";\n    const isSupabaseError =\n      errorMessage.includes(\"Supabase client not available\") ||\n      errorMessage.includes(\"Internal Server Configuration Error\");\n\n    return (\n      <div className=\"py-6 text-center\">\n        {isSupabaseError ? (\n          <>\n            <h3 className=\"text-lg font-medium text-purple-400 mb-4\">\n              Database Connection Issue\n            </h3>\n            <p className=\"text-purple-300 mb-4\">\n              The quiz feature is currently unavailable. You can still use other\n              features of the application.\n            </p>\n            <div className=\"flex flex-col items-center space-y-3\">\n              <p className=\"text-purple-300 text-sm\">\n                This could be due to a temporary database connection issue or\n                configuration setup.\n              </p>\n              <Button\n                variant=\"outline\"\n                onClick={() =>\n                  queryClient.invalidateQueries({ queryKey: [\"quizzes\"] })\n                }\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Try Again\n              </Button>\n            </div>\n          </>\n        ) : (          <p className=\"text-destructive text-sm mb-4\">\n            Error fetching quizzes: {error?.message || \"Unknown error\"}\n          </p>\n        )}\n      </div>\n    );\n  }\n\n  const quizzes = quizzesResponse?.quizzes || [];\n  return (\n    <div>\n      {quizzes.length === 0 ? (\n        <p className=\"text-purple-300\">No quizzes yet. Create one!</p>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {quizzes.map((quiz: Quiz) => (\n            <div\n              key={quiz.id}\n              className=\"bg-slate-800 border border-slate-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-150 ease-in-out\"\n            >\n              <div className=\"mb-3\">\n                <h3 className=\"text-purple-400 font-semibold mb-2 truncate\" title={quiz.name}>\n                  {quiz.name}\n                </h3>\n                {quiz.description && (\n                  <p className=\"text-sm text-purple-300 mb-2 line-clamp-2\">\n                    {quiz.description}\n                  </p>\n                )}\n                <p className=\"text-xs text-purple-300 mb-4\">\n                  Created:{\" \"}\n                  {new Date(quiz.created_at || Date.now()).toLocaleDateString()}\n                </p>\n              </div>\n              <div className=\"flex flex-col gap-2\">\n                <Button\n                  size=\"sm\"\n                  variant=\"outline\"\n                  onClick={() => onPlayQuiz(String(quiz.id))}\n                  className=\"w-full\"\n                >\n                  <span className=\"material-icons md-18 mr-1\">play_arrow</span>\n                  Play\n                </Button>\n                <div className=\"flex gap-2\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => onSelectQuiz(String(quiz.id), quiz.name)}\n                    className=\"flex-1\"\n                  >\n                    <span className=\"material-icons md-18 mr-1\">edit</span>\n                    Manage\n                  </Button>\n                  <Button\n                    size=\"sm\"\n                    variant=\"destructive\"\n                    onClick={() => handleDeleteQuiz(String(quiz.id))}\n                    className=\"flex-1\"\n                  >\n                    <span className=\"material-icons md-18 mr-1\">delete</span>\n                    Delete\n                  </Button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n"}