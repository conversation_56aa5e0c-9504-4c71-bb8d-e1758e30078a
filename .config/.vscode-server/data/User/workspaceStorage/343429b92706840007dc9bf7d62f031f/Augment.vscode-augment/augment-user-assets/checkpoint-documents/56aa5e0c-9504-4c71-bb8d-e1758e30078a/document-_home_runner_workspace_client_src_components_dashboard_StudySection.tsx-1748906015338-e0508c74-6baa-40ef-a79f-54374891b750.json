{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/StudySection.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Link, useLocation } from \"wouter\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { getAllDecks, deleteDeck } from \"@/lib/storage\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Tables } from \"@/types/supabase\";\nimport { DocumentViewer } from \"@/components/document/DocumentViewer\";\nimport { InlineDocumentViewer } from \"@/components/document/InlineDocumentViewer\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { useEscapeKey } from \"@/App\";\nimport {\n  Layers,\n  FileTextIcon,\n  Eye,\n  ListTree,\n  ExternalLink,\n} from \"lucide-react\";\nimport { QuizList } from \"@/components/quiz/QuizList\";\nimport { supabase } from \"@/lib/supabaseClient\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { queryClient } from \"@/lib/queryClient\";\nimport MarkdownRenderer from \"@/components/document/MarkdownRenderer\";\n\ntype StudyDocument = Tables<\"study_documents\">;\n\ninterface SummaryDisplayModalProps {\n  summary: StudyDocument;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst SummaryDisplayModal: React.FC<SummaryDisplayModalProps> = ({ summary, isOpen, onClose }) => {\n  useEscapeKey(onClose);\n\n  if (!isOpen) return null;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\n      <DialogContent className=\"sm:max-w-[625px] bg-slate-800 border-slate-700 text-slate-200\">\n        <DialogHeader>\n          <DialogTitle className=\"text-purple-400\">Summary: {summary.file_name}</DialogTitle>\n        </DialogHeader>\n        <ScrollArea className=\"h-[60vh] mt-4\">\n          <MarkdownRenderer\n            content={summary.extracted_text_summary || \"No summary available.\"}\n            className=\"text-sm leading-relaxed\"\n          />\n        </ScrollArea>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nconst StudySection: React.FC = () => {\n  const [activeTab, setActiveTab] = useState(\"flashcards\");\n  const [selectedDocument, setSelectedDocument] =\n    useState<StudyDocument | null>(null);\n  const [selectedSummary, setSelectedSummary] = useState<StudyDocument | null>(null);\n  const [, navigate] = useLocation();\n  const { user } = useAuth();\n  const { toast } = useToast();\n\n  const { data: decks, isLoading: isLoadingDecks } = useQuery({\n    queryKey: [\"/api/flashcard-decks\"],\n    queryFn: getAllDecks,\n  });\n\n  const { data: documents, isLoading: isLoadingDocuments } = useQuery({\n    queryKey: [\"study-documents\", user?.id],\n    queryFn: async () => {\n      if (!user) return [];\n\n      const { data, error } = await supabase\n        .from(\"study_documents\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: false });\n        // .not(\"extracted_text_summary\", \"is\", null); // For summaries tab, if we only want docs with summaries\n\n      if (error) {\n        console.error(\"Error fetching documents:\", error);\n        return [];\n      }\n\n      return data || [];\n    },\n    enabled: !!user,\n  });\n\n  const documentsWithSummaries = React.useMemo(() => {\n    return (documents || []).filter(doc => doc.extracted_text_summary && doc.extracted_text_summary.trim() !== \"\");\n  }, [documents]);\n\n  const handleDocumentSelect = (doc: StudyDocument) => {\n    setSelectedDocument(doc);\n  };\n\n  const handleDocumentView = (doc: StudyDocument) => {\n    navigate(`/documents/${doc.id}`);\n  };\n\n  const handleSelectQuiz = (quizId: string, quizName: string) => {\n    navigate(`/quizzes/${quizId}/edit`);\n  };\n\n  const handlePlayQuiz = (quizId: string) => {\n    navigate(`/quizzes/${quizId}`);\n  };\n\n  const handleDeleteDeck = async (deckId: string, deckName: string) => {\n    if (\n      !window.confirm(\n        `Are you sure you want to delete the deck \"${deckName}\"? All flashcards in this deck will also be deleted.`\n      )\n    )\n      return;\n\n    try {\n      await deleteDeck(deckId);\n      \n      // Invalidate and refetch the decks\n      queryClient.invalidateQueries({\n        queryKey: [\"/api/flashcard-decks\"],\n      });\n      \n      toast({\n        title: \"Success\",\n        description: `Deck \"${deckName}\" has been deleted.`,\n      });\n    } catch (error) {\n      console.error(\"Error deleting deck:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete the deck. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleManageDeck = (deckId: string) => {\n    navigate(`/flashcards/edit/${deckId}`);\n  };\n\n  const handleDeleteDocument = async (document: StudyDocument) => {\n    try {\n      // Delete from Supabase\n      const { error } = await supabase\n        .from(\"study_documents\")\n        .delete()\n        .eq(\"id\", document.id)\n        .eq(\"user_id\", user?.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Clear selected document if it was the one being deleted\n      if (selectedDocument?.id === document.id) {\n        setSelectedDocument(null);\n      }\n\n      // Invalidate and refetch the documents\n      queryClient.invalidateQueries({\n        queryKey: [\"study-documents\", user?.id],\n      });\n\n      toast({\n        title: \"Success\",\n        description: `Document \"${document.file_name}\" has been deleted.`,\n      });\n    } catch (error) {\n      console.error(\"Error deleting document:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete the document. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const commonCardClasses =\n    \"bg-slate-800 border-slate-700 text-purple-400 shadow-md\";\n  const textPrimaryClass = \"text-purple-400\";\n  const textSecondaryClass = \"text-purple-300\";\n  const textMutedClass = \"text-purple-300\";\n  const accentColor = \"purple-400\";\n  const accentTextClass = `text-${accentColor}`;\n  const accentBorderClass = `border-${accentColor}`;\n  const accentBgClass = `bg-${accentColor}`;\n  const hoverAccentBgClass = `hover:bg-purple-500`;\n\n  return (\n    <section className=\"mb-8 bg-slate-800 p-6 rounded-lg text-purple-400\">\n      <div className=\"flex justify-between items-center mb-4\">\n        <h2 className={`text-xl font-medium ${accentTextClass}`}>\n          Study Materials\n        </h2>\n      </div>\n\n      <Tabs\n        defaultValue=\"flashcards\"\n        value={activeTab}\n        onValueChange={setActiveTab}\n        className=\"mb-6\"\n      >\n        <TabsList className=\"grid w-full grid-cols-4 border-b border-slate-700 mb-4 bg-transparent\">\n          {[\"flashcards\", \"quizzes\", \"summaries\", \"documents\"].map(\n            (tabValue) => (\n              <TabsTrigger\n                key={tabValue}\n                value={tabValue}\n                className={`pb-2 text-sm font-medium rounded-none \n                          data-[state=active]:${accentTextClass} data-[state=active]:border-b-2 data-[state=active]:${accentBorderClass} \n                          ${textMutedClass} hover:${accentTextClass}/80 transition-colors`}\n              >\n                {tabValue.charAt(0).toUpperCase() + tabValue.slice(1)}\n              </TabsTrigger>\n            )\n          )}\n        </TabsList>\n\n        <TabsContent value=\"flashcards\">\n          {isLoadingDecks ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[...Array(3)].map((_, i) => (\n                <Card key={i} className={`${commonCardClasses}`}>\n                  <Skeleton className=\"h-24 w-full bg-slate-700/80\" />\n                  <CardContent className=\"p-4\">\n                    <Skeleton className={`h-6 w-3/4 mb-2 bg-slate-700`} />\n                    <div className=\"flex justify-between mb-3\">\n                      <Skeleton className={`h-4 w-20 bg-slate-700`} />\n                      <Skeleton className={`h-4 w-20 bg-slate-700`} />\n                    </div>\n                    <Skeleton className={`h-2 w-full mb-1 bg-slate-700`} />\n                    <div className=\"flex justify-between mb-3\">\n                      <Skeleton className={`h-3 w-24 bg-slate-700`} />\n                      <Skeleton className={`h-3 w-24 bg-slate-700`} />\n                    </div>\n                    <Skeleton className={`h-9 w-full bg-slate-600`} />\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : decks && decks.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {decks.map((deck) => (\n                <Card\n                  key={deck.id}\n                  className={`overflow-hidden ${commonCardClasses} hover:shadow-lg transition-shadow`}\n                >\n                  <div\n                    className={`h-24 ${accentBgClass}/30 flex items-center justify-center ${accentTextClass}/70`}\n                  >\n                    <Layers size={36} />\n                  </div>\n                  <CardContent className=\"p-4\">\n                    <h3\n                      className={`font-medium ${textPrimaryClass} mb-2 truncate`}\n                      title={deck.name}\n                    >\n                      {deck.name}\n                    </h3>\n                    <div\n                      className={`flex justify-between text-sm ${textMutedClass} mb-3`}\n                    >\n                      <span>{deck.totalCards} cards</span>\n                      <span>{deck.dueTodayCount} due</span>\n                    </div>\n                    <div className=\"mb-3\">\n                      <Progress\n                        value={\n                          deck.totalCards > 0\n                            ? (deck.masteredCount / deck.totalCards) * 100\n                            : 0\n                        }\n                        className={`h-1.5 [&>div]:${accentBgClass}`}\n                      />\n                      <div\n                        className={`flex justify-between mt-1 text-xs ${textMutedClass}`}\n                      >\n                        <span>\n                          {deck.totalCards > 0\n                            ? Math.round(\n                                (deck.masteredCount / deck.totalCards) * 100\n                              )\n                            : 0}\n                          %\n                        </span>\n                        <span>{deck.masteredCount} done</span>\n                      </div>\n                    </div>\n                    <div className=\"flex flex-col gap-2\">\n                      <Link href={`/flashcards/${deck.id}`}>\n                        <Button\n                          className={`w-full ${\n                            deck.dueTodayCount > 0\n                              ? `${accentBgClass} ${hoverAccentBgClass} text-white`\n                              : `bg-slate-700 hover:bg-slate-600 ${textSecondaryClass}`\n                          }`}\n                        >\n                          <span className=\"material-icons md-18 mr-1\">play_arrow</span>\n                          {deck.dueTodayCount > 0\n                            ? \"Review Due Cards\"\n                            : \"Review All\"}\n                        </Button>\n                      </Link>\n                      <div className=\"flex gap-2\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleManageDeck(deck.id)}\n                          className=\"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n                        >\n                          <span className=\"material-icons md-18 mr-1\">edit</span>\n                          Manage\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleDeleteDeck(deck.id, deck.name)}\n                          className=\"flex-1\"\n                        >\n                          <span className=\"material-icons md-18 mr-1\">delete</span>\n                          Delete\n                        </Button>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : (\n            <Card\n              className={`${commonCardClasses} p-6 text-center ${textMutedClass} flex flex-col items-center justify-center min-h-[200px]`}\n            >\n              <Layers size={40} className={`${accentTextClass}/70 mb-3`} />\n              <h3 className={`text-lg font-medium mb-2 ${textPrimaryClass}`}>\n                No Flashcard Decks Yet\n              </h3>\n              <p className={`${textMutedClass} mb-4`}>\n                Upload documents and generate flashcards to start studying.\n              </p>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"quizzes\">\n          <QuizList\n            onSelectQuiz={handleSelectQuiz}\n            onPlayQuiz={handlePlayQuiz}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"summaries\">\n          {isLoadingDocuments ? (\n             <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n             {[...Array(3)].map((_, i) => (\n               <Card key={i} className={`${commonCardClasses}`}>\n                 <Skeleton className=\"h-20 w-full bg-slate-700/80\" />\n                 <CardContent className=\"p-4\">\n                   <Skeleton className={`h-5 w-3/4 mb-2 bg-slate-700`} />\n                   <Skeleton className={`h-4 w-full bg-slate-700`} />\n                   <Skeleton className={`h-4 w-2/3 bg-slate-700 mt-1`} />\n                 </CardContent>\n               </Card>\n             ))}\n           </div>\n          ) : documentsWithSummaries && documentsWithSummaries.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {documentsWithSummaries.map((doc) => (\n                <Card\n                  key={doc.id}\n                  className={`${commonCardClasses} hover:shadow-lg transition-shadow cursor-pointer`}\n                  onClick={() => setSelectedSummary(doc)}\n                >\n                  <CardHeader className=\"pb-2\">\n                    <CardTitle className={`text-md font-medium ${textPrimaryClass} truncate`} title={doc.file_name}>\n                      {doc.file_name}\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <p className={`${textMutedClass} text-sm line-clamp-3`}>\n                      {(doc.extracted_text_summary || \"\").substring(0, 100) + ((doc.extracted_text_summary || \"\").length > 100 ? \"...\" : \"\")}\n                    </p>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : (\n            <Card className={`${commonCardClasses} p-6 text-center ${textMutedClass} flex flex-col items-center justify-center min-h-[200px]`}>\n              <ListTree size={40} className={`${accentTextClass}/70 mb-3`} />\n              <h3 className={`text-lg font-medium mb-2 ${textPrimaryClass}`}>No Summaries Available</h3>\n              <p className={`${textMutedClass} mb-4`}>\n                Upload documents and ensure summaries are generated to see them here.\n              </p>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"documents\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"md:col-span-1\">\n              <Card className={`${commonCardClasses} text-slate-100 h-full`}>\n                <CardHeader>\n                  <CardTitle\n                    className={`text-lg font-medium ${textPrimaryClass}`}\n                  >\n                    Uploaded Documents\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {isLoadingDocuments ? (\n                    <div className=\"space-y-2\">\n                      {[...Array(3)].map((_, i) => (\n                        <Skeleton key={i} className=\"h-8 w-full bg-slate-700\" />\n                      ))}\n                    </div>\n                  ) : documents && documents.length > 0 ? (\n                    <ScrollArea className=\"h-[60vh]\">\n                      <ul className=\"space-y-1\">\n                        {documents.map((doc) => (\n                          <li key={doc.id} className=\"space-y-1\">\n                            <Button\n                              variant=\"ghost\"\n                              className={`w-full justify-start text-left h-auto py-2 px-3 text-sm ${\n                                selectedDocument?.id === doc.id\n                                  ? `${accentBgClass}/30 ${accentTextClass}`\n                                  : `${textMutedClass} hover:bg-slate-700/70 hover:${textSecondaryClass}`\n                              }`}\n                              onClick={() => handleDocumentSelect(doc)}\n                            >\n                              <FileTextIcon\n                                className={`text-base mr-2 flex-shrink-0 ${\n                                  selectedDocument?.id === doc.id\n                                    ? accentTextClass\n                                    : textMutedClass\n                                }`}\n                              />\n                              <span\n                                className=\"truncate flex-grow\"\n                                title={doc.file_name}\n                              >\n                                {doc.file_name}\n                              </span>\n                              <span\n                                className={`text-xs ${textMutedClass} ml-2 whitespace-nowrap`}\n                              >\n                                ({Math.round((doc.size_bytes || 0) / 1024)} KB)\n                              </span>\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              className={`w-full text-xs ${accentTextClass} border-${accentColor}/30 hover:bg-${accentColor}/10`}\n                              onClick={() => handleDocumentView(doc)}\n                            >\n                              <ExternalLink className=\"mr-1 h-3 w-3\" />\n                              Open in Full View\n                            </Button>\n                          </li>\n                        ))}\n                      </ul>\n                    </ScrollArea>\n                  ) : (\n                    <p className={`${textMutedClass} text-sm`}>\n                      No documents uploaded yet. Go to the \"Upload\" section to\n                      add some.\n                    </p>\n                  )}\n                </CardContent>\n              </Card>\n            </div>\n            <div className=\"md:col-span-2\">\n              {selectedDocument ? (\n                <InlineDocumentViewer\n                  document={selectedDocument}\n                  onClose={() => setSelectedDocument(null)}\n                  onDelete={handleDeleteDocument}\n                />\n              ) : (\n                <Card\n                  className={`${commonCardClasses} ${textMutedClass} p-6 text-center flex flex-col justify-center items-center min-h-[300px] h-full`}\n                >\n                  <Eye size={48} className={`${accentTextClass}/70 mb-4`} />\n                  <h3\n                    className={`text-lg font-medium mb-2 ${textPrimaryClass}`}\n                  >\n                    Select a Document\n                  </h3>\n                  <p className={`${textMutedClass}`}>\n                    Choose a document from the list to view its content here.\n                  </p>\n                </Card>\n              )}\n            </div>\n          </div>\n        </TabsContent>\n      </Tabs>\n\n      {selectedSummary && (\n        <SummaryDisplayModal\n          summary={selectedSummary}\n          isOpen={!!selectedSummary}\n          onClose={() => setSelectedSummary(null)}\n        />\n      )}\n    </section>\n  );\n};\n\nexport default StudySection;\n", "modifiedCode": "import React, { useState } from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Link, useLocation } from \"wouter\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { getAllDecks, deleteDeck } from \"@/lib/storage\";\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Tables } from \"@/types/supabase\";\nimport { DocumentViewer } from \"@/components/document/DocumentViewer\";\nimport { InlineDocumentViewer } from \"@/components/document/InlineDocumentViewer\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { useEscapeKey } from \"@/App\";\nimport {\n  Layers,\n  FileTextIcon,\n  Eye,\n  ListTree,\n  ExternalLink,\n} from \"lucide-react\";\nimport { QuizList } from \"@/components/quiz/QuizList\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { queryClient } from \"@/lib/queryClient\";\nimport MarkdownRenderer from \"@/components/document/MarkdownRenderer\";\n\ntype StudyDocument = Tables<\"study_documents\">;\n\ninterface SummaryDisplayModalProps {\n  summary: StudyDocument;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst SummaryDisplayModal: React.FC<SummaryDisplayModalProps> = ({ summary, isOpen, onClose }) => {\n  useEscapeKey(onClose);\n\n  if (!isOpen) return null;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\n      <DialogContent className=\"sm:max-w-[625px] bg-slate-800 border-slate-700 text-slate-200\">\n        <DialogHeader>\n          <DialogTitle className=\"text-purple-400\">Summary: {summary.file_name}</DialogTitle>\n        </DialogHeader>\n        <ScrollArea className=\"h-[60vh] mt-4\">\n          <MarkdownRenderer\n            content={summary.extracted_text_summary || \"No summary available.\"}\n            className=\"text-sm leading-relaxed\"\n          />\n        </ScrollArea>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nconst StudySection: React.FC = () => {\n  const [activeTab, setActiveTab] = useState(\"flashcards\");\n  const [selectedDocument, setSelectedDocument] =\n    useState<StudyDocument | null>(null);\n  const [selectedSummary, setSelectedSummary] = useState<StudyDocument | null>(null);\n  const [, navigate] = useLocation();\n  const { user } = useAuth();\n  const { toast } = useToast();\n\n  const { data: decks, isLoading: isLoadingDecks } = useQuery({\n    queryKey: [\"/api/flashcard-decks\"],\n    queryFn: getAllDecks,\n  });\n\n  const { data: documents, isLoading: isLoadingDocuments } = useQuery({\n    queryKey: [\"study-documents\", user?.id],\n    queryFn: async () => {\n      if (!user) return [];\n\n      const { data, error } = await supabase\n        .from(\"study_documents\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: false });\n        // .not(\"extracted_text_summary\", \"is\", null); // For summaries tab, if we only want docs with summaries\n\n      if (error) {\n        console.error(\"Error fetching documents:\", error);\n        return [];\n      }\n\n      return data || [];\n    },\n    enabled: !!user,\n  });\n\n  const documentsWithSummaries = React.useMemo(() => {\n    return (documents || []).filter(doc => doc.extracted_text_summary && doc.extracted_text_summary.trim() !== \"\");\n  }, [documents]);\n\n  const handleDocumentSelect = (doc: StudyDocument) => {\n    setSelectedDocument(doc);\n  };\n\n  const handleDocumentView = (doc: StudyDocument) => {\n    navigate(`/documents/${doc.id}`);\n  };\n\n  const handleSelectQuiz = (quizId: string, quizName: string) => {\n    navigate(`/quizzes/${quizId}/edit`);\n  };\n\n  const handlePlayQuiz = (quizId: string) => {\n    navigate(`/quizzes/${quizId}`);\n  };\n\n  const handleDeleteDeck = async (deckId: string, deckName: string) => {\n    if (\n      !window.confirm(\n        `Are you sure you want to delete the deck \"${deckName}\"? All flashcards in this deck will also be deleted.`\n      )\n    )\n      return;\n\n    try {\n      await deleteDeck(deckId);\n      \n      // Invalidate and refetch the decks\n      queryClient.invalidateQueries({\n        queryKey: [\"/api/flashcard-decks\"],\n      });\n      \n      toast({\n        title: \"Success\",\n        description: `Deck \"${deckName}\" has been deleted.`,\n      });\n    } catch (error) {\n      console.error(\"Error deleting deck:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete the deck. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleManageDeck = (deckId: string) => {\n    navigate(`/flashcards/edit/${deckId}`);\n  };\n\n  const handleDeleteDocument = async (document: StudyDocument) => {\n    try {\n      // Delete from Supabase\n      const { error } = await supabase\n        .from(\"study_documents\")\n        .delete()\n        .eq(\"id\", document.id)\n        .eq(\"user_id\", user?.id);\n\n      if (error) {\n        throw error;\n      }\n\n      // Clear selected document if it was the one being deleted\n      if (selectedDocument?.id === document.id) {\n        setSelectedDocument(null);\n      }\n\n      // Invalidate and refetch the documents\n      queryClient.invalidateQueries({\n        queryKey: [\"study-documents\", user?.id],\n      });\n\n      toast({\n        title: \"Success\",\n        description: `Document \"${document.file_name}\" has been deleted.`,\n      });\n    } catch (error) {\n      console.error(\"Error deleting document:\", error);\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete the document. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const commonCardClasses =\n    \"bg-slate-800 border-slate-700 text-purple-400 shadow-md\";\n  const textPrimaryClass = \"text-purple-400\";\n  const textSecondaryClass = \"text-purple-300\";\n  const textMutedClass = \"text-purple-300\";\n  const accentColor = \"purple-400\";\n  const accentTextClass = `text-${accentColor}`;\n  const accentBorderClass = `border-${accentColor}`;\n  const accentBgClass = `bg-${accentColor}`;\n  const hoverAccentBgClass = `hover:bg-purple-500`;\n\n  return (\n    <section className=\"mb-8 bg-slate-800 p-6 rounded-lg text-purple-400\">\n      <div className=\"flex justify-between items-center mb-4\">\n        <h2 className={`text-xl font-medium ${accentTextClass}`}>\n          Study Materials\n        </h2>\n      </div>\n\n      <Tabs\n        defaultValue=\"flashcards\"\n        value={activeTab}\n        onValueChange={setActiveTab}\n        className=\"mb-6\"\n      >\n        <TabsList className=\"grid w-full grid-cols-4 border-b border-slate-700 mb-4 bg-transparent\">\n          {[\"flashcards\", \"quizzes\", \"summaries\", \"documents\"].map(\n            (tabValue) => (\n              <TabsTrigger\n                key={tabValue}\n                value={tabValue}\n                className={`pb-2 text-sm font-medium rounded-none \n                          data-[state=active]:${accentTextClass} data-[state=active]:border-b-2 data-[state=active]:${accentBorderClass} \n                          ${textMutedClass} hover:${accentTextClass}/80 transition-colors`}\n              >\n                {tabValue.charAt(0).toUpperCase() + tabValue.slice(1)}\n              </TabsTrigger>\n            )\n          )}\n        </TabsList>\n\n        <TabsContent value=\"flashcards\">\n          {isLoadingDecks ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[...Array(3)].map((_, i) => (\n                <Card key={i} className={`${commonCardClasses}`}>\n                  <Skeleton className=\"h-24 w-full bg-slate-700/80\" />\n                  <CardContent className=\"p-4\">\n                    <Skeleton className={`h-6 w-3/4 mb-2 bg-slate-700`} />\n                    <div className=\"flex justify-between mb-3\">\n                      <Skeleton className={`h-4 w-20 bg-slate-700`} />\n                      <Skeleton className={`h-4 w-20 bg-slate-700`} />\n                    </div>\n                    <Skeleton className={`h-2 w-full mb-1 bg-slate-700`} />\n                    <div className=\"flex justify-between mb-3\">\n                      <Skeleton className={`h-3 w-24 bg-slate-700`} />\n                      <Skeleton className={`h-3 w-24 bg-slate-700`} />\n                    </div>\n                    <Skeleton className={`h-9 w-full bg-slate-600`} />\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : decks && decks.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {decks.map((deck) => (\n                <Card\n                  key={deck.id}\n                  className={`overflow-hidden ${commonCardClasses} hover:shadow-lg transition-shadow`}\n                >\n                  <div\n                    className={`h-24 ${accentBgClass}/30 flex items-center justify-center ${accentTextClass}/70`}\n                  >\n                    <Layers size={36} />\n                  </div>\n                  <CardContent className=\"p-4\">\n                    <h3\n                      className={`font-medium ${textPrimaryClass} mb-2 truncate`}\n                      title={deck.name}\n                    >\n                      {deck.name}\n                    </h3>\n                    <div\n                      className={`flex justify-between text-sm ${textMutedClass} mb-3`}\n                    >\n                      <span>{deck.totalCards} cards</span>\n                      <span>{deck.dueTodayCount} due</span>\n                    </div>\n                    <div className=\"mb-3\">\n                      <Progress\n                        value={\n                          deck.totalCards > 0\n                            ? (deck.masteredCount / deck.totalCards) * 100\n                            : 0\n                        }\n                        className={`h-1.5 [&>div]:${accentBgClass}`}\n                      />\n                      <div\n                        className={`flex justify-between mt-1 text-xs ${textMutedClass}`}\n                      >\n                        <span>\n                          {deck.totalCards > 0\n                            ? Math.round(\n                                (deck.masteredCount / deck.totalCards) * 100\n                              )\n                            : 0}\n                          %\n                        </span>\n                        <span>{deck.masteredCount} done</span>\n                      </div>\n                    </div>\n                    <div className=\"flex flex-col gap-2\">\n                      <Link href={`/flashcards/${deck.id}`}>\n                        <Button\n                          className={`w-full ${\n                            deck.dueTodayCount > 0\n                              ? `${accentBgClass} ${hoverAccentBgClass} text-white`\n                              : `bg-slate-700 hover:bg-slate-600 ${textSecondaryClass}`\n                          }`}\n                        >\n                          <span className=\"material-icons md-18 mr-1\">play_arrow</span>\n                          {deck.dueTodayCount > 0\n                            ? \"Review Due Cards\"\n                            : \"Review All\"}\n                        </Button>\n                      </Link>\n                      <div className=\"flex gap-2\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleManageDeck(deck.id)}\n                          className=\"flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300\"\n                        >\n                          <span className=\"material-icons md-18 mr-1\">edit</span>\n                          Manage\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleDeleteDeck(deck.id, deck.name)}\n                          className=\"flex-1\"\n                        >\n                          <span className=\"material-icons md-18 mr-1\">delete</span>\n                          Delete\n                        </Button>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : (\n            <Card\n              className={`${commonCardClasses} p-6 text-center ${textMutedClass} flex flex-col items-center justify-center min-h-[200px]`}\n            >\n              <Layers size={40} className={`${accentTextClass}/70 mb-3`} />\n              <h3 className={`text-lg font-medium mb-2 ${textPrimaryClass}`}>\n                No Flashcard Decks Yet\n              </h3>\n              <p className={`${textMutedClass} mb-4`}>\n                Upload documents and generate flashcards to start studying.\n              </p>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"quizzes\">\n          <QuizList\n            onSelectQuiz={handleSelectQuiz}\n            onPlayQuiz={handlePlayQuiz}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"summaries\">\n          {isLoadingDocuments ? (\n             <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n             {[...Array(3)].map((_, i) => (\n               <Card key={i} className={`${commonCardClasses}`}>\n                 <Skeleton className=\"h-20 w-full bg-slate-700/80\" />\n                 <CardContent className=\"p-4\">\n                   <Skeleton className={`h-5 w-3/4 mb-2 bg-slate-700`} />\n                   <Skeleton className={`h-4 w-full bg-slate-700`} />\n                   <Skeleton className={`h-4 w-2/3 bg-slate-700 mt-1`} />\n                 </CardContent>\n               </Card>\n             ))}\n           </div>\n          ) : documentsWithSummaries && documentsWithSummaries.length > 0 ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {documentsWithSummaries.map((doc) => (\n                <Card\n                  key={doc.id}\n                  className={`${commonCardClasses} hover:shadow-lg transition-shadow cursor-pointer`}\n                  onClick={() => setSelectedSummary(doc)}\n                >\n                  <CardHeader className=\"pb-2\">\n                    <CardTitle className={`text-md font-medium ${textPrimaryClass} truncate`} title={doc.file_name}>\n                      {doc.file_name}\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <p className={`${textMutedClass} text-sm line-clamp-3`}>\n                      {(doc.extracted_text_summary || \"\").substring(0, 100) + ((doc.extracted_text_summary || \"\").length > 100 ? \"...\" : \"\")}\n                    </p>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : (\n            <Card className={`${commonCardClasses} p-6 text-center ${textMutedClass} flex flex-col items-center justify-center min-h-[200px]`}>\n              <ListTree size={40} className={`${accentTextClass}/70 mb-3`} />\n              <h3 className={`text-lg font-medium mb-2 ${textPrimaryClass}`}>No Summaries Available</h3>\n              <p className={`${textMutedClass} mb-4`}>\n                Upload documents and ensure summaries are generated to see them here.\n              </p>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"documents\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"md:col-span-1\">\n              <Card className={`${commonCardClasses} text-slate-100 h-full`}>\n                <CardHeader>\n                  <CardTitle\n                    className={`text-lg font-medium ${textPrimaryClass}`}\n                  >\n                    Uploaded Documents\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {isLoadingDocuments ? (\n                    <div className=\"space-y-2\">\n                      {[...Array(3)].map((_, i) => (\n                        <Skeleton key={i} className=\"h-8 w-full bg-slate-700\" />\n                      ))}\n                    </div>\n                  ) : documents && documents.length > 0 ? (\n                    <ScrollArea className=\"h-[60vh]\">\n                      <ul className=\"space-y-1\">\n                        {documents.map((doc) => (\n                          <li key={doc.id} className=\"space-y-1\">\n                            <Button\n                              variant=\"ghost\"\n                              className={`w-full justify-start text-left h-auto py-2 px-3 text-sm ${\n                                selectedDocument?.id === doc.id\n                                  ? `${accentBgClass}/30 ${accentTextClass}`\n                                  : `${textMutedClass} hover:bg-slate-700/70 hover:${textSecondaryClass}`\n                              }`}\n                              onClick={() => handleDocumentSelect(doc)}\n                            >\n                              <FileTextIcon\n                                className={`text-base mr-2 flex-shrink-0 ${\n                                  selectedDocument?.id === doc.id\n                                    ? accentTextClass\n                                    : textMutedClass\n                                }`}\n                              />\n                              <span\n                                className=\"truncate flex-grow\"\n                                title={doc.file_name}\n                              >\n                                {doc.file_name}\n                              </span>\n                              <span\n                                className={`text-xs ${textMutedClass} ml-2 whitespace-nowrap`}\n                              >\n                                ({Math.round((doc.size_bytes || 0) / 1024)} KB)\n                              </span>\n                            </Button>\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              className={`w-full text-xs ${accentTextClass} border-${accentColor}/30 hover:bg-${accentColor}/10`}\n                              onClick={() => handleDocumentView(doc)}\n                            >\n                              <ExternalLink className=\"mr-1 h-3 w-3\" />\n                              Open in Full View\n                            </Button>\n                          </li>\n                        ))}\n                      </ul>\n                    </ScrollArea>\n                  ) : (\n                    <p className={`${textMutedClass} text-sm`}>\n                      No documents uploaded yet. Go to the \"Upload\" section to\n                      add some.\n                    </p>\n                  )}\n                </CardContent>\n              </Card>\n            </div>\n            <div className=\"md:col-span-2\">\n              {selectedDocument ? (\n                <InlineDocumentViewer\n                  document={selectedDocument}\n                  onClose={() => setSelectedDocument(null)}\n                  onDelete={handleDeleteDocument}\n                />\n              ) : (\n                <Card\n                  className={`${commonCardClasses} ${textMutedClass} p-6 text-center flex flex-col justify-center items-center min-h-[300px] h-full`}\n                >\n                  <Eye size={48} className={`${accentTextClass}/70 mb-4`} />\n                  <h3\n                    className={`text-lg font-medium mb-2 ${textPrimaryClass}`}\n                  >\n                    Select a Document\n                  </h3>\n                  <p className={`${textMutedClass}`}>\n                    Choose a document from the list to view its content here.\n                  </p>\n                </Card>\n              )}\n            </div>\n          </div>\n        </TabsContent>\n      </Tabs>\n\n      {selectedSummary && (\n        <SummaryDisplayModal\n          summary={selectedSummary}\n          isOpen={!!selectedSummary}\n          onClose={() => setSelectedSummary(null)}\n        />\n      )}\n    </section>\n  );\n};\n\nexport default StudySection;\n"}