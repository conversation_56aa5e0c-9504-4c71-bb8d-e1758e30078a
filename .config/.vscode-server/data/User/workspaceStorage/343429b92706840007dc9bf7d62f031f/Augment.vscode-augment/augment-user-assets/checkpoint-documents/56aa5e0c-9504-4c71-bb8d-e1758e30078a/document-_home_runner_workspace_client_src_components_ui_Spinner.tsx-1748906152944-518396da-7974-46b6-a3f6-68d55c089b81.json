{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/Spinner.tsx"}, "originalCode": "import React from \"react\";\n\nconst Spinner: React.FC<{ size?: \"sm\" | \"md\" | \"lg\"; className?: string }> = ({ size = \"md\", className = \"\" }) => {\n  const sizeClasses = {\n    sm: \"w-6 h-6\",\n    md: \"w-10 h-10\",\n    lg: \"w-16 h-16\",\n  };\n\n  return (\n    <div className={`flex justify-center items-center`}>\n      <div\n        className={`animate-spin rounded-full ${sizeClasses[size]} border-t-2 border-b-2 border-primary`}\n      ></div>\n    </div>\n  );\n};\n\nexport default Spinner;\n", "modifiedCode": "import React from \"react\";\n\nconst Spinner: React.FC<{ size?: \"sm\" | \"md\" | \"lg\"; className?: string }> = ({ size = \"md\", className = \"\" }) => {\n  const sizeClasses = {\n    sm: \"w-6 h-6\",\n    md: \"w-10 h-10\",\n    lg: \"w-16 h-16\",\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div\n        className={`animate-spin rounded-full ${sizeClasses[size]} border-t-2 border-b-2 border-primary`}\n      ></div>\n    </div>\n  );\n};\n\nexport default Spinner;\n"}