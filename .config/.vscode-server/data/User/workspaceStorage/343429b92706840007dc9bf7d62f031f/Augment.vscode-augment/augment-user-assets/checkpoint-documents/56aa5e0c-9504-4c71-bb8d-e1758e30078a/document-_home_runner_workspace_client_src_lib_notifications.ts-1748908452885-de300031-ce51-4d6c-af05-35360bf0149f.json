{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/notifications.ts"}, "originalCode": "import { toast } from \"@/hooks/use-toast\";\n\nexport interface NotificationOptions {\n  title: string;\n  description?: string;\n  duration?: number;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n}\n\nexport class NotificationManager {\n  static success(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 4000,\n      className: \"border-green-500 bg-green-950 text-green-100\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static error(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 6000,\n      variant: \"destructive\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static warning(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 5000,\n      className: \"border-yellow-500 bg-yellow-950 text-yellow-100\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static info(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 4000,\n      className: \"border-blue-500 bg-blue-950 text-blue-100\",\n      action: options.action\n        ? <ToastAction onClick={options.action.onClick}>{options.action.label}</ToastAction>\n        : undefined,\n    });\n  }\n\n  static loading(options: Omit<NotificationOptions, \"duration\">) {\n    return toast({\n      title: options.title,\n      description: options.description,\n      duration: Infinity,\n      className: \"border-purple-500 bg-purple-950 text-purple-100\",\n    });\n  }\n\n  static dismiss(toastId?: string) {\n    if (toastId) {\n      // Individual toast dismissal would need to be implemented in the toast system\n      console.log(\"Dismissing toast:\", toastId);\n    }\n  }\n}\n\nexport const notify = {\n  success: NotificationManager.success,\n  error: NotificationManager.error,\n  warning: NotificationManager.warning,\n  info: NotificationManager.info,\n  loading: NotificationManager.loading,\n  dismiss: NotificationManager.dismiss,\n};\n\nexport const commonNotifications = {\n  documentUploaded: (fileName: string) =>\n    notify.success({\n      title: \"Document uploaded successfully\",\n      description: `${fileName} has been processed and is ready for study.`,\n    }),\n\n  documentUploadFailed: (error: string) =>\n    notify.error({\n      title: \"Document upload failed\",\n      description: error,\n      action: {\n        label: \"Retry\",\n        onClick: () => window.location.reload(),\n      },\n    }),\n\n  flashcardsGenerated: (count: number) =>\n    notify.success({\n      title: \"Flashcards generated\",\n      description: `Successfully created ${count} flashcards for your study.`,\n    }),\n\n  flashcardGenerationFailed: (error: string) =>\n    notify.error({\n      title: \"Flashcard generation failed\",\n      description: error,\n    }),\n\n  quizGenerated: (name: string) =>\n    notify.success({\n      title: \"Quiz generated\",\n      description: `\"${name}\" is ready for you to take.`,\n    }),\n\n  quizGenerationFailed: (error: string) =>\n    notify.error({\n      title: \"Quiz generation failed\",\n      description: error,\n    }),\n\n  aiConfigSaved: () =>\n    notify.success({\n      title: \"AI configuration saved\",\n      description: \"Your AI provider settings have been updated.\",\n    }),\n\n  aiConfigFailed: (error: string) =>\n    notify.error({\n      title: \"Failed to save AI configuration\",\n      description: error,\n    }),\n\n  dataExported: (fileName: string) =>\n    notify.success({\n      title: \"Data exported\",\n      description: `Your study data has been exported to ${fileName}.`,\n    }),\n\n  dataExportFailed: (error: string) =>\n    notify.error({\n      title: \"Export failed\",\n      description: error,\n    }),\n\n  sessionExpired: () =>\n    notify.warning({\n      title: \"Session expired\",\n      description: \"Please sign in again to continue.\",\n      action: {\n        label: \"Sign In\",\n        onClick: () => (window.location.href = \"/login\"),\n      },\n    }),\n\n  networkError: () =>\n    notify.error({\n      title: \"Network error\",\n      description: \"Please check your internet connection and try again.\",\n      action: {\n        label: \"Retry\",\n        onClick: () => window.location.reload(),\n      },\n    }),\n\n  comingSoon: (feature: string) =>\n    notify.info({\n      title: \"Coming soon\",\n      description: `${feature} is currently under development.`,\n    }),\n};\n", "modifiedCode": "import { toast } from \"@/hooks/use-toast\";\n\nexport interface NotificationOptions {\n  title: string;\n  description?: string;\n  duration?: number;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n}\n\nexport class NotificationManager {\n  static success(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 4000,\n      className: \"border-green-500 bg-green-950 text-green-100\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static error(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 6000,\n      variant: \"destructive\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static warning(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 5000,\n      className: \"border-yellow-500 bg-yellow-950 text-yellow-100\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static info(options: NotificationOptions) {\n    toast({\n      title: options.title,\n      description: options.description,\n      duration: options.duration || 4000,\n      className: \"border-blue-500 bg-blue-950 text-blue-100\",\n      action: options.action\n        ? {\n            label: options.action.label,\n            onClick: options.action.onClick,\n          }\n        : undefined,\n    });\n  }\n\n  static loading(options: Omit<NotificationOptions, \"duration\">) {\n    return toast({\n      title: options.title,\n      description: options.description,\n      duration: Infinity,\n      className: \"border-purple-500 bg-purple-950 text-purple-100\",\n    });\n  }\n\n  static dismiss(toastId?: string) {\n    if (toastId) {\n      // Individual toast dismissal would need to be implemented in the toast system\n      console.log(\"Dismissing toast:\", toastId);\n    }\n  }\n}\n\nexport const notify = {\n  success: NotificationManager.success,\n  error: NotificationManager.error,\n  warning: NotificationManager.warning,\n  info: NotificationManager.info,\n  loading: NotificationManager.loading,\n  dismiss: NotificationManager.dismiss,\n};\n\nexport const commonNotifications = {\n  documentUploaded: (fileName: string) =>\n    notify.success({\n      title: \"Document uploaded successfully\",\n      description: `${fileName} has been processed and is ready for study.`,\n    }),\n\n  documentUploadFailed: (error: string) =>\n    notify.error({\n      title: \"Document upload failed\",\n      description: error,\n      action: {\n        label: \"Retry\",\n        onClick: () => window.location.reload(),\n      },\n    }),\n\n  flashcardsGenerated: (count: number) =>\n    notify.success({\n      title: \"Flashcards generated\",\n      description: `Successfully created ${count} flashcards for your study.`,\n    }),\n\n  flashcardGenerationFailed: (error: string) =>\n    notify.error({\n      title: \"Flashcard generation failed\",\n      description: error,\n    }),\n\n  quizGenerated: (name: string) =>\n    notify.success({\n      title: \"Quiz generated\",\n      description: `\"${name}\" is ready for you to take.`,\n    }),\n\n  quizGenerationFailed: (error: string) =>\n    notify.error({\n      title: \"Quiz generation failed\",\n      description: error,\n    }),\n\n  aiConfigSaved: () =>\n    notify.success({\n      title: \"AI configuration saved\",\n      description: \"Your AI provider settings have been updated.\",\n    }),\n\n  aiConfigFailed: (error: string) =>\n    notify.error({\n      title: \"Failed to save AI configuration\",\n      description: error,\n    }),\n\n  dataExported: (fileName: string) =>\n    notify.success({\n      title: \"Data exported\",\n      description: `Your study data has been exported to ${fileName}.`,\n    }),\n\n  dataExportFailed: (error: string) =>\n    notify.error({\n      title: \"Export failed\",\n      description: error,\n    }),\n\n  sessionExpired: () =>\n    notify.warning({\n      title: \"Session expired\",\n      description: \"Please sign in again to continue.\",\n      action: {\n        label: \"Sign In\",\n        onClick: () => (window.location.href = \"/login\"),\n      },\n    }),\n\n  networkError: () =>\n    notify.error({\n      title: \"Network error\",\n      description: \"Please check your internet connection and try again.\",\n      action: {\n        label: \"Retry\",\n        onClick: () => window.location.reload(),\n      },\n    }),\n\n  comingSoon: (feature: string) =>\n    notify.info({\n      title: \"Coming soon\",\n      description: `${feature} is currently under development.`,\n    }),\n};\n"}