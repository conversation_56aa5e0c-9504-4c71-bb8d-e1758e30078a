{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Dashboard.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport DashboardOverview from \"@/components/dashboard/DashboardOverview\";\nimport UploadSection from \"@/components/dashboard/UploadSection\";\nimport StudySection from \"@/components/dashboard/StudySection\";\nimport ExportSection from \"@/components/export/ExportSection\";\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { cacheDocument, saveFlashcards } from \"@/lib/storage\";\nimport { Document as DocumentType } from \"@/types\";\nimport { Flashcard } from \"@/types\";\nimport { Quiz } from \"../../../shared/types/quiz\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { uploadDocumentSecure } from \"@/lib/api\";\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n  const [currentDocument, setCurrentDocument] = useState<DocumentType | null>(\n    null\n  );\n  const [generatedFlashcards, setGeneratedFlashcards] = useState<Flashcard[]>(\n    []\n  );\n\n  const handleDocumentProcessed = async (document: DocumentType) => {\n    setCurrentDocument(document);\n\n    // Cache document locally for backward compatibility and offline access\n    await cacheDocument(document);\n\n    // Use secure backend upload instead of direct Supabase upload\n    if (user) {\n      try {\n        console.log(\"Starting secure upload for document:\", document.name);\n\n        const uploadResult = await uploadDocumentSecure({\n          fileName: document.name,\n          content: document.content,\n          contentType: document.type,\n          sizeBytes: document.size,\n          documentId: document.id, // Use the client-generated document ID for consistency\n        });\n\n        console.log(\"Secure upload successful:\", uploadResult);\n\n        toast({\n          title: \"Success\",\n          description: \"Document processed and uploaded securely!\",\n        });\n\n        // Invalidate queries to refresh the sidebar\n        await queryClient.invalidateQueries({\n          queryKey: [\"study-documents\", user.id],\n        });\n      } catch (error: any) {\n        console.error(\"Secure upload failed:\", error);\n        toast({\n          title: \"Upload Failed\",\n          description:\n            error.message ||\n            \"Failed to upload document securely. Document is cached locally only.\",\n          variant: \"destructive\",\n        });\n      }\n    } else {\n      toast({\n        title: \"Not Authenticated\",\n        description: \"Please log in to save documents to the cloud.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleFlashcardsGenerated = (flashcards: Flashcard[]) => {\n    setGeneratedFlashcards(flashcards);\n    if (currentDocument) {\n      saveFlashcards(flashcards, currentDocument.id);\n    }\n  };\n\n  const handleQuizGenerated = (quiz: Quiz) => {\n    console.log(\"Quiz generated:\", quiz);\n    // You could add logic here to store the quiz or navigate to it\n  };\n\n  return (\n    <AppLayout title=\"Dashboard\">\n      <div className=\"mb-6 bg-slate-800 p-6 rounded-lg text-purple-400\">\n        <h1 className=\"text-2xl font-medium text-purple-400 mb-1\">Dashboard</h1>\n        <p className=\"text-purple-300\">\n          Welcome to ChewyAI. Let's make studying more effective!\n        </p>\n      </div>\n\n      <DashboardOverview />\n\n      <div className=\"mb-8\">\n        <UploadSection\n          onDocumentProcessed={handleDocumentProcessed}\n          onFlashcardsGenerated={handleFlashcardsGenerated}\n          onQuizGenerated={handleQuizGenerated}\n        />\n      </div>\n\n      <div className=\"mb-8\">\n        <StudySection />\n      </div>\n\n      <div className=\"mb-8\">\n        <ExportSection />\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default Dashboard;\n", "modifiedCode": "import React, { useState } from \"react\";\nimport AppLayout from \"@/components/layout/AppLayout\";\nimport DashboardOverview from \"@/components/dashboard/DashboardOverview\";\nimport UploadSection from \"@/components/dashboard/UploadSection\";\nimport StudySection from \"@/components/dashboard/StudySection\";\nimport ExportSection from \"@/components/export/ExportSection\";\nimport { useQueryClient } from \"@tanstack/react-query\";\nimport { cacheDocument, saveFlashcards } from \"@/lib/storage\";\nimport { Document as DocumentType } from \"@/types\";\nimport { Flashcard } from \"@/types\";\nimport { Quiz } from \"../../../shared/types/quiz\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { uploadDocumentSecure } from \"@/lib/api\";\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n  const [currentDocument, setCurrentDocument] = useState<DocumentType | null>(\n    null\n  );\n  const [generatedFlashcards, setGeneratedFlashcards] = useState<Flashcard[]>(\n    []\n  );\n\n  const handleDocumentProcessed = async (document: DocumentType) => {\n    setCurrentDocument(document);\n\n    // Cache document locally for backward compatibility and offline access\n    await cacheDocument(document);\n\n    // Use secure backend upload instead of direct Supabase upload\n    if (user) {\n      try {\n        console.log(\"Starting secure upload for document:\", document.name);\n\n        const uploadResult = await uploadDocumentSecure({\n          fileName: document.name,\n          content: document.content,\n          contentType: document.type,\n          sizeBytes: document.size,\n          documentId: document.id, // Use the client-generated document ID for consistency\n        });\n\n        console.log(\"Secure upload successful:\", uploadResult);\n\n        toast({\n          title: \"Success\",\n          description: \"Document processed and uploaded securely!\",\n        });\n\n        // Invalidate queries to refresh the sidebar\n        await queryClient.invalidateQueries({\n          queryKey: [\"study-documents\", user.id],\n        });\n      } catch (error: any) {\n        console.error(\"Secure upload failed:\", error);\n        toast({\n          title: \"Upload Failed\",\n          description:\n            error.message ||\n            \"Failed to upload document securely. Document is cached locally only.\",\n          variant: \"destructive\",\n        });\n      }\n    } else {\n      toast({\n        title: \"Not Authenticated\",\n        description: \"Please log in to save documents to the cloud.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  const handleFlashcardsGenerated = (flashcards: Flashcard[]) => {\n    setGeneratedFlashcards(flashcards);\n    if (currentDocument) {\n      saveFlashcards(flashcards, currentDocument.id);\n    }\n  };\n\n  const handleQuizGenerated = (quiz: Quiz) => {\n    console.log(\"Quiz generated:\", quiz);\n    // You could add logic here to store the quiz or navigate to it\n  };\n\n  return (\n    <AppLayout title=\"Dashboard\">\n      <div className=\"mb-6 bg-slate-800 p-6 rounded-lg text-purple-400\">\n        <h1 className=\"text-2xl font-medium text-purple-400 mb-1\">Dashboard</h1>\n        <p className=\"text-purple-300\">\n          Welcome to ChewyAI. Let's make studying more effective!\n        </p>\n      </div>\n\n      <DashboardOverview />\n\n      <div className=\"mb-8\">\n        <UploadSection\n          onDocumentProcessed={handleDocumentProcessed}\n          onFlashcardsGenerated={handleFlashcardsGenerated}\n          onQuizGenerated={handleQuizGenerated}\n        />\n      </div>\n\n      <div className=\"mb-8\">\n        <StudySection />\n      </div>\n\n      <div className=\"mb-8\">\n        <ExportSection />\n      </div>\n    </AppLayout>\n  );\n};\n\nexport default Dashboard;\n"}