{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/MobileNav.tsx"}, "originalCode": "import React from \"react\";\nimport { Link, useLocation } from \"wouter\";\nimport {\n  LucideIcon,\n  Home,\n  BookOpen,\n  FileQuestion,\n  Upload,\n  Settings,\n} from \"lucide-react\";\n\ninterface NavItemProps {\n  href: string;\n  icon: LucideIcon;\n  label: string;\n  isActive: boolean;\n  onClick?: () => void;\n}\n\nconst NavItem: React.FC<NavItemProps> = ({\n  href,\n  icon: Icon,\n  label,\n  isActive,\n  onClick,\n}) => (\n  <Link\n    href={href}\n    onClick={onClick}\n    className={`flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors duration-150 ease-in-out \n      ${isActive ? \"text-purple-400\" : \"text-slate-400 hover:text-purple-300\"}`}\n    aria-label={label}\n  >\n    <Icon size={20} className=\"mb-0.5\" />\n    <span className=\"text-xs tracking-tight leading-tight\">{label}</span>\n  </Link>\n);\n\nconst MobileNav: React.FC = () => {\n  const [location, navigate] = useLocation();\n\n  const handleUploadClick = (e: React.MouseEvent) => {\n    e.preventDefault(); // Prevent navigation if it's just a trigger\n    navigate(\"/\"); // Navigate to dashboard first\n    // Dispatch event to open upload dialog on dashboard page\n    setTimeout(() => {\n      window.dispatchEvent(new CustomEvent(\"openDocumentUploadDialog\"));\n    }, 100); // Timeout to allow dashboard to mount if navigating\n  };\n\n  return (\n    <nav className=\"md:hidden bg-slate-900 border-t border-slate-700 fixed bottom-0 left-0 right-0 z-40 flex justify-around items-stretch\">\n      <NavItem\n        href=\"/\"\n        icon={Home}\n        label=\"Dashboard\"\n        isActive={location === \"/\"}\n      />\n      <NavItem\n        href=\"/flashcards\"\n        icon={BookOpen}\n        label=\"Flashcards\"\n        isActive={location.startsWith(\"/flashcards\")}\n      />\n      <NavItem\n        href=\"/quizzes\"\n        icon={FileQuestion}\n        label=\"Quizzes\"\n        isActive={location.startsWith(\"/quizzes\")}\n      />\n      <NavItem\n        href=\"/\"\n        icon={Upload}\n        label=\"Upload\"\n        isActive={false}\n        onClick={handleUploadClick}\n      />\n      <NavItem\n        href=\"/ai-config\"\n        icon={Settings}\n        label=\"Settings\"\n        isActive={location === \"/ai-config\"}\n      />\n    </nav>\n  );\n};\n\nexport default MobileNav;\n", "modifiedCode": "import React from \"react\";\nimport { Link, useLocation } from \"wouter\";\nimport {\n  LucideIcon,\n  Home,\n  BookOpen,\n  FileQuestion,\n  Upload,\n  Settings,\n} from \"lucide-react\";\n\ninterface NavItemProps {\n  href: string;\n  icon: LucideIcon;\n  label: string;\n  isActive: boolean;\n  onClick?: (e: React.MouseEvent) => void;\n}\n\nconst NavItem: React.FC<NavItemProps> = ({\n  href,\n  icon: Icon,\n  label,\n  isActive,\n  onClick,\n}) => (\n  <Link\n    href={href}\n    onClick={onClick}\n    className={`flex flex-col items-center justify-center flex-1 py-2 px-1 transition-colors duration-150 ease-in-out \n      ${isActive ? \"text-purple-400\" : \"text-slate-400 hover:text-purple-300\"}`}\n    aria-label={label}\n  >\n    <Icon size={20} className=\"mb-0.5\" />\n    <span className=\"text-xs tracking-tight leading-tight\">{label}</span>\n  </Link>\n);\n\nconst MobileNav: React.FC = () => {\n  const [location, navigate] = useLocation();\n\n  const handleUploadClick = (e: React.MouseEvent) => {\n    e.preventDefault(); // Prevent navigation if it's just a trigger\n    navigate(\"/\"); // Navigate to dashboard first\n    // Dispatch event to open upload dialog on dashboard page\n    setTimeout(() => {\n      window.dispatchEvent(new CustomEvent(\"openDocumentUploadDialog\"));\n    }, 100); // Timeout to allow dashboard to mount if navigating\n  };\n\n  return (\n    <nav className=\"md:hidden bg-slate-900 border-t border-slate-700 fixed bottom-0 left-0 right-0 z-40 flex justify-around items-stretch\">\n      <NavItem\n        href=\"/\"\n        icon={Home}\n        label=\"Dashboard\"\n        isActive={location === \"/\"}\n      />\n      <NavItem\n        href=\"/flashcards\"\n        icon={BookOpen}\n        label=\"Flashcards\"\n        isActive={location.startsWith(\"/flashcards\")}\n      />\n      <NavItem\n        href=\"/quizzes\"\n        icon={FileQuestion}\n        label=\"Quizzes\"\n        isActive={location.startsWith(\"/quizzes\")}\n      />\n      <NavItem\n        href=\"/\"\n        icon={Upload}\n        label=\"Upload\"\n        isActive={false}\n        onClick={handleUploadClick}\n      />\n      <NavItem\n        href=\"/ai-config\"\n        icon={Settings}\n        label=\"Settings\"\n        isActive={location === \"/ai-config\"}\n      />\n    </nav>\n  );\n};\n\nexport default MobileNav;\n"}