{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/CreateFlashcardSetForm.tsx"}, "originalCode": "import React, { useState, FormEvent } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { useAuth } from \"../../hooks/useAuth\";\n\ninterface CreateFlashcardSetFormProps {\n  studyDocumentId?: string; // Optional: if creating a set linked to a document\n  onSetCreated: (setId: string, setName: string) => void; // Callback after successful creation\n}\n\nexport const CreateFlashcardSetForm: React.FC<CreateFlashcardSetFormProps> = ({\n  studyDocumentId,\n  onSetCreated,\n}) => {\n  const { user } = useAuth();\n  const [name, setName] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    if (!user) {\n      setError(\"You must be logged in.\");\n      return;\n    }\n    if (!name.trim()) {\n      setError(\"Set name is required.\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const { data, error: dbError } = await supabase\n        .from(\"flashcard_sets\")\n        .insert({\n          user_id: user.id,\n          name,\n          description,\n          study_document_id: studyDocumentId, // Can be null\n        })\n        .select(\"id\")\n        .single();\n\n      if (dbError) throw dbError;\n\n      const createdSetName = name; // Store the name before resetting\n      setName(\"\");\n      setDescription(\"\");\n      alert(\"Flashcard set created successfully!\");\n      if (data?.id) {\n        onSetCreated(data.id, createdSetName);\n      }\n    } catch (err: any) {\n      setError(err.message || \"Failed to create flashcard set.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"p-4 bg-slate-800 border border-slate-700 shadow-md rounded-lg mt-5\">\n      <h3 className=\"text-lg font-semibold mb-3 text-purple-400\">\n        Create New Flashcard Set\n      </h3>\n      {error && <p className=\"text-red-400 text-sm mb-2\">{error}</p>}\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        <div>\n          <label\n            htmlFor=\"setName\"\n            className=\"block text-sm font-medium text-slate-300\"\n          >\n            Set Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"setName\"\n            value={name}\n            onChange={(e) => setName(e.target.value)}\n            required\n            className=\"mt-1 block w-full px-3 py-2 border !border-slate-700 !bg-slate-900 !text-slate-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm placeholder:text-slate-500\"\n          />\n        </div>\n        <div>\n          <label\n            htmlFor=\"setDescription\"\n            className=\"block text-sm font-medium text-slate-300\"\n          >\n            Description (Optional)\n          </label>\n          <textarea\n            id=\"setDescription\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            rows={3}\n            className=\"mt-1 block w-full px-3 py-2 border !border-slate-700 !bg-slate-900 !text-slate-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm placeholder:text-slate-500\"\n          />\n        </div>\n        {studyDocumentId && (\n          <p className=\"text-sm text-slate-400\">\n            Linking to document ID: {studyDocumentId}\n          </p>\n        )}\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-purple-500 disabled:opacity-50 disabled:bg-slate-500\"\n        >\n          {loading ? \"Creating...\" : \"Create Set\"}\n        </button>\n      </form>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useState, FormEvent } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { useAuth } from \"../../hooks/useAuth\";\n\ninterface CreateFlashcardSetFormProps {\n  studyDocumentId?: string; // Optional: if creating a set linked to a document\n  onSetCreated: (setId: string, setName: string) => void; // Callback after successful creation\n}\n\nexport const CreateFlashcardSetForm: React.FC<CreateFlashcardSetFormProps> = ({\n  studyDocumentId,\n  onSetCreated,\n}) => {\n  const { user } = useAuth();\n  const [name, setName] = useState(\"\");\n  const [description, setDescription] = useState(\"\");\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    if (!user) {\n      setError(\"You must be logged in.\");\n      return;\n    }\n    if (!name.trim()) {\n      setError(\"Set name is required.\");\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const { data, error: dbError } = await supabase\n        .from(\"flashcard_sets\")\n        .insert({\n          user_id: user.id,\n          name,\n          description,\n          study_document_id: studyDocumentId, // Can be null\n        })\n        .select(\"id\")\n        .single();\n\n      if (dbError) throw dbError;\n\n      const createdSetName = name; // Store the name before resetting\n      setName(\"\");\n      setDescription(\"\");\n      alert(\"Flashcard set created successfully!\");\n      if (data?.id) {\n        onSetCreated(data.id, createdSetName);\n      }\n    } catch (err: any) {\n      setError(err.message || \"Failed to create flashcard set.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"p-4 bg-slate-800 border border-slate-700 shadow-md rounded-lg mt-5\">\n      <h3 className=\"text-lg font-semibold mb-3 text-purple-400\">\n        Create New Flashcard Set\n      </h3>\n      {error && <p className=\"text-red-400 text-sm mb-2\">{error}</p>}\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        <div>\n          <label\n            htmlFor=\"setName\"\n            className=\"block text-sm font-medium text-slate-300\"\n          >\n            Set Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"setName\"\n            value={name}\n            onChange={(e) => setName(e.target.value)}\n            required\n            className=\"mt-1 block w-full px-3 py-2 border !border-slate-700 !bg-slate-900 !text-slate-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm placeholder:text-slate-500\"\n          />\n        </div>\n        <div>\n          <label\n            htmlFor=\"setDescription\"\n            className=\"block text-sm font-medium text-slate-300\"\n          >\n            Description (Optional)\n          </label>\n          <textarea\n            id=\"setDescription\"\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            rows={3}\n            className=\"mt-1 block w-full px-3 py-2 border !border-slate-700 !bg-slate-900 !text-slate-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm placeholder:text-slate-500\"\n          />\n        </div>\n        {studyDocumentId && (\n          <p className=\"text-sm text-slate-400\">\n            Linking to document ID: {studyDocumentId}\n          </p>\n        )}\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-purple-500 disabled:opacity-50 disabled:bg-slate-500\"\n        >\n          {loading ? \"Creating...\" : \"Create Set\"}\n        </button>\n      </form>\n    </div>\n  );\n};\n"}