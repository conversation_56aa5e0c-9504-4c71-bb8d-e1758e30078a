{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizPlayer.tsx"}, "originalCode": "import React, { useEffect, useState } from \"react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  ChevronLeft,\n  ChevronRight,\n  X,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Trophy,\n  RotateCcw,\n} from \"lucide-react\";\nimport { notify } from \"@/lib/notifications\";\nimport { useKeyboardNavigation } from \"@/hooks/useKeyboardNavigation\";\nimport { useQuizSettings } from \"@/contexts/QuizSettingsContext\";\nimport { QuizSettingsToggle } from \"./QuizSettingsToggle\";\nimport {\n  ReviewDifficulty,\n  updateQuizQuestionSRS,\n  sortQuizQuestionsByDueDate,\n  getQuizQuestionsDueCount,\n} from \"@/lib/srs\";\nimport { QuizAPI } from \"@/lib/api/quizApi\";\ntype Quiz = Tables<\"quizzes\">;\ntype QuizQuestion = Tables<\"quiz_questions\">;\n// Ensure McqOption type is defined if you use it for displaying options\n// interface McqOption { text: string; is_correct: boolean; id?: string; }\n\ninterface QuizPlayerProps {\n  quizId: string;\n  onQuizComplete?: (score: number, totalQuestions: number) => void; // Optional callback\n  onExit: () => void; // Callback to close the player\n}\n\ninterface QuizResults {\n  score: number;\n  totalQuestions: number;\n  percentage: number;\n  timeSpent: number;\n}\n\nexport const QuizPlayer: React.FC<QuizPlayerProps> = ({\n  quizId,\n  onQuizComplete,\n  onExit,\n}) => {\n  const { user } = useAuth();\n  const { settings } = useQuizSettings();\n  const [quiz, setQuiz] = useState<Quiz | null>(null);\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({}); // { questionId: answer }\n  const [submittedAnswers, setSubmittedAnswers] = useState<Array<boolean>>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const [results, setResults] = useState<QuizResults | null>(null);\n  const [startTime] = useState(Date.now());\n\n  useEffect(() => {\n    const fetchQuizData = async () => {\n      if (!user || !quizId) return;\n      setLoading(true);\n      setError(null);\n      try {\n        // Fetch quiz details\n        const { data: quizData, error: quizError } = await supabase\n          .from(\"quizzes\")\n          .select(\"*\")\n          .eq(\"id\", quizId)\n          .eq(\"user_id\", user.id)\n          .single();\n        if (quizError) throw quizError;\n        setQuiz(quizData);\n\n        // Fetch quiz questions\n        const { data: questionsData, error: questionsError } = await supabase\n          .from(\"quiz_questions\")\n          .select(\"*\")\n          .eq(\"quiz_id\", quizId)\n          .eq(\"user_id\", user.id) // RLS should cover this, but good for explicitness\n          .order(\"created_at\", { ascending: true });\n        if (questionsError) throw questionsError;\n\n        // Sort questions by SRS due date for optimal spaced repetition\n        const sortedQuestions = sortQuizQuestionsByDueDate(questionsData || []);\n        setQuestions(sortedQuestions);\n        setSubmittedAnswers(new Array(sortedQuestions.length).fill(false));\n      } catch (err: any) {\n        console.error(\"Error fetching quiz data:\", err);\n        setError(err.message || \"Failed to load quiz.\");\n        notify.error({\n          title: \"Failed to load quiz\",\n          description: err.message || \"Please try again later.\",\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchQuizData();\n  }, [quizId, user]);\n\n  const handleAnswerSelect = (questionId: string, answer: any) => {\n    setUserAnswers((prev) => ({ ...prev, [questionId]: answer }));\n  };\n\n  const handleSelectAllAnswerToggle = (\n    questionId: string,\n    optionText: string\n  ) => {\n    setUserAnswers((prev) => {\n      const currentAnswers = prev[questionId] || [];\n      const isSelected = currentAnswers.includes(optionText);\n\n      if (isSelected) {\n        return {\n          ...prev,\n          [questionId]: currentAnswers.filter(\n            (ans: string) => ans !== optionText\n          ),\n        };\n      } else {\n        return {\n          ...prev,\n          [questionId]: [...currentAnswers, optionText],\n        };\n      }\n    });\n  };\n\n  const goToNextQuestion = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex((prev) => prev + 1);\n    }\n  };\n\n  const goToPreviousQuestion = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex((prev) => prev - 1);\n    }\n  };\n\n  const checkAnswerCorrect = (\n    question: QuizQuestion,\n    userAnswer: any\n  ): boolean => {\n    if (!userAnswer) return false;\n\n    if (question.type === \"multiple_choice\" && question.options) {\n      const selectedOption = (question.options as any[])?.find(\n        (opt) => opt.text === userAnswer\n      );\n      return selectedOption?.is_correct || false;\n    } else if (question.type === \"select_all_that_apply\" && question.options) {\n      const userSelectedAnswers = userAnswer || [];\n      const correctOptions = (question.options as any[])?.filter(\n        (opt) => opt.is_correct\n      );\n      const correctOptionTexts = correctOptions.map((opt) => opt.text);\n\n      return (\n        correctOptionTexts.length === userSelectedAnswers.length &&\n        correctOptionTexts.every((text) => userSelectedAnswers.includes(text))\n      );\n    } else if (question.type === \"true_false\") {\n      return userAnswer === question.correct_answer;\n    } else if (question.type === \"short_answer\") {\n      return (\n        userAnswer?.toLowerCase().trim() ===\n        question.correct_answer?.toLowerCase().trim()\n      );\n    }\n    return false;\n  };\n  const handleSubmitAnswer = async () => {\n    const currentQuestion = questions[currentQuestionIndex];\n    if (\n      currentQuestion &&\n      userAnswers[currentQuestion.id] !== undefined &&\n      !submittedAnswers[currentQuestionIndex]\n    ) {\n      const isCorrect = checkAnswerCorrect(\n        currentQuestion,\n        userAnswers[currentQuestion.id]\n      );\n      const newSubmittedAnswers = [...submittedAnswers];\n      newSubmittedAnswers[currentQuestionIndex] = true;\n      setSubmittedAnswers(newSubmittedAnswers);\n\n      // SRS Update Logic\n      const difficulty = isCorrect\n        ? ReviewDifficulty.EASY\n        : ReviewDifficulty.DIFFICULT; // Simplified difficulty\n      const updatedQuestion = updateQuizQuestionSRS(\n        currentQuestion,\n        difficulty\n      );\n\n      // Persist SRS updates to the backend\n      try {\n        await QuizAPI.updateQuestionSRS(updatedQuestion.id, {\n          srs_level: updatedQuestion.srs_level || undefined,\n          due_at: updatedQuestion.due_at || undefined,\n          last_reviewed_at: updatedQuestion.last_reviewed_at || undefined,\n          srs_interval: updatedQuestion.srs_interval || undefined,\n          srs_ease_factor: updatedQuestion.srs_ease_factor || undefined,\n          srs_repetitions: updatedQuestion.srs_repetitions || undefined,\n          srs_correct_streak: updatedQuestion.srs_correct_streak || undefined,\n        });\n        console.log(\"SRS Updated successfully:\", updatedQuestion.id);\n      } catch (error) {\n        console.error(\"Failed to update SRS data:\", error);\n        notify.error({\n          title: \"SRS Update Failed\",\n          description: \"Failed to save spaced repetition data\",\n        });\n      }\n\n      // Auto-advance to next question if enabled\n      if (settings.autoAdvance) {\n        setTimeout(() => {\n          if (currentQuestionIndex < questions.length - 1) {\n            goToNextQuestion();\n          } else {\n            handleSubmitQuiz();\n          }\n        }, 1500);\n      }\n    }\n  };\n\n  const calculateScore = (): QuizResults => {\n    let score = 0;\n    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n\n    questions.forEach((q) => {\n      const userAnswer = userAnswers[q.id];\n      if (!userAnswer) return;\n\n      if (q.type === \"multiple_choice\" && q.options) {\n        const selectedOption = (q.options as any[])?.find(\n          (opt) => opt.text === userAnswer\n        );\n        if (selectedOption?.is_correct) {\n          score++;\n        }\n      } else if (q.type === \"select_all_that_apply\" && q.options) {\n        const userSelectedAnswers = userAnswer || [];\n        const correctOptions = (q.options as any[])?.filter(\n          (opt) => opt.is_correct\n        );\n        const correctOptionTexts = correctOptions.map((opt) => opt.text);\n\n        // Check if user selected exactly the correct answers (no more, no less)\n        const isCorrect =\n          correctOptionTexts.length === userSelectedAnswers.length &&\n          correctOptionTexts.every((text) =>\n            userSelectedAnswers.includes(text)\n          );\n\n        if (isCorrect) {\n          score++;\n        }\n      } else if (q.type === \"true_false\") {\n        if (userAnswer === q.correct_answer) {\n          score++;\n        }\n      } else if (q.type === \"short_answer\") {\n        if (\n          userAnswer?.toLowerCase().trim() ===\n          q.correct_answer?.toLowerCase().trim()\n        ) {\n          score++;\n        }\n      }\n    });\n\n    const percentage = Math.round((score / questions.length) * 100);\n\n    return {\n      score,\n      totalQuestions: questions.length,\n      percentage,\n      timeSpent,\n    };\n  };\n\n  const handleSubmitQuiz = () => {\n    const quizResults = calculateScore();\n    setResults(quizResults);\n    setIsCompleted(true);\n\n    if (quizResults.percentage >= 70) {\n      notify.success({\n        title: \"Great job!\",\n        description: `You scored ${quizResults.score}/${quizResults.totalQuestions} (${quizResults.percentage}%)`,\n      });\n    } else {\n      notify.info({\n        title: \"Quiz completed\",\n        description: `You scored ${quizResults.score}/${quizResults.totalQuestions} (${quizResults.percentage}%). Keep studying!`,\n      });\n    }\n\n    onQuizComplete?.(quizResults.score, quizResults.totalQuestions);\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n  };\n\n  const restartQuiz = () => {\n    setCurrentQuestionIndex(0);\n    setUserAnswers({});\n    setSubmittedAnswers(new Array(questions.length).fill(false));\n    setIsCompleted(false);\n    setResults(null);\n  };\n\n  // Keyboard navigation\n  useKeyboardNavigation({\n    onNext: () =>\n      !isCompleted &&\n      currentQuestionIndex < questions.length - 1 &&\n      goToNextQuestion(),\n    onPrevious: () =>\n      !isCompleted && currentQuestionIndex > 0 && goToPreviousQuestion(),\n    onSubmit: () =>\n      !isCompleted &&\n      currentQuestionIndex === questions.length - 1 &&\n      handleSubmitQuiz(),\n    onEscape: onExit,\n    disabled: loading || isCompleted,\n  });\n\n  if (loading) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-slate-300\">Loading quiz...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (error || !quiz) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n            {error ? \"Error Loading Quiz\" : \"Quiz Not Found\"}\n          </h3>\n          <p className=\"text-slate-400 mb-4\">\n            {error || \"The quiz you're looking for doesn't exist.\"}\n          </p>\n          <Button\n            onClick={onExit}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300\"\n          >\n            Go Back\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (questions.length === 0) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <AlertCircle className=\"h-12 w-12 text-yellow-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n            No Questions\n          </h3>\n          <p className=\"text-slate-400 mb-4\">\n            This quiz doesn't have any questions yet.\n          </p>\n          <Button\n            onClick={onExit}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300\"\n          >\n            Go Back\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (isCompleted && results) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <Trophy\n            className={`h-16 w-16 mx-auto mb-4 ${\n              results.percentage >= 70 ? \"text-yellow-400\" : \"text-slate-400\"\n            }`}\n          />\n\n          <h2 className=\"text-2xl font-semibold text-slate-200 mb-2\">\n            Quiz Complete!\n          </h2>\n          <p className=\"text-slate-400 mb-6\">Here are your results:</p>\n\n          <div className=\"grid grid-cols-2 gap-4 mb-6\">\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Score</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.score}/{results.totalQuestions}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Percentage</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.percentage}%\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Time</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {formatTime(results.timeSpent)}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Grade</p>\n              <Badge\n                variant={results.percentage >= 70 ? \"default\" : \"secondary\"}\n                className=\"text-lg px-3 py-1\"\n              >\n                {results.percentage >= 90\n                  ? \"A\"\n                  : results.percentage >= 80\n                  ? \"B\"\n                  : results.percentage >= 70\n                  ? \"C\"\n                  : \"D\"}\n              </Badge>\n            </div>\n          </div>\n\n          <div className=\"flex justify-center space-x-3\">\n            <Button\n              onClick={restartQuiz}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300\"\n            >\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              Retry\n            </Button>\n            <Button\n              onClick={onExit}\n              className=\"bg-purple-600 hover:bg-purple-700\"\n            >\n              Finish\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const dueQuestionsCount = getQuizQuestionsDueCount(questions);\n\n  return (\n    <div className=\"max-w-2xl mx-auto space-y-6\">\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">          <div className=\"flex justify-between items-center mb-4\">\n            <div>\n              <h2 className=\"text-xl font-semibold text-slate-200\">\n                {quiz.name}\n              </h2>\n              {quiz.description && (\n                <p className=\"text-slate-400 text-sm mt-1\">\n                  {quiz.description}\n                </p>\n              )}\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <QuizSettingsToggle size=\"sm\" />\n              <Button\n                onClick={onExit}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-slate-400 hover:text-slate-200\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-slate-400\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </span>\n              {dueQuestionsCount > 0 && (\n                <Badge\n                  variant=\"secondary\"\n                  className=\"text-xs bg-orange-900/30 text-orange-400 border-orange-700\"\n                >\n                  {dueQuestionsCount} due for review\n                </Badge>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-2 text-sm text-slate-400\">\n              <Clock className=\"h-4 w-4\" />\n              <span>\n                {formatTime(Math.round((Date.now() - startTime) / 1000))}\n              </span>\n            </div>\n          </div>\n\n          <Progress value={progress} className=\"mb-6\" />\n        </CardContent>\n      </Card>\n\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-slate-200 mb-4\">\n            {currentQuestion.question_text}\n          </h3>\n\n          <div className=\"space-y-3\">            {currentQuestion.type === \"multiple_choice\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  {(currentQuestion.options as any[]).map((option, index) => {\n                    const isSelected = userAnswers[currentQuestion.id] === option.text;\n                    const isSubmitted = submittedAnswers[currentQuestionIndex];\n                    \n                    // Determine border and background colors based on feedback settings\n                    let borderClass = \"border-slate-600\";\n                    let bgClass = \"\";\n                    \n                    if (isSelected) {\n                      if (isSubmitted && settings.instantFeedback) {\n                        // Show correct/incorrect feedback\n                        if (option.is_correct) {\n                          borderClass = \"border-green-500\";\n                          bgClass = \"bg-green-950/20\";\n                        } else {\n                          borderClass = \"border-red-500\";\n                          bgClass = \"bg-red-950/20\";\n                        }\n                      } else {\n                        // Default selection style\n                        borderClass = \"border-purple-500\";\n                        bgClass = \"bg-purple-950/20\";\n                      }\n                    } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct) {\n                      // Highlight correct answer even if not selected\n                      borderClass = \"border-green-500\";\n                      bgClass = \"bg-green-950/20\";\n                    }\n\n                    return (\n                      <label\n                        key={index}\n                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}\n                      >\n                        <input\n                          type=\"radio\"\n                          name={`question-${currentQuestion.id}`}\n                          value={option.text}\n                          checked={isSelected}\n                          onChange={() =>\n                            !isSubmitted && handleAnswerSelect(currentQuestion.id, option.text)\n                          }\n                          className=\"sr-only\"\n                          disabled={isSubmitted}\n                        />\n                        <div\n                          className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                            isSelected\n                              ? isSubmitted && settings.instantFeedback\n                                ? option.is_correct\n                                  ? \"border-green-500 bg-green-500\"\n                                  : \"border-red-500 bg-red-500\"\n                                : \"border-purple-500 bg-purple-500\"\n                              : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-slate-400\"\n                          }`}\n                        >\n                          {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct)) && (\n                            <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                          )}\n                        </div>\n                        <span className=\"text-slate-200\">{option.text}</span>\n                      </label>\n                    );\n                  })}\n                </div>\n              )}            {currentQuestion.type === \"select_all_that_apply\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  <p className=\"text-sm text-slate-400 mb-3 italic\">\n                    Select all correct answers:\n                  </p>\n                  {(currentQuestion.options as any[]).map((option, index) => {\n                    const isSelected = (\n                      userAnswers[currentQuestion.id] || []\n                    ).includes(option.text);\n                    const isSubmitted = submittedAnswers[currentQuestionIndex];\n                    \n                    // Determine border and background colors based on feedback settings\n                    let borderClass = \"border-slate-600\";\n                    let bgClass = \"\";\n                    \n                    if (isSelected) {\n                      if (isSubmitted && settings.instantFeedback) {\n                        // Show correct/incorrect feedback for selected options\n                        if (option.is_correct) {\n                          borderClass = \"border-green-500\";\n                          bgClass = \"bg-green-950/20\";\n                        } else {\n                          borderClass = \"border-red-500\";\n                          bgClass = \"bg-red-950/20\";\n                        }\n                      } else {\n                        // Default selection style\n                        borderClass = \"border-purple-500\";\n                        bgClass = \"bg-purple-950/20\";\n                      }\n                    } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct) {\n                      // Highlight unselected correct answers\n                      borderClass = \"border-green-500\";\n                      bgClass = \"bg-green-950/20\";\n                    }\n\n                    return (\n                      <label\n                        key={index}\n                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}\n                      >\n                        <input\n                          type=\"checkbox\"\n                          checked={isSelected}\n                          onChange={() =>\n                            !isSubmitted && handleSelectAllAnswerToggle(\n                              currentQuestion.id,\n                              option.text\n                            )\n                          }\n                          className=\"sr-only\"\n                          disabled={isSubmitted}\n                        />\n                        <div\n                          className={`w-4 h-4 border-2 mr-3 flex items-center justify-center rounded ${\n                            isSelected\n                              ? isSubmitted && settings.instantFeedback\n                                ? option.is_correct\n                                  ? \"border-green-500 bg-green-500\"\n                                  : \"border-red-500 bg-red-500\"\n                                : \"border-purple-500 bg-purple-500\"\n                              : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-slate-400\"\n                          }`}\n                        >\n                          {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct)) && (\n                            <div className=\"w-2 h-2 bg-white rounded-sm\"></div>\n                          )}\n                        </div>\n                        <span className=\"text-slate-200\">{option.text}</span>\n                      </label>\n                    );\n                  })}\n                </div>\n              )}            {currentQuestion.type === \"true_false\" && (\n              <div className=\"space-y-3\">\n                {[\"True\", \"False\"].map((val) => {\n                  const isSelected = userAnswers[currentQuestion.id] === val;\n                  const isSubmitted = submittedAnswers[currentQuestionIndex];\n                  \n                  // Determine styling based on feedback settings\n                  let borderClass = \"border-slate-600\";\n                  let bgClass = \"\";\n                  \n                  if (isSelected) {\n                    if (isSubmitted && settings.instantFeedback) {\n                      // Show correct/incorrect feedback\n                      if (val === currentQuestion.correct_answer) {\n                        borderClass = \"border-green-500\";\n                        bgClass = \"bg-green-950/20\";\n                      } else {\n                        borderClass = \"border-red-500\";\n                        bgClass = \"bg-red-950/20\";\n                      }\n                    } else {\n                      // Default selection style\n                      borderClass = \"border-purple-500\";\n                      bgClass = \"bg-purple-950/20\";\n                    }\n                  } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer) {\n                    // Highlight correct answer even if not selected\n                    borderClass = \"border-green-500\";\n                    bgClass = \"bg-green-950/20\";\n                  }\n\n                  return (\n                    <label\n                      key={val}\n                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}\n                    >\n                      <input\n                        type=\"radio\"\n                        name={`question-${currentQuestion.id}`}\n                        value={val}\n                        checked={isSelected}\n                        onChange={() =>\n                          !isSubmitted && handleAnswerSelect(currentQuestion.id, val)\n                        }\n                        className=\"sr-only\"\n                        disabled={isSubmitted}\n                      />\n                      <div\n                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                          isSelected\n                            ? isSubmitted && settings.instantFeedback\n                              ? val === currentQuestion.correct_answer\n                                ? \"border-green-500 bg-green-500\"\n                                : \"border-red-500 bg-red-500\"\n                              : \"border-purple-500 bg-purple-500\"\n                            : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer\n                            ? \"border-green-500 bg-green-500\"\n                            : \"border-slate-400\"\n                        }`}\n                      >\n                        {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer)) && (\n                          <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                        )}\n                      </div>\n                      <span className=\"text-slate-200\">{val}</span>\n                    </label>\n                  );\n                })}\n              </div>\n            )}            {currentQuestion.type === \"short_answer\" && (\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={userAnswers[currentQuestion.id] || \"\"}\n                  onChange={(e) =>\n                    !submittedAnswers[currentQuestionIndex] && handleAnswerSelect(currentQuestion.id, e.target.value)\n                  }\n                  className={`w-full px-4 py-3 border rounded-lg text-slate-200 placeholder-slate-400 focus:ring-1 outline-none ${\n                    submittedAnswers[currentQuestionIndex] && settings.instantFeedback\n                      ? userAnswers[currentQuestion.id]\n                          ?.toLowerCase()\n                          .trim() ===\n                        currentQuestion.correct_answer?.toLowerCase().trim()\n                        ? \"bg-green-950/20 border-green-500 focus:border-green-500 focus:ring-green-500\"\n                        : \"bg-red-950/20 border-red-500 focus:border-red-500 focus:ring-red-500\"\n                      : \"bg-slate-900 border-slate-600 focus:border-purple-500 focus:ring-purple-500\"\n                  }`}\n                  placeholder=\"Type your answer here...\"\n                  disabled={submittedAnswers[currentQuestionIndex]}\n                />\n                {submittedAnswers[currentQuestionIndex] && settings.instantFeedback && settings.showCorrectAnswers && (\n                  <p className=\"text-sm text-slate-400\">\n                    <span className=\"font-medium\">Correct answer:</span>{\" \"}\n                    {currentQuestion.correct_answer}\n                  </p>\n                )}              </div>\n            )}\n          </div>\n\n          {submittedAnswers[currentQuestionIndex] && settings.showExplanations && currentQuestion.explanation && (\n            <div className=\"mt-4 p-4 bg-slate-900 rounded-lg border border-slate-600\">\n              <h4 className=\"text-sm font-medium text-slate-300 mb-2\">\n                Explanation:\n              </h4>\n              <p className=\"text-slate-400 text-sm\">\n                {currentQuestion.explanation}\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      <div className=\"flex justify-between items-center\">\n        <Button\n          onClick={goToPreviousQuestion}\n          disabled={currentQuestionIndex === 0}\n          variant=\"outline\"\n          className=\"border-slate-600 text-slate-300 disabled:opacity-50\"\n        >\n          <ChevronLeft className=\"h-4 w-4 mr-2\" />\n          Previous\n        </Button>\n\n        <div className=\"flex gap-2\">\n          {!submittedAnswers[currentQuestionIndex] &&\n          userAnswers[currentQuestion.id] !== undefined ? (\n            <Button\n              onClick={handleSubmitAnswer}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n            >\n              <CheckCircle className=\"h-4 w-4 mr-2\" />\n              Submit Answer\n            </Button>\n          ) : submittedAnswers[currentQuestionIndex] ? (\n            currentQuestionIndex === questions.length - 1 ? (\n              <Button\n                onClick={handleSubmitQuiz}\n                className=\"bg-green-600 hover:bg-green-700 text-white\"\n              >\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Submit Quiz\n              </Button>\n            ) : (\n              <Button\n                onClick={goToNextQuestion}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Next\n                <ChevronRight className=\"h-4 w-4 ml-2\" />\n              </Button>\n            )\n          ) : null}\n        </div>\n      </div>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useEffect, useState } from \"react\";\n// Removed direct Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  ChevronLeft,\n  ChevronRight,\n  X,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Trophy,\n  RotateCcw,\n} from \"lucide-react\";\nimport { notify } from \"@/lib/notifications\";\nimport { useKeyboardNavigation } from \"@/hooks/useKeyboardNavigation\";\nimport { useQuizSettings } from \"@/contexts/QuizSettingsContext\";\nimport { QuizSettingsToggle } from \"./QuizSettingsToggle\";\nimport {\n  ReviewDifficulty,\n  updateQuizQuestionSRS,\n  sortQuizQuestionsByDueDate,\n  getQuizQuestionsDueCount,\n} from \"@/lib/srs\";\nimport { QuizAPI } from \"@/lib/api/quizApi\";\ntype Quiz = Tables<\"quizzes\">;\ntype QuizQuestion = Tables<\"quiz_questions\">;\n// Ensure McqOption type is defined if you use it for displaying options\n// interface McqOption { text: string; is_correct: boolean; id?: string; }\n\ninterface QuizPlayerProps {\n  quizId: string;\n  onQuizComplete?: (score: number, totalQuestions: number) => void; // Optional callback\n  onExit: () => void; // Callback to close the player\n}\n\ninterface QuizResults {\n  score: number;\n  totalQuestions: number;\n  percentage: number;\n  timeSpent: number;\n}\n\nexport const QuizPlayer: React.FC<QuizPlayerProps> = ({\n  quizId,\n  onQuizComplete,\n  onExit,\n}) => {\n  const { user } = useAuth();\n  const { settings } = useQuizSettings();\n  const [quiz, setQuiz] = useState<Quiz | null>(null);\n  const [questions, setQuestions] = useState<QuizQuestion[]>([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({}); // { questionId: answer }\n  const [submittedAnswers, setSubmittedAnswers] = useState<Array<boolean>>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const [results, setResults] = useState<QuizResults | null>(null);\n  const [startTime] = useState(Date.now());\n\n  useEffect(() => {\n    const fetchQuizData = async () => {\n      if (!user || !quizId) return;\n      setLoading(true);\n      setError(null);\n      try {\n        // Fetch quiz details\n        // Fetch quiz data from backend API\n        const token = localStorage.getItem('auth_token');\n        const quizResponse = await fetch(`/api/quizzes/${quizId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (!quizResponse.ok) {\n          throw new Error('Failed to fetch quiz data');\n        }\n\n        const quizData = await quizResponse.json();\n          .from(\"quizzes\")\n          .select(\"*\")\n          .eq(\"id\", quizId)\n          .eq(\"user_id\", user.id)\n          .single();\n        if (quizError) throw quizError;\n        setQuiz(quizData);\n\n        // Fetch quiz questions\n        const { data: questionsData, error: questionsError } = await supabase\n          .from(\"quiz_questions\")\n          .select(\"*\")\n          .eq(\"quiz_id\", quizId)\n          .eq(\"user_id\", user.id) // RLS should cover this, but good for explicitness\n          .order(\"created_at\", { ascending: true });\n        if (questionsError) throw questionsError;\n\n        // Sort questions by SRS due date for optimal spaced repetition\n        const sortedQuestions = sortQuizQuestionsByDueDate(questionsData || []);\n        setQuestions(sortedQuestions);\n        setSubmittedAnswers(new Array(sortedQuestions.length).fill(false));\n      } catch (err: any) {\n        console.error(\"Error fetching quiz data:\", err);\n        setError(err.message || \"Failed to load quiz.\");\n        notify.error({\n          title: \"Failed to load quiz\",\n          description: err.message || \"Please try again later.\",\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchQuizData();\n  }, [quizId, user]);\n\n  const handleAnswerSelect = (questionId: string, answer: any) => {\n    setUserAnswers((prev) => ({ ...prev, [questionId]: answer }));\n  };\n\n  const handleSelectAllAnswerToggle = (\n    questionId: string,\n    optionText: string\n  ) => {\n    setUserAnswers((prev) => {\n      const currentAnswers = prev[questionId] || [];\n      const isSelected = currentAnswers.includes(optionText);\n\n      if (isSelected) {\n        return {\n          ...prev,\n          [questionId]: currentAnswers.filter(\n            (ans: string) => ans !== optionText\n          ),\n        };\n      } else {\n        return {\n          ...prev,\n          [questionId]: [...currentAnswers, optionText],\n        };\n      }\n    });\n  };\n\n  const goToNextQuestion = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex((prev) => prev + 1);\n    }\n  };\n\n  const goToPreviousQuestion = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex((prev) => prev - 1);\n    }\n  };\n\n  const checkAnswerCorrect = (\n    question: QuizQuestion,\n    userAnswer: any\n  ): boolean => {\n    if (!userAnswer) return false;\n\n    if (question.type === \"multiple_choice\" && question.options) {\n      const selectedOption = (question.options as any[])?.find(\n        (opt) => opt.text === userAnswer\n      );\n      return selectedOption?.is_correct || false;\n    } else if (question.type === \"select_all_that_apply\" && question.options) {\n      const userSelectedAnswers = userAnswer || [];\n      const correctOptions = (question.options as any[])?.filter(\n        (opt) => opt.is_correct\n      );\n      const correctOptionTexts = correctOptions.map((opt) => opt.text);\n\n      return (\n        correctOptionTexts.length === userSelectedAnswers.length &&\n        correctOptionTexts.every((text) => userSelectedAnswers.includes(text))\n      );\n    } else if (question.type === \"true_false\") {\n      return userAnswer === question.correct_answer;\n    } else if (question.type === \"short_answer\") {\n      return (\n        userAnswer?.toLowerCase().trim() ===\n        question.correct_answer?.toLowerCase().trim()\n      );\n    }\n    return false;\n  };\n  const handleSubmitAnswer = async () => {\n    const currentQuestion = questions[currentQuestionIndex];\n    if (\n      currentQuestion &&\n      userAnswers[currentQuestion.id] !== undefined &&\n      !submittedAnswers[currentQuestionIndex]\n    ) {\n      const isCorrect = checkAnswerCorrect(\n        currentQuestion,\n        userAnswers[currentQuestion.id]\n      );\n      const newSubmittedAnswers = [...submittedAnswers];\n      newSubmittedAnswers[currentQuestionIndex] = true;\n      setSubmittedAnswers(newSubmittedAnswers);\n\n      // SRS Update Logic\n      const difficulty = isCorrect\n        ? ReviewDifficulty.EASY\n        : ReviewDifficulty.DIFFICULT; // Simplified difficulty\n      const updatedQuestion = updateQuizQuestionSRS(\n        currentQuestion,\n        difficulty\n      );\n\n      // Persist SRS updates to the backend\n      try {\n        await QuizAPI.updateQuestionSRS(updatedQuestion.id, {\n          srs_level: updatedQuestion.srs_level || undefined,\n          due_at: updatedQuestion.due_at || undefined,\n          last_reviewed_at: updatedQuestion.last_reviewed_at || undefined,\n          srs_interval: updatedQuestion.srs_interval || undefined,\n          srs_ease_factor: updatedQuestion.srs_ease_factor || undefined,\n          srs_repetitions: updatedQuestion.srs_repetitions || undefined,\n          srs_correct_streak: updatedQuestion.srs_correct_streak || undefined,\n        });\n        console.log(\"SRS Updated successfully:\", updatedQuestion.id);\n      } catch (error) {\n        console.error(\"Failed to update SRS data:\", error);\n        notify.error({\n          title: \"SRS Update Failed\",\n          description: \"Failed to save spaced repetition data\",\n        });\n      }\n\n      // Auto-advance to next question if enabled\n      if (settings.autoAdvance) {\n        setTimeout(() => {\n          if (currentQuestionIndex < questions.length - 1) {\n            goToNextQuestion();\n          } else {\n            handleSubmitQuiz();\n          }\n        }, 1500);\n      }\n    }\n  };\n\n  const calculateScore = (): QuizResults => {\n    let score = 0;\n    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n\n    questions.forEach((q) => {\n      const userAnswer = userAnswers[q.id];\n      if (!userAnswer) return;\n\n      if (q.type === \"multiple_choice\" && q.options) {\n        const selectedOption = (q.options as any[])?.find(\n          (opt) => opt.text === userAnswer\n        );\n        if (selectedOption?.is_correct) {\n          score++;\n        }\n      } else if (q.type === \"select_all_that_apply\" && q.options) {\n        const userSelectedAnswers = userAnswer || [];\n        const correctOptions = (q.options as any[])?.filter(\n          (opt) => opt.is_correct\n        );\n        const correctOptionTexts = correctOptions.map((opt) => opt.text);\n\n        // Check if user selected exactly the correct answers (no more, no less)\n        const isCorrect =\n          correctOptionTexts.length === userSelectedAnswers.length &&\n          correctOptionTexts.every((text) =>\n            userSelectedAnswers.includes(text)\n          );\n\n        if (isCorrect) {\n          score++;\n        }\n      } else if (q.type === \"true_false\") {\n        if (userAnswer === q.correct_answer) {\n          score++;\n        }\n      } else if (q.type === \"short_answer\") {\n        if (\n          userAnswer?.toLowerCase().trim() ===\n          q.correct_answer?.toLowerCase().trim()\n        ) {\n          score++;\n        }\n      }\n    });\n\n    const percentage = Math.round((score / questions.length) * 100);\n\n    return {\n      score,\n      totalQuestions: questions.length,\n      percentage,\n      timeSpent,\n    };\n  };\n\n  const handleSubmitQuiz = () => {\n    const quizResults = calculateScore();\n    setResults(quizResults);\n    setIsCompleted(true);\n\n    if (quizResults.percentage >= 70) {\n      notify.success({\n        title: \"Great job!\",\n        description: `You scored ${quizResults.score}/${quizResults.totalQuestions} (${quizResults.percentage}%)`,\n      });\n    } else {\n      notify.info({\n        title: \"Quiz completed\",\n        description: `You scored ${quizResults.score}/${quizResults.totalQuestions} (${quizResults.percentage}%). Keep studying!`,\n      });\n    }\n\n    onQuizComplete?.(quizResults.score, quizResults.totalQuestions);\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n  };\n\n  const restartQuiz = () => {\n    setCurrentQuestionIndex(0);\n    setUserAnswers({});\n    setSubmittedAnswers(new Array(questions.length).fill(false));\n    setIsCompleted(false);\n    setResults(null);\n  };\n\n  // Keyboard navigation\n  useKeyboardNavigation({\n    onNext: () =>\n      !isCompleted &&\n      currentQuestionIndex < questions.length - 1 &&\n      goToNextQuestion(),\n    onPrevious: () =>\n      !isCompleted && currentQuestionIndex > 0 && goToPreviousQuestion(),\n    onSubmit: () =>\n      !isCompleted &&\n      currentQuestionIndex === questions.length - 1 &&\n      handleSubmitQuiz(),\n    onEscape: onExit,\n    disabled: loading || isCompleted,\n  });\n\n  if (loading) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4\"></div>\n          <p className=\"text-slate-300\">Loading quiz...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (error || !quiz) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n            {error ? \"Error Loading Quiz\" : \"Quiz Not Found\"}\n          </h3>\n          <p className=\"text-slate-400 mb-4\">\n            {error || \"The quiz you're looking for doesn't exist.\"}\n          </p>\n          <Button\n            onClick={onExit}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300\"\n          >\n            Go Back\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (questions.length === 0) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <AlertCircle className=\"h-12 w-12 text-yellow-400 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium text-slate-200 mb-2\">\n            No Questions\n          </h3>\n          <p className=\"text-slate-400 mb-4\">\n            This quiz doesn't have any questions yet.\n          </p>\n          <Button\n            onClick={onExit}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300\"\n          >\n            Go Back\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (isCompleted && results) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700 max-w-2xl mx-auto\">\n        <CardContent className=\"p-8 text-center\">\n          <Trophy\n            className={`h-16 w-16 mx-auto mb-4 ${\n              results.percentage >= 70 ? \"text-yellow-400\" : \"text-slate-400\"\n            }`}\n          />\n\n          <h2 className=\"text-2xl font-semibold text-slate-200 mb-2\">\n            Quiz Complete!\n          </h2>\n          <p className=\"text-slate-400 mb-6\">Here are your results:</p>\n\n          <div className=\"grid grid-cols-2 gap-4 mb-6\">\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Score</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.score}/{results.totalQuestions}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Percentage</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.percentage}%\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Time</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {formatTime(results.timeSpent)}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg\">\n              <p className=\"text-slate-400 text-sm\">Grade</p>\n              <Badge\n                variant={results.percentage >= 70 ? \"default\" : \"secondary\"}\n                className=\"text-lg px-3 py-1\"\n              >\n                {results.percentage >= 90\n                  ? \"A\"\n                  : results.percentage >= 80\n                  ? \"B\"\n                  : results.percentage >= 70\n                  ? \"C\"\n                  : \"D\"}\n              </Badge>\n            </div>\n          </div>\n\n          <div className=\"flex justify-center space-x-3\">\n            <Button\n              onClick={restartQuiz}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300\"\n            >\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              Retry\n            </Button>\n            <Button\n              onClick={onExit}\n              className=\"bg-purple-600 hover:bg-purple-700\"\n            >\n              Finish\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const dueQuestionsCount = getQuizQuestionsDueCount(questions);\n\n  return (\n    <div className=\"max-w-2xl mx-auto space-y-6\">\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">          <div className=\"flex justify-between items-center mb-4\">\n            <div>\n              <h2 className=\"text-xl font-semibold text-slate-200\">\n                {quiz.name}\n              </h2>\n              {quiz.description && (\n                <p className=\"text-slate-400 text-sm mt-1\">\n                  {quiz.description}\n                </p>\n              )}\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <QuizSettingsToggle size=\"sm\" />\n              <Button\n                onClick={onExit}\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-slate-400 hover:text-slate-200\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-slate-400\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </span>\n              {dueQuestionsCount > 0 && (\n                <Badge\n                  variant=\"secondary\"\n                  className=\"text-xs bg-orange-900/30 text-orange-400 border-orange-700\"\n                >\n                  {dueQuestionsCount} due for review\n                </Badge>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-2 text-sm text-slate-400\">\n              <Clock className=\"h-4 w-4\" />\n              <span>\n                {formatTime(Math.round((Date.now() - startTime) / 1000))}\n              </span>\n            </div>\n          </div>\n\n          <Progress value={progress} className=\"mb-6\" />\n        </CardContent>\n      </Card>\n\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-slate-200 mb-4\">\n            {currentQuestion.question_text}\n          </h3>\n\n          <div className=\"space-y-3\">            {currentQuestion.type === \"multiple_choice\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  {(currentQuestion.options as any[]).map((option, index) => {\n                    const isSelected = userAnswers[currentQuestion.id] === option.text;\n                    const isSubmitted = submittedAnswers[currentQuestionIndex];\n                    \n                    // Determine border and background colors based on feedback settings\n                    let borderClass = \"border-slate-600\";\n                    let bgClass = \"\";\n                    \n                    if (isSelected) {\n                      if (isSubmitted && settings.instantFeedback) {\n                        // Show correct/incorrect feedback\n                        if (option.is_correct) {\n                          borderClass = \"border-green-500\";\n                          bgClass = \"bg-green-950/20\";\n                        } else {\n                          borderClass = \"border-red-500\";\n                          bgClass = \"bg-red-950/20\";\n                        }\n                      } else {\n                        // Default selection style\n                        borderClass = \"border-purple-500\";\n                        bgClass = \"bg-purple-950/20\";\n                      }\n                    } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct) {\n                      // Highlight correct answer even if not selected\n                      borderClass = \"border-green-500\";\n                      bgClass = \"bg-green-950/20\";\n                    }\n\n                    return (\n                      <label\n                        key={index}\n                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}\n                      >\n                        <input\n                          type=\"radio\"\n                          name={`question-${currentQuestion.id}`}\n                          value={option.text}\n                          checked={isSelected}\n                          onChange={() =>\n                            !isSubmitted && handleAnswerSelect(currentQuestion.id, option.text)\n                          }\n                          className=\"sr-only\"\n                          disabled={isSubmitted}\n                        />\n                        <div\n                          className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                            isSelected\n                              ? isSubmitted && settings.instantFeedback\n                                ? option.is_correct\n                                  ? \"border-green-500 bg-green-500\"\n                                  : \"border-red-500 bg-red-500\"\n                                : \"border-purple-500 bg-purple-500\"\n                              : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-slate-400\"\n                          }`}\n                        >\n                          {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct)) && (\n                            <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                          )}\n                        </div>\n                        <span className=\"text-slate-200\">{option.text}</span>\n                      </label>\n                    );\n                  })}\n                </div>\n              )}            {currentQuestion.type === \"select_all_that_apply\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  <p className=\"text-sm text-slate-400 mb-3 italic\">\n                    Select all correct answers:\n                  </p>\n                  {(currentQuestion.options as any[]).map((option, index) => {\n                    const isSelected = (\n                      userAnswers[currentQuestion.id] || []\n                    ).includes(option.text);\n                    const isSubmitted = submittedAnswers[currentQuestionIndex];\n                    \n                    // Determine border and background colors based on feedback settings\n                    let borderClass = \"border-slate-600\";\n                    let bgClass = \"\";\n                    \n                    if (isSelected) {\n                      if (isSubmitted && settings.instantFeedback) {\n                        // Show correct/incorrect feedback for selected options\n                        if (option.is_correct) {\n                          borderClass = \"border-green-500\";\n                          bgClass = \"bg-green-950/20\";\n                        } else {\n                          borderClass = \"border-red-500\";\n                          bgClass = \"bg-red-950/20\";\n                        }\n                      } else {\n                        // Default selection style\n                        borderClass = \"border-purple-500\";\n                        bgClass = \"bg-purple-950/20\";\n                      }\n                    } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct) {\n                      // Highlight unselected correct answers\n                      borderClass = \"border-green-500\";\n                      bgClass = \"bg-green-950/20\";\n                    }\n\n                    return (\n                      <label\n                        key={index}\n                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}\n                      >\n                        <input\n                          type=\"checkbox\"\n                          checked={isSelected}\n                          onChange={() =>\n                            !isSubmitted && handleSelectAllAnswerToggle(\n                              currentQuestion.id,\n                              option.text\n                            )\n                          }\n                          className=\"sr-only\"\n                          disabled={isSubmitted}\n                        />\n                        <div\n                          className={`w-4 h-4 border-2 mr-3 flex items-center justify-center rounded ${\n                            isSelected\n                              ? isSubmitted && settings.instantFeedback\n                                ? option.is_correct\n                                  ? \"border-green-500 bg-green-500\"\n                                  : \"border-red-500 bg-red-500\"\n                                : \"border-purple-500 bg-purple-500\"\n                              : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-slate-400\"\n                          }`}\n                        >\n                          {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct)) && (\n                            <div className=\"w-2 h-2 bg-white rounded-sm\"></div>\n                          )}\n                        </div>\n                        <span className=\"text-slate-200\">{option.text}</span>\n                      </label>\n                    );\n                  })}\n                </div>\n              )}            {currentQuestion.type === \"true_false\" && (\n              <div className=\"space-y-3\">\n                {[\"True\", \"False\"].map((val) => {\n                  const isSelected = userAnswers[currentQuestion.id] === val;\n                  const isSubmitted = submittedAnswers[currentQuestionIndex];\n                  \n                  // Determine styling based on feedback settings\n                  let borderClass = \"border-slate-600\";\n                  let bgClass = \"\";\n                  \n                  if (isSelected) {\n                    if (isSubmitted && settings.instantFeedback) {\n                      // Show correct/incorrect feedback\n                      if (val === currentQuestion.correct_answer) {\n                        borderClass = \"border-green-500\";\n                        bgClass = \"bg-green-950/20\";\n                      } else {\n                        borderClass = \"border-red-500\";\n                        bgClass = \"bg-red-950/20\";\n                      }\n                    } else {\n                      // Default selection style\n                      borderClass = \"border-purple-500\";\n                      bgClass = \"bg-purple-950/20\";\n                    }\n                  } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer) {\n                    // Highlight correct answer even if not selected\n                    borderClass = \"border-green-500\";\n                    bgClass = \"bg-green-950/20\";\n                  }\n\n                  return (\n                    <label\n                      key={val}\n                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}\n                    >\n                      <input\n                        type=\"radio\"\n                        name={`question-${currentQuestion.id}`}\n                        value={val}\n                        checked={isSelected}\n                        onChange={() =>\n                          !isSubmitted && handleAnswerSelect(currentQuestion.id, val)\n                        }\n                        className=\"sr-only\"\n                        disabled={isSubmitted}\n                      />\n                      <div\n                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                          isSelected\n                            ? isSubmitted && settings.instantFeedback\n                              ? val === currentQuestion.correct_answer\n                                ? \"border-green-500 bg-green-500\"\n                                : \"border-red-500 bg-red-500\"\n                              : \"border-purple-500 bg-purple-500\"\n                            : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer\n                            ? \"border-green-500 bg-green-500\"\n                            : \"border-slate-400\"\n                        }`}\n                      >\n                        {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer)) && (\n                          <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                        )}\n                      </div>\n                      <span className=\"text-slate-200\">{val}</span>\n                    </label>\n                  );\n                })}\n              </div>\n            )}            {currentQuestion.type === \"short_answer\" && (\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={userAnswers[currentQuestion.id] || \"\"}\n                  onChange={(e) =>\n                    !submittedAnswers[currentQuestionIndex] && handleAnswerSelect(currentQuestion.id, e.target.value)\n                  }\n                  className={`w-full px-4 py-3 border rounded-lg text-slate-200 placeholder-slate-400 focus:ring-1 outline-none ${\n                    submittedAnswers[currentQuestionIndex] && settings.instantFeedback\n                      ? userAnswers[currentQuestion.id]\n                          ?.toLowerCase()\n                          .trim() ===\n                        currentQuestion.correct_answer?.toLowerCase().trim()\n                        ? \"bg-green-950/20 border-green-500 focus:border-green-500 focus:ring-green-500\"\n                        : \"bg-red-950/20 border-red-500 focus:border-red-500 focus:ring-red-500\"\n                      : \"bg-slate-900 border-slate-600 focus:border-purple-500 focus:ring-purple-500\"\n                  }`}\n                  placeholder=\"Type your answer here...\"\n                  disabled={submittedAnswers[currentQuestionIndex]}\n                />\n                {submittedAnswers[currentQuestionIndex] && settings.instantFeedback && settings.showCorrectAnswers && (\n                  <p className=\"text-sm text-slate-400\">\n                    <span className=\"font-medium\">Correct answer:</span>{\" \"}\n                    {currentQuestion.correct_answer}\n                  </p>\n                )}              </div>\n            )}\n          </div>\n\n          {submittedAnswers[currentQuestionIndex] && settings.showExplanations && currentQuestion.explanation && (\n            <div className=\"mt-4 p-4 bg-slate-900 rounded-lg border border-slate-600\">\n              <h4 className=\"text-sm font-medium text-slate-300 mb-2\">\n                Explanation:\n              </h4>\n              <p className=\"text-slate-400 text-sm\">\n                {currentQuestion.explanation}\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      <div className=\"flex justify-between items-center\">\n        <Button\n          onClick={goToPreviousQuestion}\n          disabled={currentQuestionIndex === 0}\n          variant=\"outline\"\n          className=\"border-slate-600 text-slate-300 disabled:opacity-50\"\n        >\n          <ChevronLeft className=\"h-4 w-4 mr-2\" />\n          Previous\n        </Button>\n\n        <div className=\"flex gap-2\">\n          {!submittedAnswers[currentQuestionIndex] &&\n          userAnswers[currentQuestion.id] !== undefined ? (\n            <Button\n              onClick={handleSubmitAnswer}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n            >\n              <CheckCircle className=\"h-4 w-4 mr-2\" />\n              Submit Answer\n            </Button>\n          ) : submittedAnswers[currentQuestionIndex] ? (\n            currentQuestionIndex === questions.length - 1 ? (\n              <Button\n                onClick={handleSubmitQuiz}\n                className=\"bg-green-600 hover:bg-green-700 text-white\"\n              >\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Submit Quiz\n              </Button>\n            ) : (\n              <Button\n                onClick={goToNextQuestion}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Next\n                <ChevronRight className=\"h-4 w-4 ml-2\" />\n              </Button>\n            )\n          ) : null}\n        </div>\n      </div>\n    </div>\n  );\n};\n"}