{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardSetRoutes.ts"}, "originalCode": "import { Hono, Context, Next } from \"hono\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { supabaseMiddleware } from \"../middleware/supabaseMiddleware\";\nimport {\n  UserCompletionInsert,\n  FlashcardSetCompletionData,\n  CompletionStats,\n  CompletionFilters,\n} from \"../../shared/types/completion\";\n\nconsole.log(\"flashcardSetRoutes.ts: Module loaded\");\n\ntype AppVariables = {\n  supabase: SupabaseClient;\n  user: { id: string };\n};\n\nconst flashcardSetRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply the Supabase middleware to all routes\nflashcardSetRoutes.use(\"*\", supabaseMiddleware);\n\n// Application-level error handler for flashcardSetRoutes\nflashcardSetRoutes.onError((err, c) => {\n  console.error(\"Error in flashcardSetRoutes:\", err);\n  return c.json(\n    {\n      error: \"An unexpected error occurred in flashcard set routes.\",\n      message: err.message,\n    },\n    500\n  );\n});\n\n// Middleware to ensure user is authenticated\nconst authMiddleware = async (\n  c: Context<{ Variables: AppVariables }>,\n  next: Next\n) => {\n  console.log(`flashcardSetRoutes: Auth middleware triggered for path: ${c.req.path}`);\n  const authHeader = c.req.header(\"Authorization\");\n\n  if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n    console.error(\"flashcardSetRoutes: Auth Error - Authorization header missing or malformed.\");\n    return c.json({ error: \"Unauthorized: Missing or malformed token\" }, 401);\n  }\n\n  const token = authHeader.split(\" \")[1];\n  if (!token) {\n    console.error(\"flashcardSetRoutes: Auth Error - Token missing after Bearer split.\");\n    return c.json({ error: \"Unauthorized: Missing token\" }, 401);\n  }\n\n  const supabase = c.get(\"supabase\");\n  if (!supabase) {\n    console.error(\"flashcardSetRoutes: Auth Error - Supabase client not found in request context.\");\n    return c.json(\n      { error: \"Server configuration error: Supabase client missing\" },\n      500\n    );\n  }\n\n  console.log(\"flashcardSetRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).\");\n  try {\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      console.error(\"flashcardSetRoutes: Auth Error - getUser failed:\", getUserError.message);\n      return c.json(\n        { error: \"Unauthorized: Invalid token\", details: getUserError.message },\n        401\n      );\n    }\n\n    const user = data?.user;\n    if (!user) {\n      console.error(\"flashcardSetRoutes: Auth Error - No user found for token\");\n      return c.json({ error: \"Unauthorized: No user found for token\" }, 401);\n    }\n\n    console.log(`flashcardSetRoutes: Auth Success - User ${user.id} authenticated. Calling next().`);\n    c.set(\"user\", user);\n    await next();\n  } catch (err: any) {\n    console.error(\"flashcardSetRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:\", err.message, err.stack);\n    return c.json(\n      { error: \"Internal server error during authentication processing\" },\n      500\n    );\n  }\n};\n\nflashcardSetRoutes.use(\"*\", authMiddleware);\n\n// Route to create a new flashcard set\nflashcardSetRoutes.post(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n\n  try {\n    const body = await c.req.json();\n    const { name, description, study_document_id, flashcards } = body;\n\n    if (!name) {\n      return c.json({ error: \"Flashcard set name is required\" }, 400);\n    }\n\n    // Create the flashcard set\n    const { data: flashcardSet, error: setError } = await supabase\n      .from(\"flashcard_sets\")\n      .insert({\n        user_id: user.id,\n        name: name,\n        description: description || null,\n        study_document_id: study_document_id || null,\n      })\n      .select(\"id, name, description, study_document_id, created_at\")\n      .single();\n\n    if (setError) {\n      console.error(\"Error creating flashcard set:\", setError);\n      return c.json(\n        { error: \"Failed to create flashcard set\", details: setError.message },\n        500\n      );\n    }\n\n    if (!flashcardSet) {\n      return c.json({ error: \"No flashcard set returned from database\" }, 500);\n    }\n\n    // If flashcards are provided, insert them\n    if (flashcards && Array.isArray(flashcards) && flashcards.length > 0) {\n      const flashcardsToInsert = flashcards.map((card: any) => ({\n        set_id: flashcardSet.id,\n        user_id: user.id,\n        front_text: card.front_text || card.question || \"\",\n        back_text: card.back_text || card.answer || \"\",\n      }));\n\n      const { error: cardsError } = await supabase\n        .from(\"flashcards\")\n        .insert(flashcardsToInsert);\n\n      if (cardsError) {\n        console.error(\"Error creating flashcards:\", cardsError);\n        // Don't fail the whole operation, just log the error\n        console.warn(\"Flashcard set created but flashcards failed to insert\");\n      }\n    }\n\n    return c.json(flashcardSet, 201);\n  } catch (error: any) {\n    console.error(\"Error in flashcard set creation:\", error);\n    return c.json({ error: error.message || \"Failed to create flashcard set\" }, 500);\n  }\n});\n\n// Route to track flashcard set completion\nflashcardSetRoutes.post(\n  \"/:setId/complete\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const setId = c.req.param(\"setId\");\n\n    if (!setId) {\n      return c.json({ error: \"Flashcard set ID is required\" }, 400);\n    }\n\n    try {\n      const body = await c.req.json();\n      const completionData: FlashcardSetCompletionData = body;\n\n      // Validate required fields\n      if (!completionData.time_spent_minutes) {\n        return c.json({ error: \"Time spent is required\" }, 400);\n      }\n\n      // Verify the flashcard set exists (we don't need to check ownership since users can review any public sets)\n      const { data: flashcardSet, error: setError } = await supabase\n        .from(\"flashcard_sets\")\n        .select(\"id, user_id\")\n        .eq(\"id\", setId)\n        .single();\n\n      if (setError || !flashcardSet) {\n        return c.json({ error: \"Flashcard set not found\" }, 404);\n      }\n\n      // Create completion record\n      const completionInsert: UserCompletionInsert = {\n        user_id: user.id,\n        flashcard_set_id: setId,\n        completion_type: \"flashcard_set\",\n        completed_at: new Date().toISOString(),\n        time_spent_minutes: completionData.time_spent_minutes,\n        metadata: completionData.metadata || null,\n      };\n\n      const { data: completion, error: insertError } = await supabase\n        .from(\"user_completions\")\n        .insert(completionInsert)\n        .select()\n        .single();\n\n      if (insertError) {\n        console.error(\"Error recording flashcard set completion:\", insertError);\n        return c.json(\n          { error: \"Failed to record completion\", details: insertError.message },\n          500\n        );\n      }\n\n      return c.json(completion, 201);\n    } catch (error: any) {\n      console.error(\"Error in flashcard set completion endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get all flashcard sets for the user\nflashcardSetRoutes.get(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  console.log(\"flashcardSetRoutes: GET / handler triggered\");\n  try {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    if (!supabase) {\n      console.error(\"GET /api/flashcard-sets/ handler: Supabase client not found in context.\");\n      return c.json(\n        {\n          error: \"Internal Server Configuration Error: Supabase client not available.\",\n        },\n        500\n      );\n    }\n    if (!user || !user.id) {\n      console.error(\"GET /api/flashcard-sets/ handler: User not found in context or user ID missing.\");\n      return c.json(\n        {\n          error: \"Authentication Error: User information not available.\",\n          details: \"User context is invalid or missing.\",\n        },\n        500\n      );\n    }\n\n    console.log(`Fetching flashcard sets for user: ${user.id}`);\n\n    // Get flashcard sets with card counts using a join query\n    const { data: flashcardSets, error } = await supabase\n      .from(\"flashcard_sets\")\n      .select(`\n        *,\n        flashcards(count)\n      `)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: false });\n\n    if (error) {\n      console.error(\"Error fetching flashcard sets:\", error);\n      return c.json(\n        { error: \"Failed to fetch flashcard sets\", details: error.message },\n        500\n      );\n    }\n\n    // Transform the data to include card_count\n    const setsWithCounts = (flashcardSets || []).map((set: any) => ({\n      ...set,\n      card_count: set.flashcards?.[0]?.count || 0,\n      flashcards: undefined, // Remove the nested flashcards array\n    }));\n\n    console.log(`Found ${setsWithCounts.length} flashcard sets for user ${user.id}`);\n    return c.json(setsWithCounts);\n  } catch (error: any) {\n    console.error(\"Error in flashcard sets fetch:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard sets\" }, 500);\n  }\n});\n\n// Route to get a specific flashcard set with its flashcards\nflashcardSetRoutes.get(\"/:setId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const setId = c.req.param(\"setId\");\n\n  if (!setId) {\n    return c.json({ error: \"Invalid flashcard set ID\" }, 400);\n  }\n\n  try {\n    // First get the flashcard set basic info with user ownership check\n    const { data: flashcardSet, error: setError } = await supabase\n      .from(\"flashcard_sets\")\n      .select(\"*\")\n      .eq(\"id\", setId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (setError) {\n      console.error(\"Error fetching flashcard set:\", setError);\n      if (setError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to fetch flashcard set\", details: setError.message },\n        500\n      );\n    }\n\n    if (!flashcardSet) {\n      return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n    }\n\n    // Get all flashcards for this set (with user ownership check)\n    const { data: flashcards, error: flashcardsError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"set_id\", setId)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: true });\n\n    if (flashcardsError) {\n      console.error(\"Error fetching flashcards:\", flashcardsError);\n      return c.json(\n        { error: \"Failed to fetch flashcards\", details: flashcardsError.message },\n        500\n      );\n    }\n\n    return c.json({\n      ...flashcardSet,\n      flashcards: flashcards || [],\n    });\n  } catch (error: any) {\n    console.error(\"Error fetching flashcard set with flashcards:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard set\" }, 500);\n  }\n});\n\n// Route to delete a flashcard set\nflashcardSetRoutes.delete(\"/:setId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const setId = c.req.param(\"setId\");\n\n  if (!setId) {\n    return c.json({ error: \"Invalid flashcard set ID\" }, 400);\n  }\n\n  try {\n    // First verify the flashcard set exists and user owns it\n    const { data: flashcardSet, error: setError } = await supabase\n      .from(\"flashcard_sets\")\n      .select(\"id\")\n      .eq(\"id\", setId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (setError) {\n      console.error(\"Error checking flashcard set ownership:\", setError);\n      if (setError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard set\", details: setError.message },\n        500\n      );\n    }\n\n    if (!flashcardSet) {\n      return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n    }\n\n    // Delete all flashcards in the set first\n    const { error: flashcardsError } = await supabase\n      .from(\"flashcards\")\n      .delete()\n      .eq(\"set_id\", setId)\n      .eq(\"user_id\", user.id);\n\n    if (flashcardsError) {\n      console.error(\"Error deleting flashcards:\", flashcardsError);\n      return c.json(\n        { error: \"Failed to delete flashcards\", details: flashcardsError.message },\n        500\n      );\n    }\n\n    // Delete the flashcard set\n    const { error: deleteError } = await supabase\n      .from(\"flashcard_sets\")\n      .delete()\n      .eq(\"id\", setId)\n      .eq(\"user_id\", user.id);\n\n    if (deleteError) {\n      console.error(\"Error deleting flashcard set:\", deleteError);\n      return c.json(\n        { error: \"Failed to delete flashcard set\", details: deleteError.message },\n        500\n      );\n    }\n\n    return c.json({ message: \"Flashcard set deleted successfully\" }, 200);\n  } catch (error: any) {\n    console.error(\"Error deleting flashcard set:\", error);\n    return c.json({ error: error.message || \"Failed to delete flashcard set\" }, 500);\n  }\n});\n\n// Route to get due flashcards for SRS review\nflashcardSetRoutes.get(\"/:setId/due\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const setId = c.req.param(\"setId\");\n\n  if (!setId) {\n    return c.json({ error: \"Invalid flashcard set ID\" }, 400);\n  }\n\n  try {\n    // Since the current flashcards table doesn't have SRS fields like due_at,\n    // we'll return all flashcards for the set. In the future, we can add SRS fields\n    // or implement a separate review tracking system.\n    const { data: flashcards, error: flashcardsError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"set_id\", setId)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: true });\n\n    if (flashcardsError) {\n      console.error(\"Error fetching flashcards:\", flashcardsError);\n      return c.json(\n        { error: \"Failed to fetch flashcards\", details: flashcardsError.message },\n        500\n      );\n    }\n\n    // For now, return all flashcards as \"due\" since we don't have SRS tracking yet\n    return c.json(flashcards || []);\n  } catch (error: any) {\n    console.error(\"Error fetching due flashcards:\", error);\n    return c.json({ error: error.message || \"Failed to fetch due flashcards\" }, 500);\n  }\n});\n\n// Route to get completion statistics for flashcard sets\nflashcardSetRoutes.get(\n  \"/stats/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      // Get all user completions for flashcard sets\n      const { data: completions, error: completionsError } = await supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .eq(\"completion_type\", \"flashcard_set\")\n        .order(\"completed_at\", { ascending: false });\n\n      if (completionsError) {\n        console.error(\"Error fetching flashcard set completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      const allCompletions = completions || [];\n\n      // Calculate statistics specific to flashcard sets\n      const stats = {\n        total_completions: allCompletions.length,\n        total_study_time_minutes: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0),\n        recent_completions: allCompletions.slice(0, 10),\n        completion_streak: calculateCompletionStreak(allCompletions),\n        this_week_completions: getCompletionsInTimeRange(allCompletions, 7),\n        this_month_completions: getCompletionsInTimeRange(allCompletions, 30),\n        average_cards_reviewed: allCompletions.reduce((sum, c) => {\n          const cardsReviewed = c.metadata?.cards_reviewed || 0;\n          return sum + cardsReviewed;\n        }, 0) / (allCompletions.length || 1),\n        average_session_time: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0) / (allCompletions.length || 1),\n      };\n\n      return c.json(stats);\n    } catch (error: any) {\n      console.error(\"Error fetching flashcard set completion stats:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get filtered completion history for flashcard sets\nflashcardSetRoutes.get(\n  \"/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      const queryParams = c.req.query();\n      const filters: CompletionFilters = {\n        completion_type: \"flashcard_set\", // Fixed to flashcard_set\n        flashcard_set_id: queryParams.flashcard_set_id,\n        limit: queryParams.limit ? parseInt(queryParams.limit) : 50,\n        offset: queryParams.offset ? parseInt(queryParams.offset) : 0,\n      };\n\n      if (queryParams.start_date && queryParams.end_date) {\n        filters.date_range = {\n          start: queryParams.start_date,\n          end: queryParams.end_date,\n        };\n      }\n\n      // Build query\n      let query = supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .eq(\"completion_type\", \"flashcard_set\");\n\n      if (filters.flashcard_set_id) {\n        query = query.eq(\"flashcard_set_id\", filters.flashcard_set_id);\n      }\n\n      if (filters.date_range) {\n        query = query\n          .gte(\"completed_at\", filters.date_range.start)\n          .lte(\"completed_at\", filters.date_range.end);\n      }\n\n      if (filters.limit) {\n        query = query.limit(filters.limit);\n      }\n\n      if (filters.offset) {\n        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n      }\n\n      query = query.order(\"completed_at\", { ascending: false });\n\n      const { data: completions, error: completionsError } = await query;\n\n      if (completionsError) {\n        console.error(\"Error fetching filtered flashcard set completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      return c.json(completions || []);\n    } catch (error: any) {\n      console.error(\"Error in flashcard set completions endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Helper functions for completion statistics\nfunction calculateCompletionStreak(completions: any[]): number {\n  if (completions.length === 0) return 0;\n\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  \n  let streak = 0;\n  let currentDate = new Date(today);\n  \n  for (let i = 0; i < 30; i++) { // Check last 30 days\n    const dayCompletions = completions.filter(c => {\n      const completionDate = new Date(c.completed_at);\n      completionDate.setHours(0, 0, 0, 0);\n      return completionDate.getTime() === currentDate.getTime();\n    });\n    \n    if (dayCompletions.length > 0) {\n      streak++;\n    } else if (streak > 0) {\n      break; // Streak is broken\n    }\n    \n    currentDate.setDate(currentDate.getDate() - 1);\n  }\n  \n  return streak;\n}\n\nfunction getCompletionsInTimeRange(completions: any[], days: number): number {\n  const cutoffDate = new Date();\n  cutoffDate.setDate(cutoffDate.getDate() - days);\n  \n  return completions.filter(c => new Date(c.completed_at) >= cutoffDate).length;\n}\n\n// Custom Not Found handler for flashcardSetRoutes\nflashcardSetRoutes.notFound((c) => {\n  console.error(\n    `[flashcardSetRoutes] Not Found: Path ${c.req.path} with method ${c.req.method} was not matched.`\n  );\n  return c.json(\n    { error: \"Flashcard set route not found\", path: c.req.path, method: c.req.method },\n    404\n  );\n});\n\nexport default flashcardSetRoutes;\n", "modifiedCode": "import { Hono, Context, Next } from \"hono\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { supabaseMiddleware } from \"../middleware/supabaseMiddleware\";\nimport {\n  UserCompletionInsert,\n  FlashcardSetCompletionData,\n  CompletionStats,\n  CompletionFilters,\n} from \"../../../shared/types/completion\";\n\nconsole.log(\"flashcardSetRoutes.ts: Module loaded\");\n\ntype AppVariables = {\n  supabase: SupabaseClient;\n  user: { id: string };\n};\n\nconst flashcardSetRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply the Supabase middleware to all routes\nflashcardSetRoutes.use(\"*\", supabaseMiddleware);\n\n// Application-level error handler for flashcardSetRoutes\nflashcardSetRoutes.onError((err, c) => {\n  console.error(\"Error in flashcardSetRoutes:\", err);\n  return c.json(\n    {\n      error: \"An unexpected error occurred in flashcard set routes.\",\n      message: err.message,\n    },\n    500\n  );\n});\n\n// Middleware to ensure user is authenticated\nconst authMiddleware = async (\n  c: Context<{ Variables: AppVariables }>,\n  next: Next\n) => {\n  console.log(`flashcardSetRoutes: Auth middleware triggered for path: ${c.req.path}`);\n  const authHeader = c.req.header(\"Authorization\");\n\n  if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n    console.error(\"flashcardSetRoutes: Auth Error - Authorization header missing or malformed.\");\n    return c.json({ error: \"Unauthorized: Missing or malformed token\" }, 401);\n  }\n\n  const token = authHeader.split(\" \")[1];\n  if (!token) {\n    console.error(\"flashcardSetRoutes: Auth Error - Token missing after Bearer split.\");\n    return c.json({ error: \"Unauthorized: Missing token\" }, 401);\n  }\n\n  const supabase = c.get(\"supabase\");\n  if (!supabase) {\n    console.error(\"flashcardSetRoutes: Auth Error - Supabase client not found in request context.\");\n    return c.json(\n      { error: \"Server configuration error: Supabase client missing\" },\n      500\n    );\n  }\n\n  console.log(\"flashcardSetRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).\");\n  try {\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      console.error(\"flashcardSetRoutes: Auth Error - getUser failed:\", getUserError.message);\n      return c.json(\n        { error: \"Unauthorized: Invalid token\", details: getUserError.message },\n        401\n      );\n    }\n\n    const user = data?.user;\n    if (!user) {\n      console.error(\"flashcardSetRoutes: Auth Error - No user found for token\");\n      return c.json({ error: \"Unauthorized: No user found for token\" }, 401);\n    }\n\n    console.log(`flashcardSetRoutes: Auth Success - User ${user.id} authenticated. Calling next().`);\n    c.set(\"user\", user);\n    await next();\n  } catch (err: any) {\n    console.error(\"flashcardSetRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:\", err.message, err.stack);\n    return c.json(\n      { error: \"Internal server error during authentication processing\" },\n      500\n    );\n  }\n};\n\nflashcardSetRoutes.use(\"*\", authMiddleware);\n\n// Route to create a new flashcard set\nflashcardSetRoutes.post(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n\n  try {\n    const body = await c.req.json();\n    const { name, description, study_document_id, flashcards } = body;\n\n    if (!name) {\n      return c.json({ error: \"Flashcard set name is required\" }, 400);\n    }\n\n    // Create the flashcard set\n    const { data: flashcardSet, error: setError } = await supabase\n      .from(\"flashcard_sets\")\n      .insert({\n        user_id: user.id,\n        name: name,\n        description: description || null,\n        study_document_id: study_document_id || null,\n      })\n      .select(\"id, name, description, study_document_id, created_at\")\n      .single();\n\n    if (setError) {\n      console.error(\"Error creating flashcard set:\", setError);\n      return c.json(\n        { error: \"Failed to create flashcard set\", details: setError.message },\n        500\n      );\n    }\n\n    if (!flashcardSet) {\n      return c.json({ error: \"No flashcard set returned from database\" }, 500);\n    }\n\n    // If flashcards are provided, insert them\n    if (flashcards && Array.isArray(flashcards) && flashcards.length > 0) {\n      const flashcardsToInsert = flashcards.map((card: any) => ({\n        set_id: flashcardSet.id,\n        user_id: user.id,\n        front_text: card.front_text || card.question || \"\",\n        back_text: card.back_text || card.answer || \"\",\n      }));\n\n      const { error: cardsError } = await supabase\n        .from(\"flashcards\")\n        .insert(flashcardsToInsert);\n\n      if (cardsError) {\n        console.error(\"Error creating flashcards:\", cardsError);\n        // Don't fail the whole operation, just log the error\n        console.warn(\"Flashcard set created but flashcards failed to insert\");\n      }\n    }\n\n    return c.json(flashcardSet, 201);\n  } catch (error: any) {\n    console.error(\"Error in flashcard set creation:\", error);\n    return c.json({ error: error.message || \"Failed to create flashcard set\" }, 500);\n  }\n});\n\n// Route to track flashcard set completion\nflashcardSetRoutes.post(\n  \"/:setId/complete\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const setId = c.req.param(\"setId\");\n\n    if (!setId) {\n      return c.json({ error: \"Flashcard set ID is required\" }, 400);\n    }\n\n    try {\n      const body = await c.req.json();\n      const completionData: FlashcardSetCompletionData = body;\n\n      // Validate required fields\n      if (!completionData.time_spent_minutes) {\n        return c.json({ error: \"Time spent is required\" }, 400);\n      }\n\n      // Verify the flashcard set exists (we don't need to check ownership since users can review any public sets)\n      const { data: flashcardSet, error: setError } = await supabase\n        .from(\"flashcard_sets\")\n        .select(\"id, user_id\")\n        .eq(\"id\", setId)\n        .single();\n\n      if (setError || !flashcardSet) {\n        return c.json({ error: \"Flashcard set not found\" }, 404);\n      }\n\n      // Create completion record\n      const completionInsert: UserCompletionInsert = {\n        user_id: user.id,\n        flashcard_set_id: setId,\n        completion_type: \"flashcard_set\",\n        completed_at: new Date().toISOString(),\n        time_spent_minutes: completionData.time_spent_minutes,\n        metadata: completionData.metadata || null,\n      };\n\n      const { data: completion, error: insertError } = await supabase\n        .from(\"user_completions\")\n        .insert(completionInsert)\n        .select()\n        .single();\n\n      if (insertError) {\n        console.error(\"Error recording flashcard set completion:\", insertError);\n        return c.json(\n          { error: \"Failed to record completion\", details: insertError.message },\n          500\n        );\n      }\n\n      return c.json(completion, 201);\n    } catch (error: any) {\n      console.error(\"Error in flashcard set completion endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get all flashcard sets for the user\nflashcardSetRoutes.get(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  console.log(\"flashcardSetRoutes: GET / handler triggered\");\n  try {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    if (!supabase) {\n      console.error(\"GET /api/flashcard-sets/ handler: Supabase client not found in context.\");\n      return c.json(\n        {\n          error: \"Internal Server Configuration Error: Supabase client not available.\",\n        },\n        500\n      );\n    }\n    if (!user || !user.id) {\n      console.error(\"GET /api/flashcard-sets/ handler: User not found in context or user ID missing.\");\n      return c.json(\n        {\n          error: \"Authentication Error: User information not available.\",\n          details: \"User context is invalid or missing.\",\n        },\n        500\n      );\n    }\n\n    console.log(`Fetching flashcard sets for user: ${user.id}`);\n\n    // Get flashcard sets with card counts using a join query\n    const { data: flashcardSets, error } = await supabase\n      .from(\"flashcard_sets\")\n      .select(`\n        *,\n        flashcards(count)\n      `)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: false });\n\n    if (error) {\n      console.error(\"Error fetching flashcard sets:\", error);\n      return c.json(\n        { error: \"Failed to fetch flashcard sets\", details: error.message },\n        500\n      );\n    }\n\n    // Transform the data to include card_count\n    const setsWithCounts = (flashcardSets || []).map((set: any) => ({\n      ...set,\n      card_count: set.flashcards?.[0]?.count || 0,\n      flashcards: undefined, // Remove the nested flashcards array\n    }));\n\n    console.log(`Found ${setsWithCounts.length} flashcard sets for user ${user.id}`);\n    return c.json(setsWithCounts);\n  } catch (error: any) {\n    console.error(\"Error in flashcard sets fetch:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard sets\" }, 500);\n  }\n});\n\n// Route to get a specific flashcard set with its flashcards\nflashcardSetRoutes.get(\"/:setId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const setId = c.req.param(\"setId\");\n\n  if (!setId) {\n    return c.json({ error: \"Invalid flashcard set ID\" }, 400);\n  }\n\n  try {\n    // First get the flashcard set basic info with user ownership check\n    const { data: flashcardSet, error: setError } = await supabase\n      .from(\"flashcard_sets\")\n      .select(\"*\")\n      .eq(\"id\", setId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (setError) {\n      console.error(\"Error fetching flashcard set:\", setError);\n      if (setError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to fetch flashcard set\", details: setError.message },\n        500\n      );\n    }\n\n    if (!flashcardSet) {\n      return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n    }\n\n    // Get all flashcards for this set (with user ownership check)\n    const { data: flashcards, error: flashcardsError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"set_id\", setId)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: true });\n\n    if (flashcardsError) {\n      console.error(\"Error fetching flashcards:\", flashcardsError);\n      return c.json(\n        { error: \"Failed to fetch flashcards\", details: flashcardsError.message },\n        500\n      );\n    }\n\n    return c.json({\n      ...flashcardSet,\n      flashcards: flashcards || [],\n    });\n  } catch (error: any) {\n    console.error(\"Error fetching flashcard set with flashcards:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard set\" }, 500);\n  }\n});\n\n// Route to delete a flashcard set\nflashcardSetRoutes.delete(\"/:setId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const setId = c.req.param(\"setId\");\n\n  if (!setId) {\n    return c.json({ error: \"Invalid flashcard set ID\" }, 400);\n  }\n\n  try {\n    // First verify the flashcard set exists and user owns it\n    const { data: flashcardSet, error: setError } = await supabase\n      .from(\"flashcard_sets\")\n      .select(\"id\")\n      .eq(\"id\", setId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (setError) {\n      console.error(\"Error checking flashcard set ownership:\", setError);\n      if (setError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard set\", details: setError.message },\n        500\n      );\n    }\n\n    if (!flashcardSet) {\n      return c.json({ error: \"Flashcard set not found or you don't have access\" }, 404);\n    }\n\n    // Delete all flashcards in the set first\n    const { error: flashcardsError } = await supabase\n      .from(\"flashcards\")\n      .delete()\n      .eq(\"set_id\", setId)\n      .eq(\"user_id\", user.id);\n\n    if (flashcardsError) {\n      console.error(\"Error deleting flashcards:\", flashcardsError);\n      return c.json(\n        { error: \"Failed to delete flashcards\", details: flashcardsError.message },\n        500\n      );\n    }\n\n    // Delete the flashcard set\n    const { error: deleteError } = await supabase\n      .from(\"flashcard_sets\")\n      .delete()\n      .eq(\"id\", setId)\n      .eq(\"user_id\", user.id);\n\n    if (deleteError) {\n      console.error(\"Error deleting flashcard set:\", deleteError);\n      return c.json(\n        { error: \"Failed to delete flashcard set\", details: deleteError.message },\n        500\n      );\n    }\n\n    return c.json({ message: \"Flashcard set deleted successfully\" }, 200);\n  } catch (error: any) {\n    console.error(\"Error deleting flashcard set:\", error);\n    return c.json({ error: error.message || \"Failed to delete flashcard set\" }, 500);\n  }\n});\n\n// Route to get due flashcards for SRS review\nflashcardSetRoutes.get(\"/:setId/due\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const setId = c.req.param(\"setId\");\n\n  if (!setId) {\n    return c.json({ error: \"Invalid flashcard set ID\" }, 400);\n  }\n\n  try {\n    // Since the current flashcards table doesn't have SRS fields like due_at,\n    // we'll return all flashcards for the set. In the future, we can add SRS fields\n    // or implement a separate review tracking system.\n    const { data: flashcards, error: flashcardsError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"set_id\", setId)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: true });\n\n    if (flashcardsError) {\n      console.error(\"Error fetching flashcards:\", flashcardsError);\n      return c.json(\n        { error: \"Failed to fetch flashcards\", details: flashcardsError.message },\n        500\n      );\n    }\n\n    // For now, return all flashcards as \"due\" since we don't have SRS tracking yet\n    return c.json(flashcards || []);\n  } catch (error: any) {\n    console.error(\"Error fetching due flashcards:\", error);\n    return c.json({ error: error.message || \"Failed to fetch due flashcards\" }, 500);\n  }\n});\n\n// Route to get completion statistics for flashcard sets\nflashcardSetRoutes.get(\n  \"/stats/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      // Get all user completions for flashcard sets\n      const { data: completions, error: completionsError } = await supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .eq(\"completion_type\", \"flashcard_set\")\n        .order(\"completed_at\", { ascending: false });\n\n      if (completionsError) {\n        console.error(\"Error fetching flashcard set completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      const allCompletions = completions || [];\n\n      // Calculate statistics specific to flashcard sets\n      const stats = {\n        total_completions: allCompletions.length,\n        total_study_time_minutes: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0),\n        recent_completions: allCompletions.slice(0, 10),\n        completion_streak: calculateCompletionStreak(allCompletions),\n        this_week_completions: getCompletionsInTimeRange(allCompletions, 7),\n        this_month_completions: getCompletionsInTimeRange(allCompletions, 30),\n        average_cards_reviewed: allCompletions.reduce((sum, c) => {\n          const cardsReviewed = c.metadata?.cards_reviewed || 0;\n          return sum + cardsReviewed;\n        }, 0) / (allCompletions.length || 1),\n        average_session_time: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0) / (allCompletions.length || 1),\n      };\n\n      return c.json(stats);\n    } catch (error: any) {\n      console.error(\"Error fetching flashcard set completion stats:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get filtered completion history for flashcard sets\nflashcardSetRoutes.get(\n  \"/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      const queryParams = c.req.query();\n      const filters: CompletionFilters = {\n        completion_type: \"flashcard_set\", // Fixed to flashcard_set\n        flashcard_set_id: queryParams.flashcard_set_id,\n        limit: queryParams.limit ? parseInt(queryParams.limit) : 50,\n        offset: queryParams.offset ? parseInt(queryParams.offset) : 0,\n      };\n\n      if (queryParams.start_date && queryParams.end_date) {\n        filters.date_range = {\n          start: queryParams.start_date,\n          end: queryParams.end_date,\n        };\n      }\n\n      // Build query\n      let query = supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .eq(\"completion_type\", \"flashcard_set\");\n\n      if (filters.flashcard_set_id) {\n        query = query.eq(\"flashcard_set_id\", filters.flashcard_set_id);\n      }\n\n      if (filters.date_range) {\n        query = query\n          .gte(\"completed_at\", filters.date_range.start)\n          .lte(\"completed_at\", filters.date_range.end);\n      }\n\n      if (filters.limit) {\n        query = query.limit(filters.limit);\n      }\n\n      if (filters.offset) {\n        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n      }\n\n      query = query.order(\"completed_at\", { ascending: false });\n\n      const { data: completions, error: completionsError } = await query;\n\n      if (completionsError) {\n        console.error(\"Error fetching filtered flashcard set completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      return c.json(completions || []);\n    } catch (error: any) {\n      console.error(\"Error in flashcard set completions endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Helper functions for completion statistics\nfunction calculateCompletionStreak(completions: any[]): number {\n  if (completions.length === 0) return 0;\n\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  \n  let streak = 0;\n  let currentDate = new Date(today);\n  \n  for (let i = 0; i < 30; i++) { // Check last 30 days\n    const dayCompletions = completions.filter(c => {\n      const completionDate = new Date(c.completed_at);\n      completionDate.setHours(0, 0, 0, 0);\n      return completionDate.getTime() === currentDate.getTime();\n    });\n    \n    if (dayCompletions.length > 0) {\n      streak++;\n    } else if (streak > 0) {\n      break; // Streak is broken\n    }\n    \n    currentDate.setDate(currentDate.getDate() - 1);\n  }\n  \n  return streak;\n}\n\nfunction getCompletionsInTimeRange(completions: any[], days: number): number {\n  const cutoffDate = new Date();\n  cutoffDate.setDate(cutoffDate.getDate() - days);\n  \n  return completions.filter(c => new Date(c.completed_at) >= cutoffDate).length;\n}\n\n// Custom Not Found handler for flashcardSetRoutes\nflashcardSetRoutes.notFound((c) => {\n  console.error(\n    `[flashcardSetRoutes] Not Found: Path ${c.req.path} with method ${c.req.method} was not matched.`\n  );\n  return c.json(\n    { error: \"Flashcard set route not found\", path: c.req.path, method: c.req.method },\n    404\n  );\n});\n\nexport default flashcardSetRoutes;\n"}