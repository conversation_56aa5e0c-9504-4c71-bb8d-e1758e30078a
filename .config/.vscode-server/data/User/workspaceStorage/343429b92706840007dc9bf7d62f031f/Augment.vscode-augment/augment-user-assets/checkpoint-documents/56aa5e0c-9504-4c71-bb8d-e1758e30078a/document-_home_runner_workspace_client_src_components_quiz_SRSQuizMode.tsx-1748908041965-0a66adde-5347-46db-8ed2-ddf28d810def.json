{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/SRSQuizMode.tsx"}, "originalCode": "import React, { useEffect, useState } from \"react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  ChevronLeft,\n  ChevronRight,\n  X,\n  Clock,\n  CheckCircle,\n  RotateCcw,\n  Brain,\n  TrendingUp,\n  Calendar,\n} from \"lucide-react\";\nimport { notify } from \"@/lib/notifications\";\nimport { useKeyboardNavigation } from \"@/hooks/useKeyboardNavigation\";\nimport {\n  ReviewDifficulty,\n  updateQuizQuestionSRS,\n  getQuizQuestionsDueForReview,\n  sortQuizQuestionsByDueDate,\n} from \"@/lib/srs\";\nimport { QuizAPI } from \"@/lib/api/quizApi\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface SRSQuizModeProps {\n  onExit: () => void;\n}\n\ninterface SRSResults {\n  questionsReviewed: number;\n  correctAnswers: number;\n  accuracy: number;\n  timeSpent: number;\n}\n\nexport const SRSQuizMode: React.FC<SRSQuizModeProps> = ({ onExit }) => {\n  const { user } = useAuth();\n  const [allQuestions, setAllQuestions] = useState<QuizQuestion[]>([]);\n  const [dueQuestions, setDueQuestions] = useState<QuizQuestion[]>([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({});\n  const [submittedAnswers, setSubmittedAnswers] = useState<Array<boolean>>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const [results, setResults] = useState<SRSResults | null>(null);\n  const [startTime] = useState(Date.now());\n\n  useEffect(() => {\n    const fetchDueQuestions = async () => {\n      if (!user) return;\n      setLoading(true);\n      setError(null);\n\n      try {\n        // Fetch all quiz questions for the user\n        const { data: questionsData, error: questionsError } = await supabase\n          .from(\"quiz_questions\")\n          .select(\"*\")\n          .eq(\"user_id\", user.id)\n          .order(\"created_at\", { ascending: true });\n\n        if (questionsError) throw questionsError;\n\n        const allQuestions = questionsData || [];\n        setAllQuestions(allQuestions);\n\n        // Filter to only questions due for review and sort by priority\n        const dueQuestions = getQuizQuestionsDueForReview(allQuestions);\n        const sortedDueQuestions = sortQuizQuestionsByDueDate(dueQuestions);\n\n        setDueQuestions(sortedDueQuestions);\n        setSubmittedAnswers(new Array(sortedDueQuestions.length).fill(false));\n\n        if (sortedDueQuestions.length === 0) {\n          notify.info({\n            title: \"No questions due\",\n            description:\n              \"Great job! No questions are due for review right now.\",\n          });\n        }\n      } catch (err: any) {\n        console.error(\"Error fetching due questions:\", err);\n        setError(err.message || \"Failed to load questions due for review.\");\n        notify.error({\n          title: \"Failed to load SRS questions\",\n          description: err.message || \"Please try again later.\",\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDueQuestions();\n  }, [user]);\n\n  const handleAnswerSelect = (questionId: string, answer: any) => {\n    setUserAnswers((prev) => ({\n      ...prev,\n      [questionId]: answer,\n    }));\n  };\n\n  const handleSelectAllAnswerToggle = (\n    questionId: string,\n    optionText: string\n  ) => {\n    setUserAnswers((prev) => {\n      const currentAnswers = prev[questionId] || [];\n      const isSelected = currentAnswers.includes(optionText);\n\n      if (isSelected) {\n        return {\n          ...prev,\n          [questionId]: currentAnswers.filter(\n            (ans: string) => ans !== optionText\n          ),\n        };\n      } else {\n        return {\n          ...prev,\n          [questionId]: [...currentAnswers, optionText],\n        };\n      }\n    });\n  };\n\n  const checkAnswerCorrect = (\n    question: QuizQuestion,\n    userAnswer: any\n  ): boolean => {\n    if (!userAnswer) return false;\n\n    if (question.type === \"multiple_choice\" && question.options) {\n      const selectedOption = (question.options as any[])?.find(\n        (opt) => opt.text === userAnswer\n      );\n      return selectedOption?.is_correct || false;\n    } else if (question.type === \"select_all_that_apply\" && question.options) {\n      const userSelectedAnswers = userAnswer || [];\n      const correctOptions = (question.options as any[])?.filter(\n        (opt) => opt.is_correct\n      );\n      const correctOptionTexts = correctOptions.map((opt) => opt.text);\n\n      return (\n        correctOptionTexts.length === userSelectedAnswers.length &&\n        correctOptionTexts.every((text) => userSelectedAnswers.includes(text))\n      );\n    } else if (question.type === \"true_false\") {\n      return userAnswer === question.correct_answer;\n    } else if (question.type === \"short_answer\") {\n      return (\n        userAnswer?.toLowerCase().trim() ===\n        question.correct_answer?.toLowerCase().trim()\n      );\n    }\n    return false;\n  };\n\n  const handleSubmitAnswer = async () => {\n    const currentQuestion = dueQuestions[currentQuestionIndex];\n    if (\n      currentQuestion &&\n      userAnswers[currentQuestion.id] !== undefined &&\n      !submittedAnswers[currentQuestionIndex]\n    ) {\n      const isCorrect = checkAnswerCorrect(\n        currentQuestion,\n        userAnswers[currentQuestion.id]\n      );\n\n      const newSubmittedAnswers = [...submittedAnswers];\n      newSubmittedAnswers[currentQuestionIndex] = true;\n      setSubmittedAnswers(newSubmittedAnswers);\n\n      // SRS Update Logic with more nuanced difficulty assessment\n      let difficulty: ReviewDifficulty;\n      if (isCorrect) {\n        // Could add time-based difficulty assessment here\n        difficulty = ReviewDifficulty.EASY;\n      } else {\n        difficulty = ReviewDifficulty.DIFFICULT;\n      }\n\n      const updatedQuestion = updateQuizQuestionSRS(\n        currentQuestion,\n        difficulty\n      );\n\n      // Persist SRS updates to the backend\n      try {\n        await QuizAPI.updateQuestionSRS(updatedQuestion.id, {\n          srs_level: updatedQuestion.srs_level,\n          due_at: updatedQuestion.due_at,\n          last_reviewed_at: updatedQuestion.last_reviewed_at,\n          srs_interval: updatedQuestion.srs_interval,\n          srs_ease_factor: updatedQuestion.srs_ease_factor,\n          srs_repetitions: updatedQuestion.srs_repetitions,\n          srs_correct_streak: updatedQuestion.srs_correct_streak,\n        });\n\n        console.log(\"SRS Updated successfully:\", updatedQuestion.id);\n      } catch (error) {\n        console.error(\"Failed to update SRS data:\", error);\n        notify.error({\n          title: \"SRS Update Failed\",\n          description: \"Failed to save spaced repetition data\",\n        });\n      }\n\n      // Auto-advance to next question after a brief delay\n      setTimeout(() => {\n        if (currentQuestionIndex < dueQuestions.length - 1) {\n          setCurrentQuestionIndex((prev) => prev + 1);\n        } else {\n          handleCompleteReview();\n        }\n      }, 1500);\n    }\n  };\n\n  const handleCompleteReview = () => {\n    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n    let correctAnswers = 0;\n\n    dueQuestions.forEach((q) => {\n      const userAnswer = userAnswers[q.id];\n      if (userAnswer && checkAnswerCorrect(q, userAnswer)) {\n        correctAnswers++;\n      }\n    });\n\n    const reviewResults: SRSResults = {\n      questionsReviewed: dueQuestions.length,\n      correctAnswers,\n      accuracy: Math.round((correctAnswers / dueQuestions.length) * 100),\n      timeSpent,\n    };\n\n    setResults(reviewResults);\n    setIsCompleted(true);\n\n    notify.success({\n      title: \"SRS Review Complete!\",\n      description: `You reviewed ${dueQuestions.length} questions with ${reviewResults.accuracy}% accuracy.`,\n    });\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n  };\n\n  const restartReview = () => {\n    setCurrentQuestionIndex(0);\n    setUserAnswers({});\n    setSubmittedAnswers(new Array(dueQuestions.length).fill(false));\n    setIsCompleted(false);\n    setResults(null);\n  };\n\n  // Keyboard navigation\n  useKeyboardNavigation({\n    onNext: () =>\n      !isCompleted && currentQuestionIndex < dueQuestions.length - 1,\n    onPrevious: () => !isCompleted && currentQuestionIndex > 0,\n    onSubmit: () => !isCompleted && handleSubmitAnswer,\n    onEscape: onExit,\n    disabled: loading || isCompleted,\n  });\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6 text-center\">\n          <p className=\"text-red-400 mb-4\">{error}</p>\n          <Button\n            onClick={onExit}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300\"\n          >\n            Go Back\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (dueQuestions.length === 0) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6 text-center\">\n          <div className=\"mb-4\">\n            <CheckCircle className=\"h-16 w-16 mx-auto text-green-500 mb-4\" />\n            <h2 className=\"text-xl font-semibold text-slate-200 mb-2\">\n              All Caught Up!\n            </h2>\n            <p className=\"text-slate-400\">\n              No questions are due for review right now.\n            </p>\n            <p className=\"text-slate-500 text-sm mt-2\">\n              You have {allQuestions.length} total questions across all quizzes.\n            </p>\n          </div>\n          <Button\n            onClick={onExit}\n            className=\"bg-purple-600 hover:bg-purple-700\"\n          >\n            Back to Dashboard\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (isCompleted && results) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader className=\"text-center\">\n          <CardTitle className=\"text-2xl font-bold text-slate-200 flex items-center justify-center gap-2\">\n            <Brain className=\"h-8 w-8 text-purple-400\" />\n            SRS Review Complete!\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"p-6\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Questions Reviewed</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.questionsReviewed}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Correct</p>\n              <p className=\"text-2xl font-bold text-green-400\">\n                {results.correctAnswers}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Accuracy</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.accuracy}%\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Time</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {formatTime(results.timeSpent)}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex justify-center space-x-3\">\n            <Button\n              onClick={restartReview}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300\"\n            >\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              Review Again\n            </Button>\n            <Button\n              onClick={onExit}\n              className=\"bg-purple-600 hover:bg-purple-700\"\n            >\n              Finish\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const currentQuestion = dueQuestions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / dueQuestions.length) * 100;\n  const isAnswered = userAnswers[currentQuestion.id] !== undefined;\n  const isSubmitted = submittedAnswers[currentQuestionIndex];\n\n  return (\n    <div className=\"max-w-2xl mx-auto space-y-6\">\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <div>\n              <h2 className=\"text-xl font-semibold text-slate-200 flex items-center gap-2\">\n                <Brain className=\"h-5 w-5 text-purple-400\" />\n                SRS Review Mode\n              </h2>\n              <p className=\"text-slate-400 text-sm mt-1\">\n                Spaced repetition optimized learning\n              </p>\n            </div>\n            <Button\n              onClick={onExit}\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"text-slate-400 hover:text-slate-200\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-slate-400\">\n                Question {currentQuestionIndex + 1} of {dueQuestions.length}\n              </span>\n              <Badge\n                variant=\"secondary\"\n                className=\"text-xs bg-purple-900/30 text-purple-400 border-purple-700\"\n              >\n                <TrendingUp className=\"h-3 w-3 mr-1\" />\n                SRS Level {currentQuestion.srs_level || 0}\n              </Badge>\n            </div>\n            <div className=\"flex items-center space-x-2 text-sm text-slate-400\">\n              <Clock className=\"h-4 w-4\" />\n              <span>\n                {formatTime(Math.round((Date.now() - startTime) / 1000))}\n              </span>\n            </div>\n          </div>\n\n          <Progress value={progress} className=\"mb-6\" />\n        </CardContent>\n      </Card>\n\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-slate-200 mb-4\">\n            {currentQuestion.question_text}\n          </h3>\n\n          <div className=\"space-y-3\">\n            {/* Question rendering logic (same as QuizPlayer) */}\n            {currentQuestion.type === \"multiple_choice\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  {(currentQuestion.options as any[]).map((option, index) => (\n                    <label\n                      key={index}\n                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${\n                        userAnswers[currentQuestion.id] === option.text\n                          ? isSubmitted\n                            ? option.is_correct\n                              ? \"border-green-500 bg-green-950/20\"\n                              : \"border-red-500 bg-red-950/20\"\n                            : \"border-purple-500 bg-purple-950/20\"\n                          : isSubmitted && option.is_correct\n                          ? \"border-green-500 bg-green-950/20\"\n                          : \"border-slate-600\"\n                      }`}\n                    >\n                      <input\n                        type=\"radio\"\n                        name={`question-${currentQuestion.id}`}\n                        value={option.text}\n                        checked={\n                          userAnswers[currentQuestion.id] === option.text\n                        }\n                        onChange={() =>\n                          !isSubmitted &&\n                          handleAnswerSelect(currentQuestion.id, option.text)\n                        }\n                        className=\"sr-only\"\n                        disabled={isSubmitted}\n                      />\n                      <div\n                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                          userAnswers[currentQuestion.id] === option.text\n                            ? isSubmitted\n                              ? option.is_correct\n                                ? \"border-green-500 bg-green-500\"\n                                : \"border-red-500 bg-red-500\"\n                              : \"border-purple-500 bg-purple-500\"\n                            : isSubmitted && option.is_correct\n                            ? \"border-green-500 bg-green-500\"\n                            : \"border-slate-400\"\n                        }`}\n                      >\n                        {(userAnswers[currentQuestion.id] === option.text ||\n                          (isSubmitted && option.is_correct)) && (\n                          <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                        )}\n                      </div>\n                      <span className=\"text-slate-200\">{option.text}</span>\n                    </label>\n                  ))}\n                </div>\n              )}\n\n            {currentQuestion.type === \"select_all_that_apply\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  <p className=\"text-sm text-slate-400 mb-3 italic\">\n                    Select all correct answers:\n                  </p>\n                  {(currentQuestion.options as any[]).map((option, index) => {\n                    const isSelected = (\n                      userAnswers[currentQuestion.id] || []\n                    ).includes(option.text);\n                    return (\n                      <label\n                        key={index}\n                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${\n                          isSelected\n                            ? isSubmitted\n                              ? option.is_correct\n                                ? \"border-green-500 bg-green-950/20\"\n                                : \"border-red-500 bg-red-950/20\"\n                              : \"border-purple-500 bg-purple-950/20\"\n                            : isSubmitted && option.is_correct\n                            ? \"border-green-500 bg-green-950/20\"\n                            : \"border-slate-600\"\n                        }`}\n                      >\n                        <input\n                          type=\"checkbox\"\n                          checked={isSelected}\n                          onChange={() =>\n                            !isSubmitted &&\n                            handleSelectAllAnswerToggle(\n                              currentQuestion.id,\n                              option.text\n                            )\n                          }\n                          className=\"sr-only\"\n                          disabled={isSubmitted}\n                        />\n                        <div\n                          className={`w-4 h-4 border-2 mr-3 flex items-center justify-center rounded ${\n                            isSelected\n                              ? isSubmitted\n                                ? option.is_correct\n                                  ? \"border-green-500 bg-green-500\"\n                                  : \"border-red-500 bg-red-500\"\n                                : \"border-purple-500 bg-purple-500\"\n                              : isSubmitted && option.is_correct\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-slate-400\"\n                          }`}\n                        >\n                          {(isSelected ||\n                            (isSubmitted && option.is_correct)) && (\n                            <div className=\"w-2 h-2 bg-white rounded-sm\"></div>\n                          )}\n                        </div>\n                        <span className=\"text-slate-200\">{option.text}</span>\n                      </label>\n                    );\n                  })}\n                </div>\n              )}\n\n            {currentQuestion.type === \"true_false\" && (\n              <div className=\"space-y-3\">\n                {[\"True\", \"False\"].map((val) => (\n                  <label\n                    key={val}\n                    className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${\n                      userAnswers[currentQuestion.id] === val\n                        ? isSubmitted\n                          ? val === currentQuestion.correct_answer\n                            ? \"border-green-500 bg-green-950/20\"\n                            : \"border-red-500 bg-red-950/20\"\n                          : \"border-purple-500 bg-purple-950/20\"\n                        : isSubmitted && val === currentQuestion.correct_answer\n                        ? \"border-green-500 bg-green-950/20\"\n                        : \"border-slate-600\"\n                    }`}\n                  >\n                    <input\n                      type=\"radio\"\n                      name={`question-${currentQuestion.id}`}\n                      value={val}\n                      checked={userAnswers[currentQuestion.id] === val}\n                      onChange={() =>\n                        !isSubmitted &&\n                        handleAnswerSelect(currentQuestion.id, val)\n                      }\n                      className=\"sr-only\"\n                      disabled={isSubmitted}\n                    />\n                    <div\n                      className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                        userAnswers[currentQuestion.id] === val\n                          ? isSubmitted\n                            ? val === currentQuestion.correct_answer\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-red-500 bg-red-500\"\n                            : \"border-purple-500 bg-purple-500\"\n                          : isSubmitted &&\n                            val === currentQuestion.correct_answer\n                          ? \"border-green-500 bg-green-500\"\n                          : \"border-slate-400\"\n                      }`}\n                    >\n                      {(userAnswers[currentQuestion.id] === val ||\n                        (isSubmitted &&\n                          val === currentQuestion.correct_answer)) && (\n                        <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                      )}\n                    </div>\n                    <span className=\"text-slate-200\">{val}</span>\n                  </label>\n                ))}\n              </div>\n            )}\n\n            {currentQuestion.type === \"short_answer\" && (\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={userAnswers[currentQuestion.id] || \"\"}\n                  onChange={(e) =>\n                    !isSubmitted &&\n                    handleAnswerSelect(currentQuestion.id, e.target.value)\n                  }\n                  className={`w-full px-4 py-3 border rounded-lg text-slate-200 placeholder-slate-400 focus:ring-1 outline-none ${\n                    isSubmitted\n                      ? userAnswers[currentQuestion.id]\n                          ?.toLowerCase()\n                          .trim() ===\n                        currentQuestion.correct_answer?.toLowerCase().trim()\n                        ? \"bg-green-950/20 border-green-500 focus:border-green-500 focus:ring-green-500\"\n                        : \"bg-red-950/20 border-red-500 focus:border-red-500 focus:ring-red-500\"\n                      : \"bg-slate-900 border-slate-600 focus:border-purple-500 focus:ring-purple-500\"\n                  }`}\n                  placeholder=\"Type your answer here...\"\n                  disabled={isSubmitted}\n                />\n                {isSubmitted && (\n                  <p className=\"text-sm text-slate-400\">\n                    <span className=\"font-medium\">Correct answer:</span>{\" \"}\n                    {currentQuestion.correct_answer}\n                  </p>\n                )}\n              </div>\n            )}\n          </div>\n\n          {isSubmitted && currentQuestion.explanation && (\n            <div className=\"mt-4 p-4 bg-slate-900 rounded-lg border border-slate-600\">\n              <h4 className=\"text-sm font-medium text-slate-300 mb-2\">\n                Explanation:\n              </h4>\n              <p className=\"text-slate-400 text-sm\">\n                {currentQuestion.explanation}\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      <div className=\"flex justify-center\">\n        {!isSubmitted && isAnswered ? (\n          <Button\n            onClick={handleSubmitAnswer}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n          >\n            <CheckCircle className=\"h-4 w-4 mr-2\" />\n            Submit Answer\n          </Button>\n        ) : isSubmitted ? (\n          <div className=\"text-center\">\n            <p className=\"text-slate-400 text-sm\">\n              {currentQuestionIndex === dueQuestions.length - 1\n                ? \"Completing review...\"\n                : \"Moving to next question...\"}\n            </p>\n          </div>\n        ) : (\n          <p className=\"text-slate-500 text-sm\">Select an answer to continue</p>\n        )}\n      </div>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useEffect, useState } from \"react\";\n// Removed Supabase import - using backend API endpoints\nimport { useAuth } from \"../../hooks/useAuth\";\nimport { Tables } from \"../../types/supabase\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  ChevronLeft,\n  ChevronRight,\n  X,\n  Clock,\n  CheckCircle,\n  RotateCcw,\n  Brain,\n  TrendingUp,\n  Calendar,\n} from \"lucide-react\";\nimport { notify } from \"@/lib/notifications\";\nimport { useKeyboardNavigation } from \"@/hooks/useKeyboardNavigation\";\nimport {\n  ReviewDifficulty,\n  updateQuizQuestionSRS,\n  getQuizQuestionsDueForReview,\n  sortQuizQuestionsByDueDate,\n} from \"@/lib/srs\";\nimport { QuizAPI } from \"@/lib/api/quizApi\";\n\ntype QuizQuestion = Tables<\"quiz_questions\">;\n\ninterface SRSQuizModeProps {\n  onExit: () => void;\n}\n\ninterface SRSResults {\n  questionsReviewed: number;\n  correctAnswers: number;\n  accuracy: number;\n  timeSpent: number;\n}\n\nexport const SRSQuizMode: React.FC<SRSQuizModeProps> = ({ onExit }) => {\n  const { user } = useAuth();\n  const [allQuestions, setAllQuestions] = useState<QuizQuestion[]>([]);\n  const [dueQuestions, setDueQuestions] = useState<QuizQuestion[]>([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({});\n  const [submittedAnswers, setSubmittedAnswers] = useState<Array<boolean>>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isCompleted, setIsCompleted] = useState(false);\n  const [results, setResults] = useState<SRSResults | null>(null);\n  const [startTime] = useState(Date.now());\n\n  useEffect(() => {\n    const fetchDueQuestions = async () => {\n      if (!user) return;\n      setLoading(true);\n      setError(null);\n\n      try {\n        // Fetch all quiz questions for the user\n        const { data: questionsData, error: questionsError } = await supabase\n          .from(\"quiz_questions\")\n          .select(\"*\")\n          .eq(\"user_id\", user.id)\n          .order(\"created_at\", { ascending: true });\n\n        if (questionsError) throw questionsError;\n\n        const allQuestions = questionsData || [];\n        setAllQuestions(allQuestions);\n\n        // Filter to only questions due for review and sort by priority\n        const dueQuestions = getQuizQuestionsDueForReview(allQuestions);\n        const sortedDueQuestions = sortQuizQuestionsByDueDate(dueQuestions);\n\n        setDueQuestions(sortedDueQuestions);\n        setSubmittedAnswers(new Array(sortedDueQuestions.length).fill(false));\n\n        if (sortedDueQuestions.length === 0) {\n          notify.info({\n            title: \"No questions due\",\n            description:\n              \"Great job! No questions are due for review right now.\",\n          });\n        }\n      } catch (err: any) {\n        console.error(\"Error fetching due questions:\", err);\n        setError(err.message || \"Failed to load questions due for review.\");\n        notify.error({\n          title: \"Failed to load SRS questions\",\n          description: err.message || \"Please try again later.\",\n        });\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDueQuestions();\n  }, [user]);\n\n  const handleAnswerSelect = (questionId: string, answer: any) => {\n    setUserAnswers((prev) => ({\n      ...prev,\n      [questionId]: answer,\n    }));\n  };\n\n  const handleSelectAllAnswerToggle = (\n    questionId: string,\n    optionText: string\n  ) => {\n    setUserAnswers((prev) => {\n      const currentAnswers = prev[questionId] || [];\n      const isSelected = currentAnswers.includes(optionText);\n\n      if (isSelected) {\n        return {\n          ...prev,\n          [questionId]: currentAnswers.filter(\n            (ans: string) => ans !== optionText\n          ),\n        };\n      } else {\n        return {\n          ...prev,\n          [questionId]: [...currentAnswers, optionText],\n        };\n      }\n    });\n  };\n\n  const checkAnswerCorrect = (\n    question: QuizQuestion,\n    userAnswer: any\n  ): boolean => {\n    if (!userAnswer) return false;\n\n    if (question.type === \"multiple_choice\" && question.options) {\n      const selectedOption = (question.options as any[])?.find(\n        (opt) => opt.text === userAnswer\n      );\n      return selectedOption?.is_correct || false;\n    } else if (question.type === \"select_all_that_apply\" && question.options) {\n      const userSelectedAnswers = userAnswer || [];\n      const correctOptions = (question.options as any[])?.filter(\n        (opt) => opt.is_correct\n      );\n      const correctOptionTexts = correctOptions.map((opt) => opt.text);\n\n      return (\n        correctOptionTexts.length === userSelectedAnswers.length &&\n        correctOptionTexts.every((text) => userSelectedAnswers.includes(text))\n      );\n    } else if (question.type === \"true_false\") {\n      return userAnswer === question.correct_answer;\n    } else if (question.type === \"short_answer\") {\n      return (\n        userAnswer?.toLowerCase().trim() ===\n        question.correct_answer?.toLowerCase().trim()\n      );\n    }\n    return false;\n  };\n\n  const handleSubmitAnswer = async () => {\n    const currentQuestion = dueQuestions[currentQuestionIndex];\n    if (\n      currentQuestion &&\n      userAnswers[currentQuestion.id] !== undefined &&\n      !submittedAnswers[currentQuestionIndex]\n    ) {\n      const isCorrect = checkAnswerCorrect(\n        currentQuestion,\n        userAnswers[currentQuestion.id]\n      );\n\n      const newSubmittedAnswers = [...submittedAnswers];\n      newSubmittedAnswers[currentQuestionIndex] = true;\n      setSubmittedAnswers(newSubmittedAnswers);\n\n      // SRS Update Logic with more nuanced difficulty assessment\n      let difficulty: ReviewDifficulty;\n      if (isCorrect) {\n        // Could add time-based difficulty assessment here\n        difficulty = ReviewDifficulty.EASY;\n      } else {\n        difficulty = ReviewDifficulty.DIFFICULT;\n      }\n\n      const updatedQuestion = updateQuizQuestionSRS(\n        currentQuestion,\n        difficulty\n      );\n\n      // Persist SRS updates to the backend\n      try {\n        await QuizAPI.updateQuestionSRS(updatedQuestion.id, {\n          srs_level: updatedQuestion.srs_level ?? undefined,\n          due_at: updatedQuestion.due_at ?? undefined,\n          last_reviewed_at: updatedQuestion.last_reviewed_at ?? undefined,\n          srs_interval: updatedQuestion.srs_interval ?? undefined,\n          srs_ease_factor: updatedQuestion.srs_ease_factor ?? undefined,\n          srs_repetitions: updatedQuestion.srs_repetitions ?? undefined,\n          srs_correct_streak: updatedQuestion.srs_correct_streak ?? undefined,\n        });\n\n        console.log(\"SRS Updated successfully:\", updatedQuestion.id);\n      } catch (error) {\n        console.error(\"Failed to update SRS data:\", error);\n        notify.error({\n          title: \"SRS Update Failed\",\n          description: \"Failed to save spaced repetition data\",\n        });\n      }\n\n      // Auto-advance to next question after a brief delay\n      setTimeout(() => {\n        if (currentQuestionIndex < dueQuestions.length - 1) {\n          setCurrentQuestionIndex((prev) => prev + 1);\n        } else {\n          handleCompleteReview();\n        }\n      }, 1500);\n    }\n  };\n\n  const handleCompleteReview = () => {\n    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n    let correctAnswers = 0;\n\n    dueQuestions.forEach((q) => {\n      const userAnswer = userAnswers[q.id];\n      if (userAnswer && checkAnswerCorrect(q, userAnswer)) {\n        correctAnswers++;\n      }\n    });\n\n    const reviewResults: SRSResults = {\n      questionsReviewed: dueQuestions.length,\n      correctAnswers,\n      accuracy: Math.round((correctAnswers / dueQuestions.length) * 100),\n      timeSpent,\n    };\n\n    setResults(reviewResults);\n    setIsCompleted(true);\n\n    notify.success({\n      title: \"SRS Review Complete!\",\n      description: `You reviewed ${dueQuestions.length} questions with ${reviewResults.accuracy}% accuracy.`,\n    });\n  };\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n  };\n\n  const restartReview = () => {\n    setCurrentQuestionIndex(0);\n    setUserAnswers({});\n    setSubmittedAnswers(new Array(dueQuestions.length).fill(false));\n    setIsCompleted(false);\n    setResults(null);\n  };\n\n  // Keyboard navigation\n  useKeyboardNavigation({\n    onNext: () =>\n      !isCompleted && currentQuestionIndex < dueQuestions.length - 1,\n    onPrevious: () => !isCompleted && currentQuestionIndex > 0,\n    onSubmit: () => !isCompleted && handleSubmitAnswer,\n    onEscape: onExit,\n    disabled: loading || isCompleted,\n  });\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6 text-center\">\n          <p className=\"text-red-400 mb-4\">{error}</p>\n          <Button\n            onClick={onExit}\n            variant=\"outline\"\n            className=\"border-slate-600 text-slate-300\"\n          >\n            Go Back\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (dueQuestions.length === 0) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6 text-center\">\n          <div className=\"mb-4\">\n            <CheckCircle className=\"h-16 w-16 mx-auto text-green-500 mb-4\" />\n            <h2 className=\"text-xl font-semibold text-slate-200 mb-2\">\n              All Caught Up!\n            </h2>\n            <p className=\"text-slate-400\">\n              No questions are due for review right now.\n            </p>\n            <p className=\"text-slate-500 text-sm mt-2\">\n              You have {allQuestions.length} total questions across all quizzes.\n            </p>\n          </div>\n          <Button\n            onClick={onExit}\n            className=\"bg-purple-600 hover:bg-purple-700\"\n          >\n            Back to Dashboard\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (isCompleted && results) {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader className=\"text-center\">\n          <CardTitle className=\"text-2xl font-bold text-slate-200 flex items-center justify-center gap-2\">\n            <Brain className=\"h-8 w-8 text-purple-400\" />\n            SRS Review Complete!\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"p-6\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Questions Reviewed</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.questionsReviewed}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Correct</p>\n              <p className=\"text-2xl font-bold text-green-400\">\n                {results.correctAnswers}\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Accuracy</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {results.accuracy}%\n              </p>\n            </div>\n            <div className=\"bg-slate-900 p-4 rounded-lg text-center\">\n              <p className=\"text-slate-400 text-sm\">Time</p>\n              <p className=\"text-2xl font-bold text-purple-400\">\n                {formatTime(results.timeSpent)}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex justify-center space-x-3\">\n            <Button\n              onClick={restartReview}\n              variant=\"outline\"\n              className=\"border-slate-600 text-slate-300\"\n            >\n              <RotateCcw className=\"h-4 w-4 mr-2\" />\n              Review Again\n            </Button>\n            <Button\n              onClick={onExit}\n              className=\"bg-purple-600 hover:bg-purple-700\"\n            >\n              Finish\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const currentQuestion = dueQuestions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / dueQuestions.length) * 100;\n  const isAnswered = userAnswers[currentQuestion.id] !== undefined;\n  const isSubmitted = submittedAnswers[currentQuestionIndex];\n\n  return (\n    <div className=\"max-w-2xl mx-auto space-y-6\">\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <div>\n              <h2 className=\"text-xl font-semibold text-slate-200 flex items-center gap-2\">\n                <Brain className=\"h-5 w-5 text-purple-400\" />\n                SRS Review Mode\n              </h2>\n              <p className=\"text-slate-400 text-sm mt-1\">\n                Spaced repetition optimized learning\n              </p>\n            </div>\n            <Button\n              onClick={onExit}\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"text-slate-400 hover:text-slate-200\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-slate-400\">\n                Question {currentQuestionIndex + 1} of {dueQuestions.length}\n              </span>\n              <Badge\n                variant=\"secondary\"\n                className=\"text-xs bg-purple-900/30 text-purple-400 border-purple-700\"\n              >\n                <TrendingUp className=\"h-3 w-3 mr-1\" />\n                SRS Level {currentQuestion.srs_level || 0}\n              </Badge>\n            </div>\n            <div className=\"flex items-center space-x-2 text-sm text-slate-400\">\n              <Clock className=\"h-4 w-4\" />\n              <span>\n                {formatTime(Math.round((Date.now() - startTime) / 1000))}\n              </span>\n            </div>\n          </div>\n\n          <Progress value={progress} className=\"mb-6\" />\n        </CardContent>\n      </Card>\n\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardContent className=\"p-6\">\n          <h3 className=\"text-lg font-medium text-slate-200 mb-4\">\n            {currentQuestion.question_text}\n          </h3>\n\n          <div className=\"space-y-3\">\n            {/* Question rendering logic (same as QuizPlayer) */}\n            {currentQuestion.type === \"multiple_choice\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  {(currentQuestion.options as any[]).map((option, index) => (\n                    <label\n                      key={index}\n                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${\n                        userAnswers[currentQuestion.id] === option.text\n                          ? isSubmitted\n                            ? option.is_correct\n                              ? \"border-green-500 bg-green-950/20\"\n                              : \"border-red-500 bg-red-950/20\"\n                            : \"border-purple-500 bg-purple-950/20\"\n                          : isSubmitted && option.is_correct\n                          ? \"border-green-500 bg-green-950/20\"\n                          : \"border-slate-600\"\n                      }`}\n                    >\n                      <input\n                        type=\"radio\"\n                        name={`question-${currentQuestion.id}`}\n                        value={option.text}\n                        checked={\n                          userAnswers[currentQuestion.id] === option.text\n                        }\n                        onChange={() =>\n                          !isSubmitted &&\n                          handleAnswerSelect(currentQuestion.id, option.text)\n                        }\n                        className=\"sr-only\"\n                        disabled={isSubmitted}\n                      />\n                      <div\n                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                          userAnswers[currentQuestion.id] === option.text\n                            ? isSubmitted\n                              ? option.is_correct\n                                ? \"border-green-500 bg-green-500\"\n                                : \"border-red-500 bg-red-500\"\n                              : \"border-purple-500 bg-purple-500\"\n                            : isSubmitted && option.is_correct\n                            ? \"border-green-500 bg-green-500\"\n                            : \"border-slate-400\"\n                        }`}\n                      >\n                        {(userAnswers[currentQuestion.id] === option.text ||\n                          (isSubmitted && option.is_correct)) && (\n                          <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                        )}\n                      </div>\n                      <span className=\"text-slate-200\">{option.text}</span>\n                    </label>\n                  ))}\n                </div>\n              )}\n\n            {currentQuestion.type === \"select_all_that_apply\" &&\n              currentQuestion.options && (\n                <div className=\"space-y-3\">\n                  <p className=\"text-sm text-slate-400 mb-3 italic\">\n                    Select all correct answers:\n                  </p>\n                  {(currentQuestion.options as any[]).map((option, index) => {\n                    const isSelected = (\n                      userAnswers[currentQuestion.id] || []\n                    ).includes(option.text);\n                    return (\n                      <label\n                        key={index}\n                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${\n                          isSelected\n                            ? isSubmitted\n                              ? option.is_correct\n                                ? \"border-green-500 bg-green-950/20\"\n                                : \"border-red-500 bg-red-950/20\"\n                              : \"border-purple-500 bg-purple-950/20\"\n                            : isSubmitted && option.is_correct\n                            ? \"border-green-500 bg-green-950/20\"\n                            : \"border-slate-600\"\n                        }`}\n                      >\n                        <input\n                          type=\"checkbox\"\n                          checked={isSelected}\n                          onChange={() =>\n                            !isSubmitted &&\n                            handleSelectAllAnswerToggle(\n                              currentQuestion.id,\n                              option.text\n                            )\n                          }\n                          className=\"sr-only\"\n                          disabled={isSubmitted}\n                        />\n                        <div\n                          className={`w-4 h-4 border-2 mr-3 flex items-center justify-center rounded ${\n                            isSelected\n                              ? isSubmitted\n                                ? option.is_correct\n                                  ? \"border-green-500 bg-green-500\"\n                                  : \"border-red-500 bg-red-500\"\n                                : \"border-purple-500 bg-purple-500\"\n                              : isSubmitted && option.is_correct\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-slate-400\"\n                          }`}\n                        >\n                          {(isSelected ||\n                            (isSubmitted && option.is_correct)) && (\n                            <div className=\"w-2 h-2 bg-white rounded-sm\"></div>\n                          )}\n                        </div>\n                        <span className=\"text-slate-200\">{option.text}</span>\n                      </label>\n                    );\n                  })}\n                </div>\n              )}\n\n            {currentQuestion.type === \"true_false\" && (\n              <div className=\"space-y-3\">\n                {[\"True\", \"False\"].map((val) => (\n                  <label\n                    key={val}\n                    className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${\n                      userAnswers[currentQuestion.id] === val\n                        ? isSubmitted\n                          ? val === currentQuestion.correct_answer\n                            ? \"border-green-500 bg-green-950/20\"\n                            : \"border-red-500 bg-red-950/20\"\n                          : \"border-purple-500 bg-purple-950/20\"\n                        : isSubmitted && val === currentQuestion.correct_answer\n                        ? \"border-green-500 bg-green-950/20\"\n                        : \"border-slate-600\"\n                    }`}\n                  >\n                    <input\n                      type=\"radio\"\n                      name={`question-${currentQuestion.id}`}\n                      value={val}\n                      checked={userAnswers[currentQuestion.id] === val}\n                      onChange={() =>\n                        !isSubmitted &&\n                        handleAnswerSelect(currentQuestion.id, val)\n                      }\n                      className=\"sr-only\"\n                      disabled={isSubmitted}\n                    />\n                    <div\n                      className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                        userAnswers[currentQuestion.id] === val\n                          ? isSubmitted\n                            ? val === currentQuestion.correct_answer\n                              ? \"border-green-500 bg-green-500\"\n                              : \"border-red-500 bg-red-500\"\n                            : \"border-purple-500 bg-purple-500\"\n                          : isSubmitted &&\n                            val === currentQuestion.correct_answer\n                          ? \"border-green-500 bg-green-500\"\n                          : \"border-slate-400\"\n                      }`}\n                    >\n                      {(userAnswers[currentQuestion.id] === val ||\n                        (isSubmitted &&\n                          val === currentQuestion.correct_answer)) && (\n                        <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                      )}\n                    </div>\n                    <span className=\"text-slate-200\">{val}</span>\n                  </label>\n                ))}\n              </div>\n            )}\n\n            {currentQuestion.type === \"short_answer\" && (\n              <div className=\"space-y-3\">\n                <input\n                  type=\"text\"\n                  value={userAnswers[currentQuestion.id] || \"\"}\n                  onChange={(e) =>\n                    !isSubmitted &&\n                    handleAnswerSelect(currentQuestion.id, e.target.value)\n                  }\n                  className={`w-full px-4 py-3 border rounded-lg text-slate-200 placeholder-slate-400 focus:ring-1 outline-none ${\n                    isSubmitted\n                      ? userAnswers[currentQuestion.id]\n                          ?.toLowerCase()\n                          .trim() ===\n                        currentQuestion.correct_answer?.toLowerCase().trim()\n                        ? \"bg-green-950/20 border-green-500 focus:border-green-500 focus:ring-green-500\"\n                        : \"bg-red-950/20 border-red-500 focus:border-red-500 focus:ring-red-500\"\n                      : \"bg-slate-900 border-slate-600 focus:border-purple-500 focus:ring-purple-500\"\n                  }`}\n                  placeholder=\"Type your answer here...\"\n                  disabled={isSubmitted}\n                />\n                {isSubmitted && (\n                  <p className=\"text-sm text-slate-400\">\n                    <span className=\"font-medium\">Correct answer:</span>{\" \"}\n                    {currentQuestion.correct_answer}\n                  </p>\n                )}\n              </div>\n            )}\n          </div>\n\n          {isSubmitted && currentQuestion.explanation && (\n            <div className=\"mt-4 p-4 bg-slate-900 rounded-lg border border-slate-600\">\n              <h4 className=\"text-sm font-medium text-slate-300 mb-2\">\n                Explanation:\n              </h4>\n              <p className=\"text-slate-400 text-sm\">\n                {currentQuestion.explanation}\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      <div className=\"flex justify-center\">\n        {!isSubmitted && isAnswered ? (\n          <Button\n            onClick={handleSubmitAnswer}\n            className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n          >\n            <CheckCircle className=\"h-4 w-4 mr-2\" />\n            Submit Answer\n          </Button>\n        ) : isSubmitted ? (\n          <div className=\"text-center\">\n            <p className=\"text-slate-400 text-sm\">\n              {currentQuestionIndex === dueQuestions.length - 1\n                ? \"Completing review...\"\n                : \"Moving to next question...\"}\n            </p>\n          </div>\n        ) : (\n          <p className=\"text-slate-500 text-sm\">Select an answer to continue</p>\n        )}\n      </div>\n    </div>\n  );\n};\n"}