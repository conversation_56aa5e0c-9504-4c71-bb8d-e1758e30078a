{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/export/ExportSection.tsx"}, "originalCode": "import React from \"react\";\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport {\n  exportFlashcardsAsJSON,\n  exportFlashcardsAsCSV,\n  exportQuizzesAsJSON,\n  exportQuizzesAsCSV,\n} from \"@/lib/storage\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  Download,\n  FileJson,\n  FileSpreadsheet,\n  Info,\n  Layers,\n  FlaskConical,\n  ExternalLink,\n} from \"lucide-react\";\n\nconst ExportSection: React.FC = () => {\n  const { toast } = useToast();\n\n  const commonCardClasses =\n    \"bg-slate-800 border-slate-700 text-slate-100 shadow-md\";\n  const textPrimaryClass = \"text-slate-50\"; // Lighter for primary titles/text\n  const textSecondaryClass = \"text-slate-300\"; // Slightly dimmer for less emphasis\n  const textMutedClass = \"text-slate-400\"; // For muted/description text\n  const accentColor = \"purple-400\";\n  const accentTextClass = `text-${accentColor}`;\n  const accentBorderClass = `border-${accentColor}`;\n  const infoBoxBgClass = \"bg-sky-900/40\"; // Using sky for info to differentiate from purple accent\n  const infoBoxBorderClass = \"border-sky-700\";\n  const infoBoxTextClass = \"text-sky-300\";\n  const infoBoxIconClass = \"text-sky-400\";\n\n  const handleExport = async (\n    exportFn: () => Promise<string>,\n    filename: string\n  ) => {\n    try {\n      const data = await exportFn();\n      const blob = new Blob([data], {\n        type: filename.endsWith(\".csv\") ? \"text/csv\" : \"application/json\",\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      setTimeout(() => {\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n      }, 0);\n      toast({\n        title: \"Export Successful\",\n        description: `Your data has been exported to ${filename}`,\n        className: \"bg-slate-700 border-slate-600 text-slate-200\",\n      });\n    } catch (error) {\n      console.error(\"Export failed:\", error);\n      toast({\n        title: \"Export Failed\",\n        description: \"There was an error exporting your data.\",\n        variant: \"destructive\", // This will use the destructive colors from theme\n      });\n    }\n  };\n\n  const exportItems = [\n    {\n      id: \"flashcards-json\",\n      title: \"Flashcards (JSON)\",\n      description: \"Export all your flashcards with review history.\",\n      icon: FileJson,\n      format: \"JSON\",\n      action: () =>\n        handleExport(exportFlashcardsAsJSON, \"chewyai-flashcards.json\"),\n    },\n    {\n      id: \"flashcards-csv\",\n      title: \"Flashcards (CSV)\",\n      description: \"Compatible with Anki and other SRS tools.\",\n      icon: FileSpreadsheet,\n      format: \"CSV\",\n      action: () =>\n        handleExport(exportFlashcardsAsCSV, \"chewyai-flashcards.csv\"),\n    },\n    {\n      id: \"all-data-json\",\n      title: \"All Data (JSON)\",\n      description: \"Complete backup of all your study materials.\",\n      icon: Layers,\n      format: \"JSON\",\n      action: () =>\n        handleExport(exportFlashcardsAsJSON, \"chewyai-all-data.json\"), // Assuming all data is also JSON for now\n    },\n    {\n      id: \"quizzes-json\",\n      title: \"Quizzes (JSON)\",\n      description: \"Export all your practice quizzes with SRS data.\",\n      icon: FlaskConical,\n      format: \"JSON\",\n      action: () => handleExport(exportQuizzesAsJSON, \"chewyai-quizzes.json\"),\n    },\n    {\n      id: \"quizzes-csv\",\n      title: \"Quizzes (CSV)\",\n      description: \"Compatible with spreadsheet applications.\",\n      icon: FileSpreadsheet,\n      format: \"CSV\",\n      action: () => handleExport(exportQuizzesAsCSV, \"chewyai-quizzes.csv\"),\n    },\n  ];\n\n  return (\n    <Card className={`${commonCardClasses} w-full`}>\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle\n          className={`flex items-center gap-2 text-xl ${textSecondaryClass}`}\n        >\n          <Download className={`h-5 w-5 ${accentTextClass}`} /> Export Data\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent className=\"pt-6 space-y-6\">\n        <p className={`${textMutedClass} text-sm`}>\n          Export your study materials in various formats to use them anywhere or\n          create backups.\n        </p>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {exportItems.map((item) => (\n            <div\n              key={item.id}\n              className={`border border-slate-700 rounded-lg p-4 transition-colors duration-150 \n                          ${\n                            item.disabled\n                              ? \"opacity-50 cursor-not-allowed bg-slate-800/50\"\n                              : \"hover:border-purple-500 hover:bg-slate-700/50 cursor-pointer\"\n                          }`}\n              onClick={!item.disabled ? item.action : undefined}\n              role={item.disabled ? undefined : \"button\"}\n              tabIndex={item.disabled ? -1 : 0}\n              onKeyDown={(e) => {\n                if (!item.disabled && (e.key === \"Enter\" || e.key === \" \")) {\n                  item.action();\n                }\n              }}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <item.icon className={`${accentTextClass} h-6 w-6`} />\n                <span\n                  className={`text-xs font-medium ${textMutedClass} bg-slate-700 px-2 py-0.5 rounded`}\n                >\n                  {item.format}\n                </span>\n              </div>\n              <h3 className={`font-medium ${textSecondaryClass} mb-1`}>\n                {item.title}\n              </h3>\n              <p className={`${textMutedClass} text-sm`}>{item.description}</p>\n            </div>\n          ))}\n        </div>\n\n        <div\n          className={`${infoBoxBgClass} p-4 rounded-md ${infoBoxBorderClass} border`}\n        >\n          <div className=\"flex items-start gap-3\">\n            <Info\n              className={`${infoBoxIconClass} h-5 w-5 mt-0.5 flex-shrink-0`}\n            />\n            <p className={`${infoBoxTextClass} text-sm`}>\n              Your study data is stored in your browser. Exporting creates a\n              backup you can use to restore your data or transfer it to another\n              device. We recommend creating backups regularly.\n            </p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ExportSection;\n", "modifiedCode": "import React from \"react\";\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport {\n  exportFlashcardsAsJSON,\n  exportFlashcardsAsCSV,\n  exportQuizzesAsJSON,\n  exportQuizzesAsCSV,\n} from \"@/lib/storage\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  Download,\n  FileJson,\n  FileSpreadsheet,\n  Info,\n  Layers,\n  FlaskConical,\n  ExternalLink,\n} from \"lucide-react\";\n\nconst ExportSection: React.FC = () => {\n  const { toast } = useToast();\n\n  const commonCardClasses =\n    \"bg-slate-800 border-slate-700 text-slate-100 shadow-md\";\n  const textPrimaryClass = \"text-slate-50\"; // Lighter for primary titles/text\n  const textSecondaryClass = \"text-slate-300\"; // Slightly dimmer for less emphasis\n  const textMutedClass = \"text-slate-400\"; // For muted/description text\n  const accentColor = \"purple-400\";\n  const accentTextClass = `text-${accentColor}`;\n  const accentBorderClass = `border-${accentColor}`;\n  const infoBoxBgClass = \"bg-sky-900/40\"; // Using sky for info to differentiate from purple accent\n  const infoBoxBorderClass = \"border-sky-700\";\n  const infoBoxTextClass = \"text-sky-300\";\n  const infoBoxIconClass = \"text-sky-400\";\n\n  const handleExport = async (\n    exportFn: () => Promise<string>,\n    filename: string\n  ) => {\n    try {\n      const data = await exportFn();\n      const blob = new Blob([data], {\n        type: filename.endsWith(\".csv\") ? \"text/csv\" : \"application/json\",\n      });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = filename;\n      document.body.appendChild(a);\n      a.click();\n      setTimeout(() => {\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n      }, 0);\n      toast({\n        title: \"Export Successful\",\n        description: `Your data has been exported to ${filename}`,\n        className: \"bg-slate-700 border-slate-600 text-slate-200\",\n      });\n    } catch (error) {\n      console.error(\"Export failed:\", error);\n      toast({\n        title: \"Export Failed\",\n        description: \"There was an error exporting your data.\",\n        variant: \"destructive\", // This will use the destructive colors from theme\n      });\n    }\n  };\n\n  const exportItems = [\n    {\n      id: \"flashcards-json\",\n      title: \"Flashcards (JSON)\",\n      description: \"Export all your flashcards with review history.\",\n      icon: FileJson,\n      format: \"JSON\",\n      action: () =>\n        handleExport(exportFlashcardsAsJSON, \"chewyai-flashcards.json\"),\n    },\n    {\n      id: \"flashcards-csv\",\n      title: \"Flashcards (CSV)\",\n      description: \"Compatible with Anki and other SRS tools.\",\n      icon: FileSpreadsheet,\n      format: \"CSV\",\n      action: () =>\n        handleExport(exportFlashcardsAsCSV, \"chewyai-flashcards.csv\"),\n    },\n    {\n      id: \"all-data-json\",\n      title: \"All Data (JSON)\",\n      description: \"Complete backup of all your study materials.\",\n      icon: Layers,\n      format: \"JSON\",\n      action: () =>\n        handleExport(exportFlashcardsAsJSON, \"chewyai-all-data.json\"), // Assuming all data is also JSON for now\n    },\n    {\n      id: \"quizzes-json\",\n      title: \"Quizzes (JSON)\",\n      description: \"Export all your practice quizzes with SRS data.\",\n      icon: FlaskConical,\n      format: \"JSON\",\n      action: () => handleExport(exportQuizzesAsJSON, \"chewyai-quizzes.json\"),\n    },\n    {\n      id: \"quizzes-csv\",\n      title: \"Quizzes (CSV)\",\n      description: \"Compatible with spreadsheet applications.\",\n      icon: FileSpreadsheet,\n      format: \"CSV\",\n      action: () => handleExport(exportQuizzesAsCSV, \"chewyai-quizzes.csv\"),\n    },\n  ];\n\n  return (\n    <Card className={`${commonCardClasses} w-full`}>\n      <CardHeader className=\"border-b border-slate-700\">\n        <CardTitle\n          className={`flex items-center gap-2 text-xl ${textSecondaryClass}`}\n        >\n          <Download className={`h-5 w-5 ${accentTextClass}`} /> Export Data\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent className=\"pt-6 space-y-6\">\n        <p className={`${textMutedClass} text-sm`}>\n          Export your study materials in various formats to use them anywhere or\n          create backups.\n        </p>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {exportItems.map((item) => (\n            <div\n              key={item.id}\n              className={`border border-slate-700 rounded-lg p-4 transition-colors duration-150 \n                          ${\n                            item.disabled\n                              ? \"opacity-50 cursor-not-allowed bg-slate-800/50\"\n                              : \"hover:border-purple-500 hover:bg-slate-700/50 cursor-pointer\"\n                          }`}\n              onClick={!item.disabled ? item.action : undefined}\n              role={item.disabled ? undefined : \"button\"}\n              tabIndex={item.disabled ? -1 : 0}\n              onKeyDown={(e) => {\n                if (!item.disabled && (e.key === \"Enter\" || e.key === \" \")) {\n                  item.action();\n                }\n              }}\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <item.icon className={`${accentTextClass} h-6 w-6`} />\n                <span\n                  className={`text-xs font-medium ${textMutedClass} bg-slate-700 px-2 py-0.5 rounded`}\n                >\n                  {item.format}\n                </span>\n              </div>\n              <h3 className={`font-medium ${textSecondaryClass} mb-1`}>\n                {item.title}\n              </h3>\n              <p className={`${textMutedClass} text-sm`}>{item.description}</p>\n            </div>\n          ))}\n        </div>\n\n        <div\n          className={`${infoBoxBgClass} p-4 rounded-md ${infoBoxBorderClass} border`}\n        >\n          <div className=\"flex items-start gap-3\">\n            <Info\n              className={`${infoBoxIconClass} h-5 w-5 mt-0.5 flex-shrink-0`}\n            />\n            <p className={`${infoBoxTextClass} text-sm`}>\n              Your study data is stored in your browser. Exporting creates a\n              backup you can use to restore your data or transfer it to another\n              device. We recommend creating backups regularly.\n            </p>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ExportSection;\n"}