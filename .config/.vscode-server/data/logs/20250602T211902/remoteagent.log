2025-06-02 21:19:03.299 [info] 




2025-06-02 21:19:03.315 [info] Extension host agent started.
2025-06-02 21:19:03.547 [info] [<unknown>][3d386ffd][ExtensionHostConnection] New connection established.
2025-06-02 21:19:03.548 [info] [<unknown>][eb08b616][ManagementConnection] New connection established.
2025-06-02 21:19:03.659 [info] [<unknown>][3d386ffd][ExtensionHostConnection] <248> Launched Extension Host Process.
2025-06-02 21:19:03.922 [info] ComputeTargetPlatform: linux-x64
2025-06-02 21:19:07.917 [info] ComputeTargetPlatform: linux-x64
2025-06-02 21:24:03.315 [info] New EH opened, aborting shutdown
2025-06-02 22:33:57.971 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 22:33:58.237 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 22:42:45.244 [info] Getting Manifest... saoudrizwan.claude-dev
2025-06-02 22:42:45.326 [info] Installing extension: saoudrizwan.claude-dev {"installPreReleaseVersion":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"isApplicationScoped":false,"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"},"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"}}
2025-06-02 22:42:47.223 [info] Extension signature verification result for saoudrizwan.claude-dev: Success. Internal Code: 0. Executed: true. Duration: 1345ms.
2025-06-02 22:42:48.665 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.8: saoudrizwan.claude-dev
2025-06-02 22:42:48.729 [info] Renamed to /home/<USER>/.vscode-server/extensions/saoudrizwan.claude-dev-3.17.8
2025-06-02 22:42:48.756 [info] Extension installed successfully: saoudrizwan.claude-dev file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 22:55:09.979 [info] [<unknown>][e10901ef][ExtensionHostConnection] New connection established.
2025-06-02 22:55:09.980 [info] [<unknown>][73da6758][ManagementConnection] New connection established.
2025-06-02 22:55:09.986 [info] [<unknown>][e10901ef][ExtensionHostConnection] <15207> Launched Extension Host Process.
2025-06-02 22:55:36.424 [info] [<unknown>][73da6758][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-02 22:55:36.578 [info] [<unknown>][e10901ef][ExtensionHostConnection] <15207> Extension Host Process exited with code: 0, signal: null.
2025-06-02 22:55:38.972 [info] [<unknown>][2c519277][ExtensionHostConnection] New connection established.
2025-06-02 22:55:38.973 [info] [<unknown>][9663b016][ManagementConnection] New connection established.
2025-06-02 22:55:38.977 [info] [<unknown>][2c519277][ExtensionHostConnection] <15728> Launched Extension Host Process.
2025-06-02 23:01:25.391 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 23:01:37.442 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-02 23:38:16.882 [info] [<unknown>][eb08b616][ManagementConnection] The client has reconnected.
2025-06-02 23:38:16.938 [info] [<unknown>][3d386ffd][ExtensionHostConnection] The client has reconnected.
2025-06-02 23:38:17.953 [error] CodeExpectedError: Could not find pty 49 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6392)
    at I.updateIcon (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1889)
    at s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ul.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81207)
    at ul.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80730)
    at ps.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80132)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.x (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29911)
    at process.emit (node:events:524:28)
    at emit (node:internal/child_process:950:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
2025-06-02 23:38:17.954 [error] CodeExpectedError: Could not find pty 49 on pty host
    at I.W (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6392)
    at I.updateTitle (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at s.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2962)
    at Object.call (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ul.s (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81207)
    at ul.q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80730)
    at ps.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80132)
    at C.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at C.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.x (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js:24:29911)
    at process.emit (node:events:524:28)
    at emit (node:internal/child_process:950:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:83:21)
