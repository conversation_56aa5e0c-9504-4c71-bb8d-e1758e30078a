2025-06-02 21:19:12.681 [info] [main] Log level: Info
2025-06-02 21:19:12.681 [info] [main] Validating found git in: "git"
2025-06-02 21:19:12.681 [info] [main] Using git "2.47.2" from "git"
2025-06-02 21:19:12.681 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 21:19:12.681 [info] > git rev-parse --show-toplevel [89ms]
2025-06-02 21:19:12.681 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-02 21:19:12.681 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 21:19:12.681 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 21:19:12.681 [info] > git rev-parse --show-toplevel [113ms]
2025-06-02 21:19:12.681 [info] > git config --get commit.template [121ms]
2025-06-02 21:19:12.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [297ms]
2025-06-02 21:19:12.681 [info] > git check-ignore -v -z --stdin [276ms]
2025-06-02 21:19:12.683 [info] > git status -z -uall [24ms]
2025-06-02 21:19:12.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-06-02 21:19:12.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [130ms]
2025-06-02 21:19:12.862 [info] > git rev-parse --show-toplevel [180ms]
2025-06-02 21:19:12.898 [info] > git config --get --local branch.main.vscode-merge-base [37ms]
2025-06-02 21:19:12.905 [info] > git config --get commit.template [158ms]
2025-06-02 21:19:13.509 [info] > git rev-parse --show-toplevel [623ms]
2025-06-02 21:19:13.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [946ms]
2025-06-02 21:19:13.918 [info] > git merge-base refs/heads/main refs/remotes/origin/main [58ms]
2025-06-02 21:19:13.926 [info] > git rev-parse --show-toplevel [375ms]
2025-06-02 21:19:13.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [391ms]
2025-06-02 21:19:14.286 [info] > git rev-parse --show-toplevel [9ms]
2025-06-02 21:19:14.286 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [360ms]
2025-06-02 21:19:14.340 [info] > git status -z -uall [37ms]
2025-06-02 21:19:14.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [31ms]
2025-06-02 21:19:14.344 [info] > git rev-parse --show-toplevel [48ms]
2025-06-02 21:19:14.365 [info] > git rev-parse --show-toplevel [13ms]
2025-06-02 21:19:14.379 [info] > git rev-parse --show-toplevel [6ms]
2025-06-02 21:19:14.773 [info] > git rev-parse --show-toplevel [155ms]
2025-06-02 21:19:15.135 [info] > git rev-parse --show-toplevel [243ms]
2025-06-02 21:19:15.209 [info] > git rev-parse --show-toplevel [50ms]
2025-06-02 21:19:15.243 [info] > git rev-parse --show-toplevel [21ms]
2025-06-02 21:19:15.353 [info] > git rev-parse --show-toplevel [94ms]
2025-06-02 21:19:15.951 [info] > git rev-parse --show-toplevel [118ms]
2025-06-02 21:19:16.080 [info] > git rev-parse --show-toplevel [117ms]
2025-06-02 21:19:16.082 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 21:19:16.244 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [149ms]
2025-06-02 21:19:16.244 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [138ms]
2025-06-02 21:19:16.513 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [256ms]
2025-06-02 21:19:16.524 [info] > git config --get commit.template [292ms]
2025-06-02 21:19:16.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 21:19:17.189 [info] > git status -z -uall [90ms]
2025-06-02 21:19:17.190 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [84ms]
2025-06-02 21:19:17.485 [info] > git config --get --local branch.main.github-pr-owner-number [265ms]
2025-06-02 21:19:17.485 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 21:19:21.269 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ai/AIConfigurationSection.tsx [3514ms]
2025-06-02 21:20:03.774 [info] > git config --get commit.template [1ms]
2025-06-02 21:20:03.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 21:20:03.826 [info] > git status -z -uall [12ms]
2025-06-02 21:20:03.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 21:20:04.051 [info] > git fetch [302ms]
2025-06-02 21:20:04.051 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 21:20:04.064 [info] > git config --get commit.template [0ms]
2025-06-02 21:20:04.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 21:20:04.097 [info] > git status -z -uall [10ms]
2025-06-02 21:20:04.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 21:20:05.238 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [6ms]
2025-06-02 21:20:05.251 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [2ms]
2025-06-02 21:20:05.357 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [9ms]
2025-06-02 21:20:24.356 [info] > git config --get commit.template [4ms]
2025-06-02 21:20:24.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 21:20:24.366 [info] > git status -z -uall [5ms]
2025-06-02 21:20:24.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 21:20:35.160 [info] > git config --get commit.template [2ms]
2025-06-02 21:20:35.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 21:20:35.196 [info] > git status -z -uall [9ms]
2025-06-02 21:20:35.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 21:20:43.631 [info] > git config --get commit.template [20ms]
2025-06-02 21:20:43.633 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 21:20:43.664 [info] > git status -z -uall [15ms]
2025-06-02 21:20:43.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 21:21:12.007 [info] > git config --get commit.template [6ms]
2025-06-02 21:21:12.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 21:21:12.020 [info] > git status -z -uall [7ms]
2025-06-02 21:21:12.022 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 21:21:17.059 [info] > git config --get commit.template [20ms]
2025-06-02 21:21:17.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 21:21:17.089 [info] > git status -z -uall [13ms]
2025-06-02 21:21:17.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 21:21:22.108 [info] > git config --get commit.template [5ms]
2025-06-02 21:21:22.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 21:21:22.119 [info] > git status -z -uall [5ms]
2025-06-02 21:21:22.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 21:22:04.259 [info] > git config --get commit.template [6ms]
2025-06-02 21:22:04.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 21:22:04.270 [info] > git status -z -uall [5ms]
2025-06-02 21:22:04.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:21:13.133 [info] > git config --get commit.template [17ms]
2025-06-02 22:21:13.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 22:21:13.167 [info] > git status -z -uall [15ms]
2025-06-02 22:21:13.169 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 22:22:25.953 [info] > git config --get commit.template [12ms]
2025-06-02 22:22:25.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:22:25.977 [info] > git status -z -uall [11ms]
2025-06-02 22:22:25.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:22:47.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:22:47.322 [info] > git config --get commit.template [28ms]
2025-06-02 22:22:47.399 [info] > git status -z -uall [33ms]
2025-06-02 22:22:47.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:31:42.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:31:42.153 [info] > git config --get commit.template [14ms]
2025-06-02 22:31:42.177 [info] > git status -z -uall [11ms]
2025-06-02 22:31:42.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:32:16.342 [info] > git config --get commit.template [25ms]
2025-06-02 22:32:16.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:32:16.396 [info] > git status -z -uall [30ms]
2025-06-02 22:32:16.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:32:21.450 [info] > git config --get commit.template [29ms]
2025-06-02 22:32:21.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:32:21.491 [info] > git status -z -uall [19ms]
2025-06-02 22:32:21.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:32:26.526 [info] > git config --get commit.template [2ms]
2025-06-02 22:32:26.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 22:32:26.590 [info] > git status -z -uall [22ms]
2025-06-02 22:32:26.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:32:31.639 [info] > git config --get commit.template [21ms]
2025-06-02 22:32:31.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:32:31.686 [info] > git status -z -uall [25ms]
2025-06-02 22:32:31.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:32:36.767 [info] > git config --get commit.template [61ms]
2025-06-02 22:32:36.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [52ms]
2025-06-02 22:32:36.795 [info] > git status -z -uall [11ms]
2025-06-02 22:32:36.795 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:32:41.873 [info] > git config --get commit.template [41ms]
2025-06-02 22:32:41.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 22:32:41.933 [info] > git status -z -uall [30ms]
2025-06-02 22:32:41.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 22:32:46.973 [info] > git config --get commit.template [12ms]
2025-06-02 22:32:46.974 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:32:46.999 [info] > git status -z -uall [13ms]
2025-06-02 22:32:47.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:32:52.065 [info] > git config --get commit.template [29ms]
2025-06-02 22:32:52.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 22:32:52.113 [info] > git status -z -uall [21ms]
2025-06-02 22:32:52.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-02 22:32:57.164 [info] > git config --get commit.template [15ms]
2025-06-02 22:32:57.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:32:57.202 [info] > git status -z -uall [19ms]
2025-06-02 22:32:57.206 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:33:04.805 [info] > git config --get commit.template [2ms]
2025-06-02 22:33:04.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:33:04.880 [info] > git status -z -uall [31ms]
2025-06-02 22:33:04.882 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:33:27.553 [info] > git config --get commit.template [16ms]
2025-06-02 22:33:27.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:33:27.592 [info] > git status -z -uall [20ms]
2025-06-02 22:33:27.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:33:52.568 [info] > git config --get commit.template [16ms]
2025-06-02 22:33:52.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:33:52.605 [info] > git status -z -uall [14ms]
2025-06-02 22:33:52.606 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:33:58.474 [info] > git config --get commit.template [12ms]
2025-06-02 22:33:58.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:33:58.501 [info] > git status -z -uall [13ms]
2025-06-02 22:33:58.501 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:34:03.531 [info] > git config --get commit.template [11ms]
2025-06-02 22:34:03.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:34:03.566 [info] > git status -z -uall [19ms]
2025-06-02 22:34:03.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:34:08.608 [info] > git config --get commit.template [20ms]
2025-06-02 22:34:08.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:34:08.651 [info] > git status -z -uall [21ms]
2025-06-02 22:34:08.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:34:58.511 [info] > git config --get commit.template [15ms]
2025-06-02 22:34:58.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:34:58.535 [info] > git status -z -uall [10ms]
2025-06-02 22:34:58.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:35:07.176 [info] > git config --get commit.template [11ms]
2025-06-02 22:35:07.177 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:35:07.199 [info] > git status -z -uall [11ms]
2025-06-02 22:35:07.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:35:12.241 [info] > git config --get commit.template [18ms]
2025-06-02 22:35:12.242 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:35:12.265 [info] > git status -z -uall [12ms]
2025-06-02 22:35:12.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:35:47.371 [info] > git config --get commit.template [2ms]
2025-06-02 22:35:47.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:35:47.403 [info] > git status -z -uall [10ms]
2025-06-02 22:35:47.404 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:35:52.439 [info] > git config --get commit.template [16ms]
2025-06-02 22:35:52.440 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:35:52.470 [info] > git status -z -uall [14ms]
2025-06-02 22:35:52.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:35:57.504 [info] > git config --get commit.template [15ms]
2025-06-02 22:35:57.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:35:57.533 [info] > git status -z -uall [14ms]
2025-06-02 22:35:57.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:36:02.569 [info] > git config --get commit.template [12ms]
2025-06-02 22:36:02.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:36:02.592 [info] > git status -z -uall [9ms]
2025-06-02 22:36:02.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:36:07.628 [info] > git config --get commit.template [14ms]
2025-06-02 22:36:07.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:36:07.729 [info] > git status -z -uall [81ms]
2025-06-02 22:36:07.730 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-06-02 22:36:12.807 [info] > git config --get commit.template [56ms]
2025-06-02 22:36:12.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 22:36:12.871 [info] > git status -z -uall [21ms]
2025-06-02 22:36:12.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 22:36:17.911 [info] > git config --get commit.template [3ms]
2025-06-02 22:36:17.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:36:17.973 [info] > git status -z -uall [20ms]
2025-06-02 22:36:17.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:36:23.009 [info] > git config --get commit.template [14ms]
2025-06-02 22:36:23.009 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:36:23.035 [info] > git status -z -uall [11ms]
2025-06-02 22:36:23.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:36:28.066 [info] > git config --get commit.template [9ms]
2025-06-02 22:36:28.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:36:28.086 [info] > git status -z -uall [10ms]
2025-06-02 22:36:28.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:36:33.117 [info] > git config --get commit.template [11ms]
2025-06-02 22:36:33.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:36:33.135 [info] > git status -z -uall [8ms]
2025-06-02 22:36:33.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:36:38.171 [info] > git config --get commit.template [14ms]
2025-06-02 22:36:38.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:36:38.204 [info] > git status -z -uall [16ms]
2025-06-02 22:36:38.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:36:43.268 [info] > git config --get commit.template [32ms]
2025-06-02 22:36:43.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:36:43.315 [info] > git status -z -uall [18ms]
2025-06-02 22:36:43.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:36:48.348 [info] > git config --get commit.template [9ms]
2025-06-02 22:36:48.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:36:48.368 [info] > git status -z -uall [8ms]
2025-06-02 22:36:48.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:36:53.398 [info] > git config --get commit.template [10ms]
2025-06-02 22:36:53.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:36:53.421 [info] > git status -z -uall [10ms]
2025-06-02 22:36:53.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:36:58.451 [info] > git config --get commit.template [11ms]
2025-06-02 22:36:58.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:36:58.471 [info] > git status -z -uall [9ms]
2025-06-02 22:36:58.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:37:03.512 [info] > git config --get commit.template [19ms]
2025-06-02 22:37:03.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:37:03.543 [info] > git status -z -uall [15ms]
2025-06-02 22:37:03.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:37:09.539 [info] > git config --get commit.template [29ms]
2025-06-02 22:37:09.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:37:09.580 [info] > git status -z -uall [17ms]
2025-06-02 22:37:09.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:37:15.579 [info] > git config --get commit.template [14ms]
2025-06-02 22:37:15.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:37:15.602 [info] > git status -z -uall [13ms]
2025-06-02 22:37:15.605 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:37:20.639 [info] > git config --get commit.template [16ms]
2025-06-02 22:37:20.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:37:20.664 [info] > git status -z -uall [11ms]
2025-06-02 22:37:20.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:37:25.686 [info] > git config --get commit.template [1ms]
2025-06-02 22:37:25.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:37:25.737 [info] > git status -z -uall [18ms]
2025-06-02 22:37:25.738 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:37:30.779 [info] > git config --get commit.template [17ms]
2025-06-02 22:37:30.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:37:30.848 [info] > git status -z -uall [56ms]
2025-06-02 22:37:30.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [47ms]
2025-06-02 22:37:53.711 [info] > git config --get commit.template [30ms]
2025-06-02 22:37:53.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:37:53.760 [info] > git status -z -uall [22ms]
2025-06-02 22:37:53.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:37:58.793 [info] > git config --get commit.template [9ms]
2025-06-02 22:37:58.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:37:58.835 [info] > git status -z -uall [22ms]
2025-06-02 22:37:58.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:38:04.376 [info] > git config --get commit.template [2ms]
2025-06-02 22:38:04.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:38:04.442 [info] > git status -z -uall [23ms]
2025-06-02 22:38:04.444 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:38:09.480 [info] > git config --get commit.template [15ms]
2025-06-02 22:38:09.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:09.505 [info] > git status -z -uall [11ms]
2025-06-02 22:38:09.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 22:38:14.554 [info] > git config --get commit.template [19ms]
2025-06-02 22:38:14.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:14.593 [info] > git status -z -uall [20ms]
2025-06-02 22:38:14.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 22:38:20.374 [info] > git config --get commit.template [4ms]
2025-06-02 22:38:20.402 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:38:20.440 [info] > git status -z -uall [16ms]
2025-06-02 22:38:20.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:38:25.473 [info] > git config --get commit.template [10ms]
2025-06-02 22:38:25.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:25.496 [info] > git status -z -uall [11ms]
2025-06-02 22:38:25.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:38:30.537 [info] > git config --get commit.template [16ms]
2025-06-02 22:38:30.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:30.566 [info] > git status -z -uall [12ms]
2025-06-02 22:38:30.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:38:36.387 [info] > git config --get commit.template [1ms]
2025-06-02 22:38:36.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:36.457 [info] > git status -z -uall [30ms]
2025-06-02 22:38:36.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:38:41.489 [info] > git config --get commit.template [3ms]
2025-06-02 22:38:41.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:41.551 [info] > git status -z -uall [20ms]
2025-06-02 22:38:41.552 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:38:46.582 [info] > git config --get commit.template [2ms]
2025-06-02 22:38:46.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:38:46.675 [info] > git status -z -uall [34ms]
2025-06-02 22:38:46.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 22:38:51.723 [info] > git config --get commit.template [16ms]
2025-06-02 22:38:51.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:38:51.763 [info] > git status -z -uall [16ms]
2025-06-02 22:38:51.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:38:56.801 [info] > git config --get commit.template [11ms]
2025-06-02 22:38:56.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:38:56.829 [info] > git status -z -uall [13ms]
2025-06-02 22:38:56.830 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:39:01.869 [info] > git config --get commit.template [17ms]
2025-06-02 22:39:01.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:39:01.925 [info] > git status -z -uall [21ms]
2025-06-02 22:39:01.926 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:39:06.959 [info] > git config --get commit.template [7ms]
2025-06-02 22:39:06.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:39:07.033 [info] > git status -z -uall [19ms]
2025-06-02 22:39:07.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:39:12.082 [info] > git config --get commit.template [22ms]
2025-06-02 22:39:12.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 22:39:12.116 [info] > git status -z -uall [17ms]
2025-06-02 22:39:12.117 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:39:17.184 [info] > git config --get commit.template [30ms]
2025-06-02 22:39:17.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:39:17.228 [info] > git status -z -uall [20ms]
2025-06-02 22:39:17.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:39:22.289 [info] > git config --get commit.template [6ms]
2025-06-02 22:39:22.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 22:39:22.406 [info] > git status -z -uall [31ms]
2025-06-02 22:39:22.409 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:40:22.319 [info] > git config --get commit.template [13ms]
2025-06-02 22:40:22.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:40:22.356 [info] > git status -z -uall [20ms]
2025-06-02 22:40:22.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:40:27.388 [info] > git config --get commit.template [12ms]
2025-06-02 22:40:27.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:40:27.414 [info] > git status -z -uall [12ms]
2025-06-02 22:40:27.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:40:32.448 [info] > git config --get commit.template [15ms]
2025-06-02 22:40:32.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:40:32.472 [info] > git status -z -uall [12ms]
2025-06-02 22:40:32.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:40:37.505 [info] > git config --get commit.template [12ms]
2025-06-02 22:40:37.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:40:37.532 [info] > git status -z -uall [14ms]
2025-06-02 22:40:37.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:40:42.561 [info] > git config --get commit.template [9ms]
2025-06-02 22:40:42.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:40:42.587 [info] > git status -z -uall [10ms]
2025-06-02 22:40:42.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:40:47.618 [info] > git config --get commit.template [12ms]
2025-06-02 22:40:47.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:40:47.641 [info] > git status -z -uall [11ms]
2025-06-02 22:40:47.643 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:40:54.701 [info] > git config --get commit.template [12ms]
2025-06-02 22:40:54.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:40:54.726 [info] > git status -z -uall [11ms]
2025-06-02 22:40:54.727 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:40:59.759 [info] > git config --get commit.template [12ms]
2025-06-02 22:40:59.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:40:59.781 [info] > git status -z -uall [12ms]
2025-06-02 22:40:59.782 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:41:04.814 [info] > git config --get commit.template [13ms]
2025-06-02 22:41:04.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:41:04.839 [info] > git status -z -uall [11ms]
2025-06-02 22:41:04.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:41:09.873 [info] > git config --get commit.template [13ms]
2025-06-02 22:41:09.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:41:09.902 [info] > git status -z -uall [12ms]
2025-06-02 22:41:09.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:41:14.940 [info] > git config --get commit.template [13ms]
2025-06-02 22:41:14.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:41:14.961 [info] > git status -z -uall [11ms]
2025-06-02 22:41:14.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:41:20.007 [info] > git config --get commit.template [22ms]
2025-06-02 22:41:20.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:41:20.035 [info] > git status -z -uall [12ms]
2025-06-02 22:41:20.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:41:25.074 [info] > git config --get commit.template [16ms]
2025-06-02 22:41:25.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:41:25.130 [info] > git status -z -uall [28ms]
2025-06-02 22:41:25.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:41:30.159 [info] > git config --get commit.template [9ms]
2025-06-02 22:41:30.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:41:30.187 [info] > git status -z -uall [15ms]
2025-06-02 22:41:30.188 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:41:35.223 [info] > git config --get commit.template [13ms]
2025-06-02 22:41:35.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:41:35.245 [info] > git status -z -uall [11ms]
2025-06-02 22:41:35.246 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:41:40.278 [info] > git config --get commit.template [15ms]
2025-06-02 22:41:40.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:41:40.312 [info] > git status -z -uall [12ms]
2025-06-02 22:41:40.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:41:45.339 [info] > git config --get commit.template [9ms]
2025-06-02 22:41:45.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:41:45.362 [info] > git status -z -uall [10ms]
2025-06-02 22:41:45.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:41:50.406 [info] > git config --get commit.template [18ms]
2025-06-02 22:41:50.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:41:50.429 [info] > git status -z -uall [10ms]
2025-06-02 22:41:50.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:41:55.461 [info] > git config --get commit.template [14ms]
2025-06-02 22:41:55.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:41:55.502 [info] > git status -z -uall [25ms]
2025-06-02 22:41:55.503 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:42:00.532 [info] > git config --get commit.template [12ms]
2025-06-02 22:42:00.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:42:00.556 [info] > git status -z -uall [10ms]
2025-06-02 22:42:00.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:42:05.595 [info] > git config --get commit.template [16ms]
2025-06-02 22:42:05.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:42:05.629 [info] > git status -z -uall [16ms]
2025-06-02 22:42:05.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:42:10.738 [info] > git config --get commit.template [34ms]
2025-06-02 22:42:10.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 22:42:10.787 [info] > git status -z -uall [24ms]
2025-06-02 22:42:10.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 22:42:17.674 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 22:42:18.603 [info] > git config --get commit.template [33ms]
2025-06-02 22:42:18.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:42:18.652 [info] > git status -z -uall [27ms]
2025-06-02 22:42:18.653 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:42:25.566 [info] > git config --get commit.template [11ms]
2025-06-02 22:42:25.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:42:25.594 [info] > git status -z -uall [14ms]
2025-06-02 22:42:25.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:42:30.628 [info] > git config --get commit.template [12ms]
2025-06-02 22:42:30.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:42:30.654 [info] > git status -z -uall [13ms]
2025-06-02 22:42:30.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:42:31.126 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [148ms]
2025-06-02 22:42:31.535 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 22:42:35.708 [info] > git config --get commit.template [23ms]
2025-06-02 22:42:35.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:42:35.738 [info] > git status -z -uall [14ms]
2025-06-02 22:42:35.740 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:42:40.778 [info] > git config --get commit.template [17ms]
2025-06-02 22:42:40.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:42:40.818 [info] > git status -z -uall [22ms]
2025-06-02 22:42:40.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:42:45.882 [info] > git config --get commit.template [20ms]
2025-06-02 22:42:45.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:42:45.938 [info] > git status -z -uall [36ms]
2025-06-02 22:42:45.940 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:42:51.629 [info] > git config --get commit.template [3ms]
2025-06-02 22:42:51.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-02 22:42:51.761 [info] > git status -z -uall [28ms]
2025-06-02 22:42:51.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 22:42:59.654 [info] > git config --get commit.template [11ms]
2025-06-02 22:42:59.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:42:59.680 [info] > git status -z -uall [13ms]
2025-06-02 22:42:59.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:43:04.711 [info] > git config --get commit.template [11ms]
2025-06-02 22:43:04.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:43:04.746 [info] > git status -z -uall [17ms]
2025-06-02 22:43:04.747 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:43:09.779 [info] > git config --get commit.template [13ms]
2025-06-02 22:43:09.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:43:09.811 [info] > git status -z -uall [13ms]
2025-06-02 22:43:09.812 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:43:16.666 [info] > git config --get commit.template [11ms]
2025-06-02 22:43:16.666 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:43:16.688 [info] > git status -z -uall [11ms]
2025-06-02 22:43:16.690 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:43:36.655 [info] > git config --get commit.template [13ms]
2025-06-02 22:43:36.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 22:43:36.684 [info] > git status -z -uall [13ms]
2025-06-02 22:43:36.684 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:44:02.624 [info] > git config --get commit.template [16ms]
2025-06-02 22:44:02.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:44:02.670 [info] > git status -z -uall [27ms]
2025-06-02 22:44:02.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 22:44:07.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:44:07.724 [info] > git config --get commit.template [32ms]
2025-06-02 22:44:07.764 [info] > git status -z -uall [21ms]
2025-06-02 22:44:07.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:44:16.826 [info] > git config --get commit.template [13ms]
2025-06-02 22:44:16.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:44:16.856 [info] > git status -z -uall [13ms]
2025-06-02 22:44:16.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:44:21.889 [info] > git config --get commit.template [13ms]
2025-06-02 22:44:21.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:44:21.913 [info] > git status -z -uall [11ms]
2025-06-02 22:44:21.914 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:44:26.950 [info] > git config --get commit.template [12ms]
2025-06-02 22:44:26.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:44:26.981 [info] > git status -z -uall [15ms]
2025-06-02 22:44:26.982 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:44:32.018 [info] > git config --get commit.template [16ms]
2025-06-02 22:44:32.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:44:32.044 [info] > git status -z -uall [12ms]
2025-06-02 22:44:32.044 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:44:37.076 [info] > git config --get commit.template [12ms]
2025-06-02 22:44:37.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:44:37.101 [info] > git status -z -uall [13ms]
2025-06-02 22:44:37.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:44:42.139 [info] > git config --get commit.template [17ms]
2025-06-02 22:44:42.140 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:44:42.164 [info] > git status -z -uall [11ms]
2025-06-02 22:44:42.166 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:44:47.198 [info] > git config --get commit.template [12ms]
2025-06-02 22:44:47.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:44:47.223 [info] > git status -z -uall [11ms]
2025-06-02 22:44:47.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:44:52.263 [info] > git config --get commit.template [20ms]
2025-06-02 22:44:52.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:44:52.295 [info] > git status -z -uall [15ms]
2025-06-02 22:44:52.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:44:57.359 [info] > git config --get commit.template [27ms]
2025-06-02 22:44:57.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:44:57.419 [info] > git status -z -uall [32ms]
2025-06-02 22:44:57.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:45:04.273 [info] > git config --get commit.template [20ms]
2025-06-02 22:45:04.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:45:04.315 [info] > git status -z -uall [20ms]
2025-06-02 22:45:04.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:45:09.356 [info] > git config --get commit.template [19ms]
2025-06-02 22:45:09.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:45:09.391 [info] > git status -z -uall [16ms]
2025-06-02 22:45:09.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:45:14.426 [info] > git config --get commit.template [15ms]
2025-06-02 22:45:14.426 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:45:14.449 [info] > git status -z -uall [11ms]
2025-06-02 22:45:14.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:45:19.479 [info] > git config --get commit.template [10ms]
2025-06-02 22:45:19.480 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:45:19.501 [info] > git status -z -uall [9ms]
2025-06-02 22:45:19.503 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:45:24.552 [info] > git config --get commit.template [23ms]
2025-06-02 22:45:24.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:45:24.580 [info] > git status -z -uall [12ms]
2025-06-02 22:45:24.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:45:29.614 [info] > git config --get commit.template [13ms]
2025-06-02 22:45:29.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:45:29.639 [info] > git status -z -uall [9ms]
2025-06-02 22:45:29.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:45:34.670 [info] > git config --get commit.template [11ms]
2025-06-02 22:45:34.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:45:34.691 [info] > git status -z -uall [9ms]
2025-06-02 22:45:34.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:45:39.724 [info] > git config --get commit.template [12ms]
2025-06-02 22:45:39.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:45:39.747 [info] > git status -z -uall [10ms]
2025-06-02 22:45:39.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:45:44.794 [info] > git config --get commit.template [25ms]
2025-06-02 22:45:44.795 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 22:45:44.849 [info] > git status -z -uall [40ms]
2025-06-02 22:45:44.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [22ms]
2025-06-02 22:45:49.887 [info] > git config --get commit.template [17ms]
2025-06-02 22:45:49.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:45:49.915 [info] > git status -z -uall [13ms]
2025-06-02 22:45:49.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:45:54.966 [info] > git config --get commit.template [22ms]
2025-06-02 22:45:54.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 22:45:55.017 [info] > git status -z -uall [24ms]
2025-06-02 22:45:55.018 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:46:28.180 [info] > git config --get commit.template [11ms]
2025-06-02 22:46:28.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:46:28.204 [info] > git status -z -uall [11ms]
2025-06-02 22:46:28.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:46:33.238 [info] > git config --get commit.template [14ms]
2025-06-02 22:46:33.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:46:33.265 [info] > git status -z -uall [14ms]
2025-06-02 22:46:33.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:46:38.298 [info] > git config --get commit.template [13ms]
2025-06-02 22:46:38.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:46:38.339 [info] > git status -z -uall [23ms]
2025-06-02 22:46:38.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:46:43.410 [info] > git config --get commit.template [34ms]
2025-06-02 22:46:43.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:46:43.479 [info] > git status -z -uall [30ms]
2025-06-02 22:46:43.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:46:58.535 [info] > git config --get commit.template [40ms]
2025-06-02 22:46:58.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-02 22:46:58.606 [info] > git status -z -uall [31ms]
2025-06-02 22:46:58.609 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 22:47:10.095 [info] > git config --get commit.template [15ms]
2025-06-02 22:47:10.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:10.124 [info] > git status -z -uall [13ms]
2025-06-02 22:47:10.125 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:47:15.153 [info] > git config --get commit.template [11ms]
2025-06-02 22:47:15.154 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:15.181 [info] > git status -z -uall [12ms]
2025-06-02 22:47:15.182 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:47:20.214 [info] > git config --get commit.template [13ms]
2025-06-02 22:47:20.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:47:20.238 [info] > git status -z -uall [10ms]
2025-06-02 22:47:20.240 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:47:25.304 [info] > git config --get commit.template [1ms]
2025-06-02 22:47:25.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 22:47:25.350 [info] > git status -z -uall [10ms]
2025-06-02 22:47:25.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:47:32.984 [info] > git config --get commit.template [14ms]
2025-06-02 22:47:32.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:33.010 [info] > git status -z -uall [11ms]
2025-06-02 22:47:33.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:47:38.043 [info] > git config --get commit.template [13ms]
2025-06-02 22:47:38.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:38.064 [info] > git status -z -uall [11ms]
2025-06-02 22:47:38.065 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:47:43.110 [info] > git config --get commit.template [25ms]
2025-06-02 22:47:43.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:43.148 [info] > git status -z -uall [18ms]
2025-06-02 22:47:43.149 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:47:48.203 [info] > git config --get commit.template [32ms]
2025-06-02 22:47:48.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:48.246 [info] > git status -z -uall [19ms]
2025-06-02 22:47:48.247 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:47:53.286 [info] > git config --get commit.template [17ms]
2025-06-02 22:47:53.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:47:53.323 [info] > git status -z -uall [20ms]
2025-06-02 22:47:53.323 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:47:58.356 [info] > git config --get commit.template [13ms]
2025-06-02 22:47:58.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:47:58.399 [info] > git status -z -uall [21ms]
2025-06-02 22:47:58.400 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:48:03.437 [info] > git config --get commit.template [13ms]
2025-06-02 22:48:03.438 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:48:03.470 [info] > git status -z -uall [14ms]
2025-06-02 22:48:03.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:48:08.511 [info] > git config --get commit.template [15ms]
2025-06-02 22:48:08.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:48:08.533 [info] > git status -z -uall [10ms]
2025-06-02 22:48:08.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:48:13.565 [info] > git config --get commit.template [12ms]
2025-06-02 22:48:13.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:48:13.591 [info] > git status -z -uall [13ms]
2025-06-02 22:48:13.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:48:18.782 [info] > git config --get commit.template [2ms]
2025-06-02 22:48:18.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:48:18.915 [info] > git status -z -uall [83ms]
2025-06-02 22:48:18.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [57ms]
2025-06-02 22:48:23.964 [info] > git config --get commit.template [19ms]
2025-06-02 22:48:23.964 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:48:23.999 [info] > git status -z -uall [17ms]
2025-06-02 22:48:24.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:48:50.030 [info] > git config --get commit.template [1ms]
2025-06-02 22:48:50.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:48:50.108 [info] > git status -z -uall [29ms]
2025-06-02 22:48:50.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:48:55.162 [info] > git config --get commit.template [26ms]
2025-06-02 22:48:55.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:48:55.208 [info] > git status -z -uall [24ms]
2025-06-02 22:48:55.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:49:00.241 [info] > git config --get commit.template [11ms]
2025-06-02 22:49:00.242 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:49:00.264 [info] > git status -z -uall [10ms]
2025-06-02 22:49:00.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:49:05.306 [info] > git config --get commit.template [14ms]
2025-06-02 22:49:05.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:49:05.331 [info] > git status -z -uall [14ms]
2025-06-02 22:49:05.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:49:10.373 [info] > git config --get commit.template [16ms]
2025-06-02 22:49:10.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:49:10.412 [info] > git status -z -uall [19ms]
2025-06-02 22:49:10.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-02 22:49:15.451 [info] > git config --get commit.template [11ms]
2025-06-02 22:49:15.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:49:15.474 [info] > git status -z -uall [9ms]
2025-06-02 22:49:15.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:49:20.510 [info] > git config --get commit.template [14ms]
2025-06-02 22:49:20.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:49:20.539 [info] > git status -z -uall [14ms]
2025-06-02 22:49:20.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:49:25.581 [info] > git config --get commit.template [16ms]
2025-06-02 22:49:25.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:49:25.613 [info] > git status -z -uall [16ms]
2025-06-02 22:49:25.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:49:32.961 [info] > git config --get commit.template [19ms]
2025-06-02 22:49:32.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:49:32.999 [info] > git status -z -uall [18ms]
2025-06-02 22:49:32.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 22:49:38.032 [info] > git config --get commit.template [9ms]
2025-06-02 22:49:38.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:49:38.090 [info] > git status -z -uall [19ms]
2025-06-02 22:49:38.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:49:44.240 [info] > git config --get commit.template [135ms]
2025-06-02 22:49:44.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:49:44.297 [info] > git status -z -uall [19ms]
2025-06-02 22:49:44.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 22:49:49.989 [info] > git config --get commit.template [23ms]
2025-06-02 22:49:49.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:49:50.027 [info] > git status -z -uall [20ms]
2025-06-02 22:49:50.028 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:50:02.408 [info] > git config --get commit.template [22ms]
2025-06-02 22:50:02.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:50:02.455 [info] > git status -z -uall [21ms]
2025-06-02 22:50:02.456 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 22:50:15.855 [info] > git config --get commit.template [13ms]
2025-06-02 22:50:15.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:50:15.880 [info] > git status -z -uall [11ms]
2025-06-02 22:50:15.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:51:48.678 [info] > git config --get commit.template [2ms]
2025-06-02 22:51:48.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:51:48.722 [info] > git status -z -uall [11ms]
2025-06-02 22:51:48.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:51:53.762 [info] > git config --get commit.template [16ms]
2025-06-02 22:51:53.763 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:51:53.784 [info] > git status -z -uall [10ms]
2025-06-02 22:51:53.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:52:28.622 [info] > git config --get commit.template [9ms]
2025-06-02 22:52:28.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:52:28.647 [info] > git status -z -uall [11ms]
2025-06-02 22:52:28.648 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:53:26.489 [info] > git config --get commit.template [13ms]
2025-06-02 22:53:26.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:53:26.513 [info] > git status -z -uall [11ms]
2025-06-02 22:53:26.514 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 22:53:31.553 [info] > git config --get commit.template [18ms]
2025-06-02 22:53:31.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:53:31.596 [info] > git status -z -uall [23ms]
2025-06-02 22:53:31.600 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 22:56:00.197 [info] > git config --get commit.template [12ms]
2025-06-02 22:56:00.198 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 22:56:00.222 [info] > git status -z -uall [11ms]
2025-06-02 22:56:00.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 22:57:08.892 [info] > git config --get commit.template [90ms]
2025-06-02 22:57:08.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [72ms]
2025-06-02 22:57:08.953 [info] > git status -z -uall [34ms]
2025-06-02 22:57:08.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 22:59:59.177 [info] > git config --get commit.template [12ms]
2025-06-02 22:59:59.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 22:59:59.204 [info] > git status -z -uall [13ms]
2025-06-02 22:59:59.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:02:50.054 [info] > git config --get commit.template [14ms]
2025-06-02 23:02:50.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:02:50.076 [info] > git status -z -uall [10ms]
2025-06-02 23:02:50.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:02:55.139 [info] > git config --get commit.template [12ms]
2025-06-02 23:02:55.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 23:02:55.200 [info] > git status -z -uall [13ms]
2025-06-02 23:02:55.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:03:37.045 [info] > git config --get commit.template [5ms]
2025-06-02 23:03:37.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:03:37.084 [info] > git status -z -uall [14ms]
2025-06-02 23:03:37.085 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:03:42.146 [info] > git config --get commit.template [25ms]
2025-06-02 23:03:42.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 23:03:42.193 [info] > git status -z -uall [25ms]
2025-06-02 23:03:42.194 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:04:55.194 [info] > git config --get commit.template [42ms]
2025-06-02 23:04:55.196 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:04:55.257 [info] > git status -z -uall [27ms]
2025-06-02 23:04:55.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-06-02 23:05:00.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 23:05:00.321 [info] > git config --get commit.template [18ms]
2025-06-02 23:05:00.358 [info] > git status -z -uall [17ms]
2025-06-02 23:05:00.359 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:05:05.399 [info] > git config --get commit.template [16ms]
2025-06-02 23:05:05.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:05:05.442 [info] > git status -z -uall [22ms]
2025-06-02 23:05:05.444 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:05:18.203 [info] > git config --get commit.template [2ms]
2025-06-02 23:05:18.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:05:18.242 [info] > git status -z -uall [11ms]
2025-06-02 23:05:18.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:25:56.966 [info] > git config --get commit.template [15ms]
2025-06-02 23:25:56.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:25:57.007 [info] > git status -z -uall [19ms]
2025-06-02 23:25:57.008 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:02.042 [info] > git config --get commit.template [14ms]
2025-06-02 23:26:02.043 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:26:02.070 [info] > git status -z -uall [12ms]
2025-06-02 23:26:02.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:07.107 [info] > git config --get commit.template [17ms]
2025-06-02 23:26:07.108 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:26:07.154 [info] > git status -z -uall [26ms]
2025-06-02 23:26:07.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:26:12.192 [info] > git config --get commit.template [17ms]
2025-06-02 23:26:12.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:26:12.233 [info] > git status -z -uall [18ms]
2025-06-02 23:26:12.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:17.267 [info] > git config --get commit.template [11ms]
2025-06-02 23:26:17.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:26:17.296 [info] > git status -z -uall [12ms]
2025-06-02 23:26:17.297 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:22.331 [info] > git config --get commit.template [2ms]
2025-06-02 23:26:22.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 23:26:22.427 [info] > git status -z -uall [29ms]
2025-06-02 23:26:22.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:27.472 [info] > git config --get commit.template [22ms]
2025-06-02 23:26:27.473 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:26:27.514 [info] > git status -z -uall [23ms]
2025-06-02 23:26:27.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:26:32.566 [info] > git config --get commit.template [20ms]
2025-06-02 23:26:32.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:26:32.605 [info] > git status -z -uall [20ms]
2025-06-02 23:26:32.606 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:37.649 [info] > git config --get commit.template [17ms]
2025-06-02 23:26:37.650 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:26:37.671 [info] > git status -z -uall [10ms]
2025-06-02 23:26:37.672 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:26:42.715 [info] > git config --get commit.template [19ms]
2025-06-02 23:26:42.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:26:42.755 [info] > git status -z -uall [19ms]
2025-06-02 23:26:42.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:26:47.786 [info] > git config --get commit.template [0ms]
2025-06-02 23:26:47.818 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:26:47.868 [info] > git status -z -uall [27ms]
2025-06-02 23:26:47.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 23:26:52.947 [info] > git config --get commit.template [17ms]
2025-06-02 23:26:52.947 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:26:52.988 [info] > git status -z -uall [19ms]
2025-06-02 23:26:52.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 23:26:58.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 23:26:58.075 [info] > git config --get commit.template [48ms]
2025-06-02 23:26:58.151 [info] > git status -z -uall [37ms]
2025-06-02 23:26:58.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 23:27:03.188 [info] > git config --get commit.template [1ms]
2025-06-02 23:27:03.214 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:27:03.286 [info] > git status -z -uall [38ms]
2025-06-02 23:27:03.287 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:27:08.332 [info] > git config --get commit.template [16ms]
2025-06-02 23:27:08.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:08.365 [info] > git status -z -uall [13ms]
2025-06-02 23:27:08.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:27:13.406 [info] > git config --get commit.template [19ms]
2025-06-02 23:27:13.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:13.437 [info] > git status -z -uall [14ms]
2025-06-02 23:27:13.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:27:20.007 [info] > git config --get commit.template [18ms]
2025-06-02 23:27:20.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:27:20.086 [info] > git status -z -uall [40ms]
2025-06-02 23:27:20.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:27:25.121 [info] > git config --get commit.template [12ms]
2025-06-02 23:27:25.122 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:25.143 [info] > git status -z -uall [10ms]
2025-06-02 23:27:25.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:27:30.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:30.206 [info] > git config --get commit.template [31ms]
2025-06-02 23:27:30.270 [info] > git status -z -uall [26ms]
2025-06-02 23:27:30.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:27:35.304 [info] > git config --get commit.template [13ms]
2025-06-02 23:27:35.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:35.334 [info] > git status -z -uall [13ms]
2025-06-02 23:27:35.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:27:40.377 [info] > git config --get commit.template [21ms]
2025-06-02 23:27:40.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:40.417 [info] > git status -z -uall [21ms]
2025-06-02 23:27:40.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:27:45.455 [info] > git config --get commit.template [13ms]
2025-06-02 23:27:45.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:45.484 [info] > git status -z -uall [15ms]
2025-06-02 23:27:45.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:27:50.518 [info] > git config --get commit.template [12ms]
2025-06-02 23:27:50.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:27:50.545 [info] > git status -z -uall [13ms]
2025-06-02 23:27:50.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:27:55.614 [info] > git config --get commit.template [39ms]
2025-06-02 23:27:55.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 23:27:55.668 [info] > git status -z -uall [24ms]
2025-06-02 23:27:55.671 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 23:28:00.703 [info] > git config --get commit.template [11ms]
2025-06-02 23:28:00.703 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:00.732 [info] > git status -z -uall [14ms]
2025-06-02 23:28:00.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:05.766 [info] > git config --get commit.template [12ms]
2025-06-02 23:28:05.767 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:05.789 [info] > git status -z -uall [12ms]
2025-06-02 23:28:05.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:10.822 [info] > git config --get commit.template [1ms]
2025-06-02 23:28:10.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:10.943 [info] > git status -z -uall [76ms]
2025-06-02 23:28:10.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [60ms]
2025-06-02 23:28:15.980 [info] > git config --get commit.template [12ms]
2025-06-02 23:28:15.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:16.003 [info] > git status -z -uall [11ms]
2025-06-02 23:28:16.004 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:21.029 [info] > git config --get commit.template [1ms]
2025-06-02 23:28:21.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:21.089 [info] > git status -z -uall [17ms]
2025-06-02 23:28:21.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:28:26.126 [info] > git config --get commit.template [15ms]
2025-06-02 23:28:26.127 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:26.151 [info] > git status -z -uall [12ms]
2025-06-02 23:28:26.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:31.186 [info] > git config --get commit.template [16ms]
2025-06-02 23:28:31.187 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:28:31.213 [info] > git status -z -uall [12ms]
2025-06-02 23:28:31.214 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:28:36.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:28:36.257 [info] > git config --get commit.template [17ms]
2025-06-02 23:28:36.287 [info] > git status -z -uall [15ms]
2025-06-02 23:28:36.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:41.328 [info] > git config --get commit.template [13ms]
2025-06-02 23:28:41.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:28:41.352 [info] > git status -z -uall [13ms]
2025-06-02 23:28:41.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:46.385 [info] > git config --get commit.template [11ms]
2025-06-02 23:28:46.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:28:46.411 [info] > git status -z -uall [11ms]
2025-06-02 23:28:46.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:28:51.446 [info] > git config --get commit.template [12ms]
2025-06-02 23:28:51.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:28:51.467 [info] > git status -z -uall [9ms]
2025-06-02 23:28:51.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:28:56.499 [info] > git config --get commit.template [10ms]
2025-06-02 23:28:56.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:28:56.526 [info] > git status -z -uall [12ms]
2025-06-02 23:28:56.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:29:01.559 [info] > git config --get commit.template [12ms]
2025-06-02 23:29:01.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:29:01.578 [info] > git status -z -uall [9ms]
2025-06-02 23:29:01.579 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:29:06.609 [info] > git config --get commit.template [10ms]
2025-06-02 23:29:06.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:29:06.636 [info] > git status -z -uall [13ms]
2025-06-02 23:29:06.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 23:29:11.690 [info] > git config --get commit.template [20ms]
2025-06-02 23:29:11.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:29:11.722 [info] > git status -z -uall [14ms]
2025-06-02 23:29:11.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:29:16.755 [info] > git config --get commit.template [13ms]
2025-06-02 23:29:16.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:29:16.782 [info] > git status -z -uall [13ms]
2025-06-02 23:29:16.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:29:21.815 [info] > git config --get commit.template [14ms]
2025-06-02 23:29:21.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:29:21.842 [info] > git status -z -uall [12ms]
2025-06-02 23:29:21.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:29:26.875 [info] > git config --get commit.template [10ms]
2025-06-02 23:29:26.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:29:26.902 [info] > git status -z -uall [12ms]
2025-06-02 23:29:26.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:37:22.312 [info] > git config --get commit.template [16ms]
2025-06-02 23:37:22.313 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:37:22.338 [info] > git status -z -uall [10ms]
2025-06-02 23:37:22.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:46:15.757 [info] > git config --get commit.template [11ms]
2025-06-02 23:46:15.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:46:15.786 [info] > git status -z -uall [12ms]
2025-06-02 23:46:15.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:46:20.814 [info] > git config --get commit.template [9ms]
2025-06-02 23:46:20.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:46:20.846 [info] > git status -z -uall [15ms]
2025-06-02 23:46:20.847 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:46:25.891 [info] > git config --get commit.template [20ms]
2025-06-02 23:46:25.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:46:25.929 [info] > git status -z -uall [18ms]
2025-06-02 23:46:25.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:46:30.967 [info] > git config --get commit.template [16ms]
2025-06-02 23:46:30.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:46:30.996 [info] > git status -z -uall [12ms]
2025-06-02 23:46:30.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:46:36.029 [info] > git config --get commit.template [11ms]
2025-06-02 23:46:36.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:46:36.062 [info] > git status -z -uall [12ms]
2025-06-02 23:46:36.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:46:41.114 [info] > git config --get commit.template [20ms]
2025-06-02 23:46:41.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 23:46:41.162 [info] > git status -z -uall [28ms]
2025-06-02 23:46:41.163 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:46:46.379 [info] > git config --get commit.template [170ms]
2025-06-02 23:46:46.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [144ms]
2025-06-02 23:46:46.418 [info] > git status -z -uall [17ms]
2025-06-02 23:46:46.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 23:46:51.451 [info] > git config --get commit.template [12ms]
2025-06-02 23:46:51.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:46:51.484 [info] > git status -z -uall [18ms]
2025-06-02 23:46:51.485 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 23:46:56.509 [info] > git config --get commit.template [1ms]
2025-06-02 23:46:56.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:46:56.573 [info] > git status -z -uall [19ms]
2025-06-02 23:46:56.574 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:47:01.600 [info] > git config --get commit.template [2ms]
2025-06-02 23:47:01.621 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:47:01.657 [info] > git status -z -uall [17ms]
2025-06-02 23:47:01.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:47:06.686 [info] > git config --get commit.template [1ms]
2025-06-02 23:47:06.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:47:06.747 [info] > git status -z -uall [22ms]
2025-06-02 23:47:06.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:47:11.779 [info] > git config --get commit.template [1ms]
2025-06-02 23:47:11.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:47:11.854 [info] > git status -z -uall [31ms]
2025-06-02 23:47:11.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 23:48:29.959 [info] > git config --get commit.template [2ms]
2025-06-02 23:48:29.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:48:30.024 [info] > git status -z -uall [24ms]
2025-06-02 23:48:30.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:48:35.891 [info] > git config --get commit.template [16ms]
2025-06-02 23:48:35.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 23:48:35.968 [info] > git status -z -uall [22ms]
2025-06-02 23:48:35.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 23:50:14.208 [info] > git config --get commit.template [1ms]
2025-06-02 23:50:14.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:50:14.276 [info] > git status -z -uall [18ms]
2025-06-02 23:50:14.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:56:32.419 [info] > git config --get commit.template [38ms]
2025-06-02 23:56:32.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 23:56:32.498 [info] > git status -z -uall [39ms]
2025-06-02 23:56:32.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
