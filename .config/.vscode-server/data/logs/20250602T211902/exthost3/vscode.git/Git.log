2025-06-02 22:55:45.967 [info] [main] Log level: Info
2025-06-02 22:55:45.967 [info] [main] Validating found git in: "git"
2025-06-02 22:55:45.967 [info] [main] Using git "2.47.2" from "git"
2025-06-02 22:55:45.967 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 22:55:45.967 [info] > git rev-parse --show-toplevel [33ms]
2025-06-02 22:55:45.967 [info] > git rev-parse --git-dir --git-common-dir [134ms]
2025-06-02 22:55:45.967 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 22:55:45.967 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 22:55:46.003 [info] > git config --get commit.template [17ms]
2025-06-02 22:55:46.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [27ms]
2025-06-02 22:55:46.331 [info] > git status -z -uall [107ms]
2025-06-02 22:55:46.332 [info] > git fetch [371ms]
2025-06-02 22:55:46.332 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 22:55:46.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [100ms] (cancelled)
2025-06-02 22:55:46.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [111ms]
2025-06-02 22:55:46.470 [info] > git rev-parse --show-toplevel [233ms]
2025-06-02 22:55:46.493 [info] > git config --get --local branch.main.vscode-merge-base [23ms]
2025-06-02 22:55:46.501 [info] > git config --get commit.template [132ms]
2025-06-02 22:55:46.501 [info] > git config --get commit.template [138ms]
2025-06-02 22:55:46.674 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [174ms]
2025-06-02 22:55:46.683 [info] > git rev-parse --show-toplevel [204ms]
2025-06-02 22:55:46.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-02 22:55:46.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-06-02 22:55:46.720 [info] > git merge-base refs/heads/main refs/remotes/origin/main [37ms]
2025-06-02 22:55:46.756 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [18ms]
2025-06-02 22:55:46.757 [info] > git rev-parse --show-toplevel [47ms]
2025-06-02 22:55:46.818 [info] > git status -z -uall [8ms]
2025-06-02 22:55:46.818 [info] > git rev-parse --show-toplevel [50ms]
2025-06-02 22:55:46.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [166ms]
2025-06-02 22:55:47.294 [info] > git rev-parse --show-toplevel [315ms]
2025-06-02 22:55:47.424 [info] > git rev-parse --show-toplevel [110ms]
2025-06-02 22:55:47.478 [info] > git rev-parse --show-toplevel [19ms]
2025-06-02 22:55:47.530 [info] > git rev-parse --show-toplevel [29ms]
2025-06-02 22:55:47.550 [info] > git rev-parse --show-toplevel [8ms]
2025-06-02 22:55:47.566 [info] > git rev-parse --show-toplevel [7ms]
2025-06-02 22:55:47.589 [info] > git rev-parse --show-toplevel [2ms]
2025-06-02 22:55:47.599 [info] > git rev-parse --show-toplevel [4ms]
2025-06-02 22:55:47.616 [info] > git rev-parse --show-toplevel [4ms]
2025-06-02 22:55:47.630 [info] > git rev-parse --show-toplevel [5ms]
2025-06-02 22:55:47.632 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 22:55:47.683 [info] > git config --get commit.template [4ms]
2025-06-02 22:55:47.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-02 22:55:47.837 [info] > git status -z -uall [111ms]
2025-06-02 22:55:47.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [191ms]
2025-06-02 22:55:48.433 [info] > git config --get --local branch.main.github-pr-owner-number [230ms]
2025-06-02 22:55:48.434 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 22:55:57.341 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 22:58:08.553 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [7ms]
2025-06-02 22:58:08.553 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 22:58:08.568 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [5ms]
2025-06-02 23:00:08.555 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:00:08.555 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:00:08.579 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:00:37.377 [info] > git check-ignore -v -z --stdin [27ms]
2025-06-02 23:01:27.456 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-02 23:01:42.484 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 23:01:52.780 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 23:02:04.983 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 23:02:08.553 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-02 23:02:08.553 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:02:08.565 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [2ms]
2025-06-02 23:02:22.422 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 23:02:30.007 [info] > git check-ignore -v -z --stdin [34ms]
2025-06-02 23:03:19.790 [info] > git config --get commit.template [8ms]
2025-06-02 23:03:19.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:03:19.809 [info] > git status -z -uall [9ms]
2025-06-02 23:03:19.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:03:22.568 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 23:03:24.835 [info] > git config --get commit.template [2ms]
2025-06-02 23:03:24.852 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 23:03:24.885 [info] > git status -z -uall [15ms]
2025-06-02 23:03:24.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 23:03:29.019 [info] > git show --textconv :client/src/lib/storage.ts [12ms]
2025-06-02 23:03:29.020 [info] > git ls-files --stage -- client/src/lib/storage.ts [2ms]
2025-06-02 23:03:29.033 [info] > git cat-file -s ca5de833baf8f04ec6c68ff8eb43788a3ef3714b [1ms]
2025-06-02 23:03:29.235 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/lib/storage.ts [3ms]
2025-06-02 23:04:08.559 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:04:08.559 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:04:08.575 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:04:23.326 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 23:05:30.139 [info] > git config --get commit.template [5ms]
2025-06-02 23:05:30.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 23:05:30.189 [info] > git status -z -uall [15ms]
2025-06-02 23:05:30.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 23:05:41.142 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 23:06:08.558 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-02 23:06:08.558 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:06:08.574 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:06:10.141 [info] > git config --get commit.template [13ms]
2025-06-02 23:06:10.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-02 23:06:10.188 [info] > git status -z -uall [17ms]
2025-06-02 23:06:10.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:06:15.213 [info] > git config --get commit.template [3ms]
2025-06-02 23:06:15.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 23:06:15.262 [info] > git status -z -uall [12ms]
2025-06-02 23:06:15.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 23:08:08.625 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [7ms]
2025-06-02 23:08:08.625 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:08:08.662 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [9ms]
2025-06-02 23:09:28.976 [info] > git check-ignore -v -z --stdin [295ms]
2025-06-02 23:09:44.091 [info] > git check-ignore -v -z --stdin [60ms]
2025-06-02 23:09:59.098 [info] > git check-ignore -v -z --stdin [98ms]
2025-06-02 23:10:08.677 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [57ms]
2025-06-02 23:10:08.677 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:10:08.728 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
2025-06-02 23:10:10.132 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 23:12:08.617 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:12:08.617 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:12:08.633 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:14:08.613 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:14:08.613 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:14:08.626 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [2ms]
2025-06-02 23:16:08.615 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-02 23:16:08.616 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:16:08.632 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:18:08.618 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:18:08.618 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:18:08.654 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:20:08.614 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-02 23:20:08.614 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:20:08.632 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [2ms]
2025-06-02 23:22:08.618 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-02 23:22:08.619 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:22:08.645 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
2025-06-02 23:24:08.621 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:24:08.621 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:24:08.649 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
2025-06-02 23:26:08.617 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [1ms]
2025-06-02 23:26:08.617 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:26:08.645 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
2025-06-02 23:28:08.618 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:28:08.618 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:28:08.636 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:30:08.618 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:30:08.618 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:30:08.646 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:32:08.616 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:32:08.616 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:32:08.632 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:34:08.620 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:34:08.620 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:34:08.642 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:36:08.619 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:36:08.619 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:36:08.638 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [2ms]
2025-06-02 23:38:08.621 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:38:08.621 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:38:08.649 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [4ms]
2025-06-02 23:40:08.622 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [2ms]
2025-06-02 23:40:08.623 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:40:08.648 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
2025-06-02 23:42:08.627 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [3ms]
2025-06-02 23:42:08.627 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:42:08.660 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [2ms]
2025-06-02 23:44:08.706 [info] > git diff --name-status -z --diff-filter=ADMR 84b9e81d6e847f2506f8a35f2c8325979eeca42c [72ms]
2025-06-02 23:44:08.706 [info] fatal: bad object 84b9e81d6e847f2506f8a35f2c8325979eeca42c
2025-06-02 23:44:08.743 [info] > git diff --name-status -z --diff-filter=ADMR @{upstream} [3ms]
